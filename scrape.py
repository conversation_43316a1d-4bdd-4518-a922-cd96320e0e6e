import requests
from bs4 import BeautifulSoup
import time
import streamlit as st
from datetime import datetime
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from database import get_session
from models import CompetitorScrape
import re
import random
from urllib.parse import urlparse

# Realistic user-agent strings for rotation
USER_AGENTS = [
    # Chrome on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    
    # Firefox on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
    
    # Chrome on macOS
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    
    # Safari on macOS
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    
    # Edge on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
]

# Free proxy pool for IP rotation (updated regularly)
PROXY_POOL = [
    # Public HTTP proxies - these need to be validated before use
    '***************:7492',
    '***************:7300',
    '**************:8382',
    '**************:6286',
    '*************:8279',
    '*************:6100',
    '*************:8133',
    '*************:6893',
    '***********:8110',
    '************:6050',
    '*************:8133',
    '**************:5678',
    '*************:1080',
    '**************:8080',
    '**************:5566',
    '*************:8888',
    '************:39078',
    '*************:37497',
    '************:3128',
    '**************:23500'
]

# Working proxy cache
_working_proxies = []
_proxy_last_validated = None

def validate_proxy(proxy):
    """Validate if a proxy is working by testing it with a simple request."""
    try:
        proxies = {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }
        
        # Test with a lightweight endpoint
        response = requests.get(
            'http://httpbin.org/ip', 
            proxies=proxies, 
            timeout=10,
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )
        
        if response.status_code == 200:
            return True
    except:
        pass
    return False

def get_working_proxies():
    """Get a list of validated working proxies."""
    global _working_proxies, _proxy_last_validated
    
    # Re-validate proxies every 30 minutes
    current_time = time.time()
    if _proxy_last_validated and (current_time - _proxy_last_validated) < 1800:
        return _working_proxies
    
    st.info("🔄 Validating proxy pool for IP rotation...")
    _working_proxies = []
    
    # Test proxies in parallel for faster validation
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        proxy_results = {executor.submit(validate_proxy, proxy): proxy for proxy in PROXY_POOL}
        
        for future in concurrent.futures.as_completed(proxy_results):
            proxy = proxy_results[future]
            try:
                if future.result():
                    _working_proxies.append(proxy)
            except:
                pass
    
    _proxy_last_validated = current_time
    
    if _working_proxies:
        st.success(f"✅ Found {len(_working_proxies)} working proxies for rotation")
    else:
        st.warning("⚠️ No working proxies found, will use direct connection")
    
    return _working_proxies

def get_random_proxy():
    """Get a random working proxy."""
    working_proxies = get_working_proxies()
    if working_proxies:
        return random.choice(working_proxies)
    return None

def get_random_headers():
    """Get random headers with user-agent rotation for anti-bot protection."""
    user_agent = random.choice(USER_AGENTS)
    return {
        'User-Agent': user_agent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }

def get_page_content(url: str, headers=None, use_proxy=False, max_retries=3) -> BeautifulSoup:
    """
    Fetch and parse page content using BeautifulSoup with intelligent retry logic.
    Returns BeautifulSoup object or None if failed.
    """
    if headers is None:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    for attempt in range(max_retries + 1):
        # Set up proxy if requested
        proxies = None
        if use_proxy:
            proxy = get_random_proxy()
            if proxy:
                proxies = {
                    'http': f'http://{proxy}',
                    'https': f'http://{proxy}'
                }
                if attempt == 0:
                    st.write(f"🌐 Using proxy: {proxy}")
                else:
                    st.write(f"🔄 Retry {attempt} with new proxy: {proxy}")
        
        try:
            response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            return soup
            
        except requests.exceptions.HTTPError as e:
            if response.status_code == 429:  # Too Many Requests
                if attempt < max_retries:
                    # Progressive delay: 5s, 10s, 15s
                    delay = 5 + (attempt * 5)
                    st.warning(f"Rate limited (429). Waiting {delay}s before retry {attempt + 1}...")
                    time.sleep(delay)
                    # Get fresh headers for retry
                    headers = get_random_headers()
                    continue
                else:
                    st.error(f"Rate limit exceeded after {max_retries} retries: {url}")
                    return None
            elif response.status_code == 404:  # Product not found/discontinued
                st.warning(f"Product discontinued (404): {url}")
                # Return special marker for discontinued products
                soup = BeautifulSoup('<html><body class="discontinued-product"></body></html>', 'html.parser')
                return soup
            else:
                raise e
                
        except Exception as e:
            # If proxy failed, try direct connection as fallback
            if proxies and attempt < max_retries:
                st.warning(f"Proxy failed, trying with different proxy...")
                time.sleep(2)
                continue
            elif attempt < max_retries:
                delay = 3 + (attempt * 2)
                st.warning(f"Request failed, retrying in {delay}s...")
                time.sleep(delay)
                continue
            else:
                st.error(f"Error fetching {url} after {max_retries} retries: {str(e)}")
                return None
    
    return None

def scrape_thomann_product_enhanced_retry(url: str) -> dict:
    """
    Enhanced Thomann scraping with aggressive retry logic for rate-limited requests.
    Uses multiple proxies, extended delays, and fresh headers for each attempt.
    """
    max_attempts = 5
    base_delay = 10
    
    for attempt in range(max_attempts):
        try:
            # Progressive delay: 10s, 15s, 20s, 25s, 30s
            if attempt > 0:
                delay = base_delay + (attempt * 5)
                st.write(f"🕒 Extended retry delay: {delay}s (attempt {attempt + 1}/{max_attempts})")
                time.sleep(delay)
            
            # Get completely fresh headers and proxy for each attempt
            headers = get_random_headers()
            soup = get_page_content(url, headers, use_proxy=True, max_retries=1)
            
            if not soup:
                if attempt < max_attempts - 1:
                    st.warning(f"Failed to fetch page, attempting retry {attempt + 2}/{max_attempts}")
                    continue
                else:
                    return {
                        'price': 0.0,
                        'stock': 'Error',
                        'scraped_at': datetime.now(),
                        'success': False,
                        'error': 'Failed to fetch page after all retries'
                    }
            
            # Same parsing logic as main function
            result = {
                'price': 0.0,
                'stock': 'Unknown',
                'scraped_at': datetime.now(),
                'success': True
            }
            
            # Extract price
            price_selector = "#main > div:nth-child(1) > div > div.fx-container.fx-container--with-margin.fx-product-orderable.product-main-content.fx-content-product-grid__col > div > div.fx-grid__col.fx-col--12.fx-col--lg-4 > div > div.product-price-box > div.fx-content-product__sidebar.price-and-availability > div.fx-summary-label > div > div"
            price_element = soup.select_one(price_selector)
            
            if price_element:
                price_text = price_element.get_text(strip=True)
                price_match = re.search(r'(\d{1,3}(?:[\s.,]\d{3})*(?:[\s.,]\d{1,2})?)', price_text)
                if price_match:
                    price_str = price_match.group(1)
                    price_clean = price_str.replace(' ', '').replace('\xa0', '').replace('\u00a0', '')
                    
                    if ',' in price_clean and '.' not in price_clean:
                        price_clean = price_clean.replace(',', '')
                    elif '.' in price_clean and ',' not in price_clean:
                        if len(price_clean.split('.')[-1]) >= 3:
                            price_clean = price_clean.replace('.', '')
                    
                    try:
                        result['price'] = float(price_clean)
                        st.success(f"✅ Enhanced retry successful: {result['price']} Ft")
                    except ValueError:
                        pass
            
            # Extract stock
            stock_selectors = [
                ".fx-content-product__sidebar .js-fx-tooltip span",
                ".product-availability",
                "[class*='stock']"
            ]
            
            for selector in stock_selectors:
                stock_element = soup.select_one(selector)
                if stock_element:
                    result['stock'] = stock_element.get_text(strip=True)
                    break
            
            return result
            
        except Exception as e:
            if attempt < max_attempts - 1:
                st.warning(f"Attempt {attempt + 1} failed: {str(e)}, retrying...")
                continue
            else:
                return {
                    'price': 0.0,
                    'stock': 'Error',
                    'scraped_at': datetime.now(),
                    'success': False,
                    'error': str(e)
                }
    
    return {
        'price': 0.0,
        'stock': 'Error', 
        'scraped_at': datetime.now(),
        'success': False,
        'error': 'All retry attempts exhausted'
    }

def scrape_thomann_product(url: str, use_proxy_rotation: bool = True) -> dict:
    """
    Scrape price and stock information from Thomann product page.
    Uses specific CSS selectors for Thomann with optional proxy rotation.
    """
    try:
        if use_proxy_rotation:
            # Use random headers and proxy for enhanced anti-bot protection
            headers = get_random_headers()
            soup = get_page_content(url, headers, use_proxy=True)
        else:
            # Fast mode: use standard headers and direct connection
            soup = get_page_content(url, use_proxy=False)
        if not soup:
            return {
                'price': 0.0,
                'stock': 'Error',
                'scraped_at': datetime.now(),
                'success': False,
                'error': 'Failed to fetch page'
            }
        
        # Check if product is discontinued (404)
        if soup.select_one('body.discontinued-product'):
            return {
                'price': 0.0,
                'stock': 'Discontinued',
                'scraped_at': datetime.now(),
                'success': True,
                'error': 'Product discontinued'
            }
        
        result = {
            'price': 0.0,
            'stock': 'Unknown',
            'scraped_at': datetime.now(),
            'success': True
        }
        
        # Extract price using CSS selector
        price_selector = "#main > div:nth-child(1) > div > div.fx-container.fx-container--with-margin.fx-product-orderable.product-main-content.fx-content-product-grid__col > div > div.fx-grid__col.fx-col--12.fx-col--lg-4 > div > div.product-price-box > div.fx-content-product__sidebar.price-and-availability > div.fx-summary-label > div > div"
        price_element = soup.select_one(price_selector)
        
        if price_element:
            price_text = price_element.get_text(strip=True)
            st.write(f"Debug - Thomann price text: '{price_text}'")
            
            # Extract all numbers from the text, including thousand separators
            # Look for patterns like "181 300 Ft" or "181,300 Ft" or "181.300 Ft"
            price_match = re.search(r'(\d{1,3}(?:[\s.,]\d{3})*(?:[\s.,]\d{1,2})?)', price_text)
            if price_match:
                price_str = price_match.group(1)
                st.write(f"Debug - Extracted price string: '{price_str}'")
                
                # Clean the price string - remove all spaces (including non-breaking spaces) and handle separators
                # For HUF prices like "181 300" or "181,300" or "181.300"
                price_clean = price_str.replace(' ', '').replace('\xa0', '').replace('\u00a0', '')
                st.write(f"Debug - After removing spaces: '{price_clean}'")
                
                # Handle different separator formats
                if ',' in price_clean and '.' not in price_clean:
                    # Format: 181,300 - comma as thousand separator
                    price_clean = price_clean.replace(',', '')
                elif '.' in price_clean and ',' not in price_clean:
                    # Could be 181.300 (thousand sep) or 181.30 (decimal)
                    parts = price_clean.split('.')
                    if len(parts) == 2 and len(parts[1]) == 3:
                        # Thousand separator: 181.300
                        price_clean = price_clean.replace('.', '')
                    # else keep as is for decimal
                elif ',' in price_clean and '.' in price_clean:
                    # Format: 1.181,30 - dot as thousand, comma as decimal
                    price_clean = price_clean.replace('.', '').replace(',', '.')
                
                st.write(f"Debug - Final price string: '{price_clean}'")
                
                # Convert to float
                try:
                    result['price'] = float(price_clean)
                    st.write(f"Debug - Parsed HUF price: {result['price']}")
                except ValueError as e:
                    st.write(f"Debug - Could not convert to float: '{price_clean}', Error: {e}")
        
        # Try multiple CSS selectors for stock
        stock_selectors = [
            "#main > div:nth-child(1) > div > div.fx-container.fx-container--with-margin.fx-product-orderable.product-main-content.fx-content-product-grid__col > div > div.fx-grid__col.fx-col--12.fx-col--lg-4 > div > div.product-price-box > div.fx-content-product__sidebar.price-and-availability > div.js-fx-tooltip.price-and-availability__tooltip-wrapper.fx-text.fx-text--no-margin > div > span > span > span",
            ".fx-content-product__sidebar .js-fx-tooltip span",
            ".price-and-availability span",
            "[data-stock]",
            ".stock-status",
            ".availability"
        ]
        
        stock_element = None
        found_stock_selector = None
        
        for selector in stock_selectors:
            stock_element = soup.select_one(selector)
            if stock_element:
                found_stock_selector = selector
                break
                
        st.write(f"Debug - Thomann stock element found: {stock_element is not None}")
        if found_stock_selector:
            st.write(f"Debug - Found with stock selector: {found_stock_selector}")
        
        # If no stock element found, search for stock patterns in the page text
        if not stock_element:
            st.write("Debug - Searching for stock patterns in Thomann page text...")
            page_text = soup.get_text()
            # Look for common Hungarian stock terms
            stock_patterns = [
                r'Azonnal szállítható',
                r'Készleten',
                r'Raktáron',
                r'Nem elérhető',
                r'Elfogyott',
                r'Korlátozott készlet'
            ]
            
            for pattern in stock_patterns:
                if re.search(pattern, page_text, re.IGNORECASE):
                    st.write(f"Debug - Found stock pattern: {pattern}")
                    result['stock'] = pattern
                    break
        
        if stock_element:
            stock_text = stock_element.get_text(strip=True)
            st.write(f"Debug - Thomann stock text: '{stock_text}'")
            # Store the raw stock text without translation
            result['stock'] = stock_text
        
        return result
        
    except Exception as e:
        return {
            'price': 0.0,
            'stock': 'Error',
            'scraped_at': datetime.now(),
            'success': False,
            'error': str(e)
        }

def scrape_arukereso_product(url: str) -> dict:
    """
    Scrape competitor prices and stock information from Árukereső product page.
    Extracts data for multiple competitors from a single price comparison page.
    
    Args:
        url: Árukereső product page URL
        
    Returns:
        Dictionary with competitor data for all found stores
    """
    
    # Store name mapping to database field names
    STORE_MAPPING = {
        'muziker': {'price': 'muziker_price', 'stock': 'muziker_stock'},
        'r55': {'price': 'r55_price', 'stock': 'r55_stock'},
        'kytary': {'price': 'kytary_price', 'stock': 'kytary_stock'},
        'mezzoforte': {'price': 'mezzo_price', 'stock': 'mezzo_stock'},
        'mezzo': {'price': 'mezzo_price', 'stock': 'mezzo_stock'},
        'allegro': {'price': 'allegro_price', 'stock': 'allegro_stock'},
        'páko': {'price': 'pako_price', 'stock': 'pako_stock'},
        'pako': {'price': 'pako_price', 'stock': 'pako_stock'},
        'mango': {'price': 'mango_price', 'stock': 'mango_stock'},
        'hangszerplaza': {'price': 'plaza_price', 'stock': 'plaza_stock'},
        'plaza': {'price': 'plaza_price', 'stock': 'plaza_stock'},
        'diszkont': {'price': 'diszkont_price', 'stock': 'diszkont_stock'},
        'hitspace': {'price': 'hitspace_price', 'stock': 'hitspace_stock'}
    }
    
    def normalize_store_name(store_text):
        """Extract and normalize store name for mapping."""
        if not store_text:
            return None
            
        store_text = store_text.lower().strip()
        
        # Check for known store patterns
        for store_key in STORE_MAPPING.keys():
            if store_key in store_text:
                return store_key
        
        return None
    
    def extract_price(price_text):
        """Extract numeric price from Hungarian price format."""
        if not price_text:
            return 0.0
            
        # Remove 'Ft', spaces, and other non-numeric characters except digits and separators
        price_clean = re.sub(r'[^\d\s]', '', price_text.replace('Ft', ''))
        price_clean = re.sub(r'\s+', '', price_clean)  # Remove all spaces
        
        try:
            return float(price_clean) if price_clean else 0.0
        except ValueError:
            return 0.0
    
    def normalize_stock_status(stock_text):
        """Normalize Hungarian stock status to standard format."""
        if not stock_text:
            return 'Unknown'
            
        stock_text = stock_text.lower().strip()
        
        if 'raktáron' in stock_text or 'raktárban' in stock_text:
            return 'In Stock'
        elif 'ingyenes' in stock_text:
            return 'In Stock'  # Free shipping usually indicates availability
        elif 'részletek' in stock_text or 'boltban' in stock_text:
            return 'Check Store'
        elif 'szállítás' in stock_text:
            return 'Available'
        elif 'nincs' in stock_text or 'elfogyott' in stock_text:
            return 'Out of Stock'
        else:
            return 'Unknown'
    
    result = {
        'success': False,
        'competitors_found': [],
        'scraped_at': datetime.now(),
        'source_url': url,
        'error': None
    }
    
    # Initialize all competitor fields to default values
    for store_key, fields in STORE_MAPPING.items():
        result[fields['price']] = 0.0
        result[fields['stock']] = 'Unknown'
    
    try:
        # Fetch the page
        soup = get_page_content(url, use_proxy=False, max_retries=3)
        if not soup:
            result['error'] = 'Failed to fetch page content'
            return result
        
        # Scan all offer sections dynamically for all stores
        competitor_sections = [
            {'id': '#offer-block-promoted', 'name': 'Promoted offers'},
            {'id': '#offer-block-marketplace', 'name': 'Marketplace offers'},
            {'id': '#offer-block-paying', 'name': 'Paying offers'}
        ]
        
        st.write(f"Scanning {len(competitor_sections)} offer sections...")
        
        for section_info in competitor_sections:
            section_id = section_info['id']
            section_name = section_info['name']
            
            st.write(f"Processing {section_name}...")
            
            # Find the section
            section = soup.select_one(section_id)
            if not section:
                st.write(f"  Section {section_id} not found")
                continue
            
            # Look for all direct child divs that might contain offers
            offer_containers = []
            for child in section.find_all('div', recursive=True):
                child_text = child.get_text().lower()
                # Check if this div contains both store name and price - be more selective
                has_store_link = any(domain in child_text for domain in ['.hu', 'webáruház', 'hangszer', 'muziker', 'kytary', 'allegro'])
                has_price = 'ft' in child_text and any(c.isdigit() for c in child_text)
                has_reasonable_length = 50 <= len(child_text) <= 500  # Reasonable text length for an offer
                
                if has_store_link and has_price and has_reasonable_length:
                    offer_containers.append(child)
            
            st.write(f"  Found {len(offer_containers)} potential offer containers")
            
            for i, container in enumerate(offer_containers):
                try:
                    container_text = container.get_text().lower()
                    
                    # Identify store using domain names and text patterns with strict matching
                    store_name = None
                    
                    if 'muziker.hu' in container_text or 'muziker webáruház' in container_text:
                        store_name = 'muziker'
                    elif 'r55hangszerbolt.hu' in container_text or 'r55 hangszerbolt' in container_text:
                        store_name = 'r55'
                    elif 'kytary.hu' in container_text:
                        store_name = 'kytary'
                    elif 'hangszeraruhaz.hu' in container_text or 'mezzoforte hangszeráruház' in container_text:
                        store_name = 'mezzo'
                    elif 'allegro.hu' in container_text and 'allegro' in container_text:
                        store_name = 'allegro'
                    elif 'pako.hu' in container_text:
                        store_name = 'pako'
                    elif 'mangosound.hu' in container_text or 'mango sound' in container_text:
                        store_name = 'mango'
                    elif 'hangszerplaza.hu' in container_text or 'hangszerplaza webáruház' in container_text:
                        store_name = 'plaza'
                    
                    # Skip if store not identified or already found
                    if not store_name or store_name in result['competitors_found']:
                        continue
                    
                    st.write(f"    Processing {store_name}...")
                    
                    # Extract price using multiple strategies
                    price = 0.0
                    
                    # Strategy 1: Look for .row-price span or similar specific elements
                    price_elem = container.select_one('.row-price span, .col-price span, span[class*="price"]')
                    if price_elem:
                        price_text = price_elem.get_text(strip=True)
                        st.write(f"    Price element text: {price_text}")
                    else:
                        # Strategy 2: Find the main price in the container text
                        price_matches = re.findall(r'(\d+(?:\s+\d+)*)\s*Ft', container_text)
                        if price_matches:
                            # Usually the first significant price is the main price
                            for price_match in price_matches:
                                price_num = price_match.replace(' ', '').replace('\u00a0', '')
                                if len(price_num) >= 4:  # At least 4 digits (1000+ Ft)
                                    price_text = price_match + ' Ft'
                                    st.write(f"    Found price in text: {price_text}")
                                    break
                            else:
                                continue
                        else:
                            continue
                    
                    # Parse price
                    price_match = re.search(r'(\d+(?:\s+\d+)*)\s*Ft', price_text)
                    if price_match:
                        price_str = price_match.group(1).replace(' ', '').replace('\u00a0', '')
                        try:
                            price = float(price_str)
                            st.write(f"    Price extracted: {price}")
                        except ValueError:
                            continue
                    else:
                        continue
                    
                    # Extract stock status
                    stock = 'Unknown'
                    if 'raktáron' in container_text:
                        stock = 'In Stock'
                    elif 'ingyenes kiszállítás' in container_text or 'ingyenes' in container_text:
                        stock = 'In Stock'
                    elif 'részletek a boltban' in container_text or 'részletek' in container_text:
                        stock = 'Check Store'
                    elif 'szállítás:' in container_text:
                        stock = 'Available'
                    
                    # Store results
                    if store_name in STORE_MAPPING and price > 0:
                        fields = STORE_MAPPING[store_name]
                        result[fields['price']] = price
                        result[fields['stock']] = stock
                        result['competitors_found'].append(store_name)
                        
                        st.write(f"    ✓ Successfully extracted {store_name}: {price} Ft, {stock}")
                
                except Exception as container_error:
                    st.warning(f"Error processing container {i}: {str(container_error)}")
                    continue
        
        # Fallback method: Look for structured elements if text parsing didn't work
        competitor_sections = []
        if not result['competitors_found']:
            st.write("Trying fallback method with structured elements...")
            
            # Look for any elements containing store URLs or names
            store_patterns = ['muziker', 'r55', 'kytary', 'mezzoforte', 'allegro', 'páko', 'mango', 'plaza']
            for pattern in store_patterns:
                elements = soup.find_all(text=lambda text: text and pattern.lower() in text.lower())
                for element in elements:
                    parent = element.parent.find_parent(['div', 'section', 'article', 'tr', 'td'])
                    if parent and parent not in competitor_sections:
                        competitor_sections.append(parent)
            
            st.write(f"Found {len(competitor_sections)} potential competitor sections")
        
        # Process each competitor section
        for i, section in enumerate(competitor_sections):
            try:
                # Extract store name from various possible locations
                store_name = None
                
                # Try to find store name in headers, titles, alt text
                for selector in ['h3', 'h4', 'h5', '.store-name', '[title]', 'img[alt]']:
                    elements = section.select(selector)
                    for elem in elements:
                        text = elem.get_text() if hasattr(elem, 'get_text') else str(elem.get('alt', ''))
                        if text:
                            normalized = normalize_store_name(text)
                            if normalized:
                                store_name = normalized
                                break
                    if store_name:
                        break
                
                # If no store name found in structured elements, search in all text
                if not store_name:
                    section_text = section.get_text()
                    store_name = normalize_store_name(section_text)
                
                if not store_name:
                    continue
                
                # Extract price
                price = 0.0
                price_elements = section.find_all(text=lambda text: text and 'ft' in text.lower() and any(c.isdigit() for c in text))
                for price_text in price_elements:
                    extracted_price = extract_price(price_text)
                    if extracted_price > 0:
                        price = extracted_price
                        break
                
                # Extract stock status
                stock = 'Unknown'
                section_text = section.get_text().lower()
                stock = normalize_stock_status(section_text)
                
                # Map to result structure
                if store_name in STORE_MAPPING and price > 0:
                    fields = STORE_MAPPING[store_name]
                    result[fields['price']] = price
                    result[fields['stock']] = stock
                    result['competitors_found'].append(store_name)
                    
                    st.write(f"✓ Found {store_name}: {price} Ft, {stock}")
                
            except Exception as section_error:
                st.warning(f"Error processing competitor section {i}: {str(section_error)}")
                continue
        
        if result['competitors_found']:
            result['success'] = True
            st.success(f"Successfully scraped {len(result['competitors_found'])} competitors from Árukereső")
        else:
            result['error'] = 'No competitor data found on page'
            st.warning("No competitor data could be extracted from Árukereső page")
        
        return result
        
    except Exception as e:
        result['error'] = f'Scraping error: {str(e)}'
        st.error(f"Error scraping Árukereső: {str(e)}")
        return result

def scrape_muziker_product(url: str) -> dict:
    """
    Scrape price and stock information from Muziker product page.
    Uses specific CSS selectors for Muziker.
    """
    try:
        soup = get_page_content(url)
        if not soup:
            return {
                'price': 0.0,
                'stock': 'Error',
                'scraped_at': datetime.now(),
                'success': False,
                'error': 'Failed to fetch page'
            }
        
        result = {
            'price': 0.0,
            'stock': 'Unknown',
            'scraped_at': datetime.now(),
            'success': True
        }
        
        # Try multiple CSS selectors for price
        price_selectors = [
            "body > div:nth-child(1) > main > div:nth-child(6) > div:nth-child(1) > div:nth-child(2) > div:nth-child(7) > div:nth-child(4) > div:nth-child(1) > div > div > div > div:nth-child(1)",
            ".mzkr-text-dark.font-weight-bold.mzkr-mr-2",
            ".product-price-wrapper .mzkr-text-dark",
            "[data-price]",
            ".price",
            ".product-price"
        ]
        
        price_element = None
        found_selector = None
        
        for selector in price_selectors:
            price_element = soup.select_one(selector)
            if price_element:
                found_selector = selector
                break
                
        st.write(f"Debug - Muziker price element found: {price_element is not None}")
        if found_selector:
            st.write(f"Debug - Found with selector: {found_selector}")
        
        # If no price element found, try searching for common price patterns in the text
        if not price_element:
            st.write("Debug - Searching for price patterns in page text...")
            page_text = soup.get_text()
            # Look for Hungarian price patterns like "217 500 Ft" or "217.500 Ft"
            price_patterns = [
                r'(\d{1,3}(?:[\s.]\d{3})*)\s*Ft',
                r'(\d{1,3}(?:[,]\d{3})*)\s*Ft',
                r'(\d+)\s*Ft'
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    st.write(f"Debug - Found price patterns: {matches[:3]}")  # Show first 3 matches
                    break
        
        if price_element:
            price_text = price_element.get_text(strip=True)
            st.write(f"Debug - Muziker price text: '{price_text}'")
            
            # Extract price from text (format: "123 456 Ft" or "123.456 Ft")
            price_match = re.search(r'(\d{1,3}(?:[\s.,]\d{3})*(?:[\s.,]\d{1,2})?)', price_text)
            if price_match:
                price_str = price_match.group(1)
                st.write(f"Debug - Extracted price string: '{price_str}'")
                
                # Clean the price string - remove all spaces (including non-breaking spaces) and handle separators
                price_clean = price_str.replace(' ', '').replace('\xa0', '').replace('\u00a0', '')
                st.write(f"Debug - After removing spaces: '{price_clean}'")
                
                # Handle different separator formats for Muziker (Hungarian)
                if ',' in price_clean and '.' not in price_clean:
                    # Format: 123,456 - comma as thousand separator
                    price_clean = price_clean.replace(',', '')
                elif '.' in price_clean and ',' not in price_clean:
                    # Could be 123.456 (thousand sep) or 123.45 (decimal)
                    parts = price_clean.split('.')
                    if len(parts) == 2 and len(parts[1]) == 3:
                        # Thousand separator: 123.456
                        price_clean = price_clean.replace('.', '')
                    # else keep as is for decimal
                elif ',' in price_clean and '.' in price_clean:
                    # Format: 123.456,78 - dot as thousand, comma as decimal
                    price_clean = price_clean.replace('.', '').replace(',', '.')
                
                st.write(f"Debug - Final price string: '{price_clean}'")
                
                # Convert to float
                try:
                    result['price'] = float(price_clean)
                    st.write(f"Debug - Parsed Muziker HUF price: {result['price']}")
                except ValueError as e:
                    st.write(f"Debug - Could not convert to float: '{price_clean}', Error: {e}")
        
        # Extract stock using CSS selector
        stock_selector = "body > div:nth-child(5) > main > div.container.container-product > div.mzkr-row.mzkr-mb-8.mzkr-mb-lg-10.mzkr-mb-xxl-12 > div.mzkr-col-12.mzkr-col-lg-6.mzkr-col-xxl-5 > div.product-order-box.mzkr-my-4 > div.stock-status-detail.mzkr-mb-half.stock-status-orange"
        stock_element = soup.select_one(stock_selector)
        
        if stock_element:
            stock_text = stock_element.get_text(strip=True).lower()
            
            # Map Hungarian stock terms to English
            if any(term in stock_text for term in ['készleten', 'raktáron', 'elérhető']):
                result['stock'] = 'In Stock'
            elif any(term in stock_text for term in ['nincs készleten', 'elfogyott', 'nem elérhető']):
                result['stock'] = 'Out of Stock'
            elif any(term in stock_text for term in ['kevés', 'utolsó', 'korlátozott']):
                result['stock'] = 'Limited Stock'
            else:
                result['stock'] = stock_text.title()
        
        # Fallback: try alternative stock selectors
        if result['stock'] == 'Unknown':
            alternative_selectors = [
                ".stock-status-detail",
                ".product-availability",
                "[class*='stock']",
                "[class*='availability']"
            ]
            
            for selector in alternative_selectors:
                stock_element = soup.select_one(selector)
                if stock_element:
                    stock_text = stock_element.get_text(strip=True)
                    if stock_text:
                        result['stock'] = stock_text.title()
                        break
        
        return result
        
    except Exception as e:
        return {
            'price': 0.0,
            'stock': 'Error',
            'scraped_at': datetime.now(),
            'success': False,
            'error': str(e)
        }

def scrape_single_product_parallel(product_code: str) -> dict:
    """
    Scrape a single product for parallel processing.
    Returns results for both Thomann and Muziker.
    """
    session = get_session()
    if not session:
        return {
            'product_code': product_code,
            'success': False,
            'error': 'Database connection failed'
        }
    
    try:
        # Convert product code to string to handle number/text type issues
        product_code_str = str(product_code).strip()
        
        # Get competitor record
        competitor = session.query(CompetitorScrape).filter_by(
            product_code=product_code_str
        ).first()
        
        if not competitor:
            return {
                'product_code': product_code,
                'success': False,
                'error': 'Product not found in competitor database'
            }
        
        result = {
            'product_code': product_code,
            'success': True,
            'thomann': None,
            'muziker': None,
            'arukereso': None,
            'competitor_record': competitor
        }
        
        # Scrape Thomann if URL exists
        if competitor.thomann_url:
            thomann_result = scrape_thomann_product(competitor.thomann_url)
            result['thomann'] = thomann_result
            if thomann_result['success']:
                competitor.thomann_price = thomann_result['price']
                competitor.thomann_stock = thomann_result['stock']
        
        # Scrape Muziker if URL exists
        if competitor.muziker_url:
            muziker_result = scrape_muziker_product(competitor.muziker_url)
            result['muziker'] = muziker_result
            if muziker_result['success']:
                competitor.muziker_price = muziker_result['price']
                competitor.muziker_stock = muziker_result['stock']
        
        # Scrape Árukereső if URL exists (this will get multiple competitors)
        if competitor.arukereso_url:
            arukereso_result = scrape_arukereso_product(competitor.arukereso_url)
            result['arukereso'] = arukereso_result
            if arukereso_result['success']:
                # Update all competitor prices from Árukereső results
                if arukereso_result.get('r55_price', 0) > 0:
                    competitor.r55_price = arukereso_result['r55_price']
                    competitor.r55_stock = arukereso_result['r55_stock']
                if arukereso_result.get('kytary_price', 0) > 0:
                    competitor.kytary_price = arukereso_result['kytary_price']
                    competitor.kytary_stock = arukereso_result['kytary_stock']
                if arukereso_result.get('mezzo_price', 0) > 0:
                    competitor.mezzo_price = arukereso_result['mezzo_price']
                    competitor.mezzo_stock = arukereso_result['mezzo_stock']
                if arukereso_result.get('allegro_price', 0) > 0:
                    competitor.allegro_price = arukereso_result['allegro_price']
                    competitor.allegro_stock = arukereso_result['allegro_stock']
                if arukereso_result.get('pako_price', 0) > 0:
                    competitor.pako_price = arukereso_result['pako_price']
                    competitor.pako_stock = arukereso_result['pako_stock']
                if arukereso_result.get('mango_price', 0) > 0:
                    competitor.mango_price = arukereso_result['mango_price']
                    competitor.mango_stock = arukereso_result['mango_stock']
                if arukereso_result.get('plaza_price', 0) > 0:
                    competitor.plaza_price = arukereso_result['plaza_price']
                    competitor.plaza_stock = arukereso_result['plaza_stock']
                if arukereso_result.get('diszkont_price', 0) > 0:
                    competitor.diszkont_price = arukereso_result['diszkont_price']
                    competitor.diszkont_stock = arukereso_result['diszkont_stock']
                if arukereso_result.get('hitspace_price', 0) > 0:
                    competitor.hitspace_price = arukereso_result['hitspace_price']
                    competitor.hitspace_stock = arukereso_result['hitspace_stock']
                
                # Only update Muziker from Árukereső if direct scrape failed or returned no price
                if (arukereso_result.get('muziker_price', 0) > 0 and 
                    ('muziker' not in result or not result['muziker'].get('success', False) or result['muziker'].get('price', 0) == 0)):
                    competitor.muziker_price = arukereso_result['muziker_price']
                    competitor.muziker_stock = arukereso_result['muziker_stock']
                    # Update result dict to reflect the Árukereső data
                    result['muziker'] = {
                        'success': True,
                        'price': arukereso_result['muziker_price'],
                        'stock': arukereso_result['muziker_stock'],
                        'source': 'arukereso'
                    }
        
        # Update timestamp
        competitor.last_scraped = datetime.now()
        
        return result
        
    except Exception as e:
        return {
            'product_code': product_code,
            'success': False,
            'error': str(e)
        }
    finally:
        session.close()

def scrape_competitor_products(product_codes: list, progress_callback=None, message_callback=None) -> dict:
    """
    Scrape competitor prices and stock for multiple products.
    Updates the database with fresh data.
    
    Args:
        product_codes: List of product codes to scrape
        progress_callback: Optional callback function for progress updates
    
    Returns:
        Dictionary with scraping results
    """
    results = {
        'total_products': len(product_codes),
        'successful_scrapes': 0,
        'failed_scrapes': 0,
        'products': {}
    }
    
    # Determine scraping strategy based on product count
    use_proxy_rotation = len(product_codes) >= 5
    
    if use_proxy_rotation:
        st.info(f"Starting enhanced scraping with proxy rotation for {len(product_codes)} products...")
        st.write("🛡️ Enhanced Anti-Bot Protection Active:")
        st.write("• User-Agent Rotation: 11 realistic browser headers")
        st.write("• IP Rotation: Proxy validation and rotation")
        st.write("• Rate Limiting: Randomized delays (1.5-2.5s)")
    else:
        st.info(f"Starting fast scraping for {len(product_codes)} products (no proxy rotation for small batches)...")
        st.write("⚡ Fast Mode Active:")
        st.write("• Standard headers and direct connections")
        st.write("• Minimal delays for faster processing")
        st.write("• Best for 2-4 products")
    
    # Get all competitor records and keep session open for updates
    session = get_session()
    if not session:
        return {'success': False, 'error': 'Database connection failed'}
    
    try:
        # Convert all product codes to strings to handle number/text type issues
        product_codes_str = [str(code).strip() for code in product_codes]
        
        competitors = session.query(CompetitorScrape).filter(
            CompetitorScrape.product_code.in_(product_codes_str)
        ).all()
        
        competitor_dict = {comp.product_code: comp for comp in competitors}
        
        # Step 1: Scrape Muziker in parallel (they handle parallel requests well)
        muziker_products = [code for code in product_codes_str if 
                           code in competitor_dict and competitor_dict[code].muziker_url]
        
        if muziker_products:
            st.info(f"Scraping Muziker for {len(muziker_products)} products in parallel...")
            
            def scrape_muziker_only(product_code):
                competitor = competitor_dict[product_code]
                result = scrape_muziker_product(competitor.muziker_url)
                return product_code, result
            
            with ThreadPoolExecutor(max_workers=3) as executor:
                muziker_futures = {
                    executor.submit(scrape_muziker_only, code): code 
                    for code in muziker_products
                }
                
                for future in as_completed(muziker_futures):
                    product_code, muziker_result = future.result()
                    results['products'][product_code] = {'product_code': product_code}
                
                    if muziker_result['success']:
                        st.success(f"Muziker ({product_code}): {muziker_result['price']} Ft, Stock: {muziker_result['stock']}")
                        results['products'][product_code]['muziker'] = muziker_result
                        # Update database record - merge to reattach to session
                        competitor = competitor_dict[product_code]
                        competitor = session.merge(competitor)
                        competitor.muziker_price = muziker_result['price']
                        competitor.muziker_stock = muziker_result['stock']
                        competitor.last_scraped = datetime.now()
                        competitor_dict[product_code] = competitor
                    else:
                        st.error(f"Muziker error ({product_code}): {muziker_result.get('error', 'Unknown error')}")
                        results['products'][product_code]['muziker_error'] = muziker_result.get('error', 'Unknown error')
        
        # Step 2: Scrape Thomann sequentially with delays (they are sensitive to parallel requests)
        thomann_products = [code for code in product_codes_str if 
                           code in competitor_dict and competitor_dict[code].thomann_url]
        
        if thomann_products:
            st.info(f"Scraping Thomann for {len(thomann_products)} products sequentially...")
        
        for i, product_code in enumerate(thomann_products):
            if progress_callback:
                # Calculate progress correctly - don't exceed total count
                current_progress = min(len(muziker_products) + i + 1, len(product_codes))
                progress_callback(current_progress, len(product_codes), product_code)
            
            # Add delay between Thomann requests - shorter for fast mode
            if i > 0:
                if use_proxy_rotation:
                    delay = 1.5 + random.uniform(0, 1.0)  # 1.5-2.5 seconds for enhanced mode
                else:
                    delay = 0.5 + random.uniform(0, 0.5)  # 0.5-1.0 seconds for fast mode
                time.sleep(delay)
            
            competitor = competitor_dict[product_code]
            thomann_result = scrape_thomann_product(competitor.thomann_url, use_proxy_rotation)
            
            if product_code not in results['products']:
                results['products'][product_code] = {'product_code': product_code}
            
            if thomann_result['success']:
                st.success(f"Thomann ({product_code}): {thomann_result['price']} Ft, Stock: {thomann_result['stock']}")
                results['products'][product_code]['thomann'] = thomann_result
                # Update database record - merge to reattach to session
                competitor = session.merge(competitor)
                competitor.thomann_price = thomann_result['price']
                competitor.thomann_stock = thomann_result['stock']
                competitor.last_scraped = datetime.now()
                competitor_dict[product_code] = competitor
            else:
                st.error(f"Thomann error ({product_code}): {thomann_result.get('error', 'Unknown error')}")
                results['products'][product_code]['thomann_error'] = thomann_result.get('error', 'Unknown error')
        
        # Step 3: Scrape Árukereső for products with Árukereső URLs (gets multiple competitors per page)
        arukereso_products = [code for code in product_codes_str if 
                             code in competitor_dict and competitor_dict[code].arukereso_url]
        
        if arukereso_products:
            st.info(f"Scraping Árukereső for {len(arukereso_products)} products (multiple competitors per page)...")
            
            for i, product_code in enumerate(arukereso_products):
                if progress_callback:
                    current_progress = min(len(muziker_products) + len(thomann_products) + i + 1, len(product_codes))
                    progress_callback(current_progress, len(product_codes), product_code)
            
                # Add delay between Árukereső requests
                if i > 0:
                    delay = 2.0 + random.uniform(0, 1.0)  # 2-3 seconds for Árukereső
                    time.sleep(delay)
                
                competitor = competitor_dict[product_code]
                arukereso_result = scrape_arukereso_product(competitor.arukereso_url)
                
                if product_code not in results['products']:
                    results['products'][product_code] = {'product_code': product_code}
                
                if arukereso_result['success']:
                    competitors_found = arukereso_result.get('competitors_found', [])
                    st.success(f"Árukereső ({product_code}): Found {len(competitors_found)} competitors: {', '.join(competitors_found)}")
                    results['products'][product_code]['arukereso'] = arukereso_result
                    
                    # Update database record with all competitor prices - merge to reattach to session
                    competitor = session.merge(competitor)
                    if arukereso_result.get('muziker_price', 0) > 0:
                        competitor.muziker_price = arukereso_result['muziker_price']
                        competitor.muziker_stock = arukereso_result['muziker_stock']
                    if arukereso_result.get('r55_price', 0) > 0:
                        competitor.r55_price = arukereso_result['r55_price']
                        competitor.r55_stock = arukereso_result['r55_stock']
                    if arukereso_result.get('kytary_price', 0) > 0:
                        competitor.kytary_price = arukereso_result['kytary_price']
                        competitor.kytary_stock = arukereso_result['kytary_stock']
                if arukereso_result.get('mezzo_price', 0) > 0:
                    competitor.mezzo_price = arukereso_result['mezzo_price']
                    competitor.mezzo_stock = arukereso_result['mezzo_stock']
                if arukereso_result.get('allegro_price', 0) > 0:
                    competitor.allegro_price = arukereso_result['allegro_price']
                    competitor.allegro_stock = arukereso_result['allegro_stock']
                if arukereso_result.get('pako_price', 0) > 0:
                    competitor.pako_price = arukereso_result['pako_price']
                    competitor.pako_stock = arukereso_result['pako_stock']
                if arukereso_result.get('mango_price', 0) > 0:
                    competitor.mango_price = arukereso_result['mango_price']
                    competitor.mango_stock = arukereso_result['mango_stock']
                if arukereso_result.get('plaza_price', 0) > 0:
                    competitor.plaza_price = arukereso_result['plaza_price']
                    competitor.plaza_stock = arukereso_result['plaza_stock']
                if arukereso_result.get('diszkont_price', 0) > 0:
                    competitor.diszkont_price = arukereso_result['diszkont_price']
                    competitor.diszkont_stock = arukereso_result['diszkont_stock']
                if arukereso_result.get('hitspace_price', 0) > 0:
                    competitor.hitspace_price = arukereso_result['hitspace_price']
                    competitor.hitspace_stock = arukereso_result['hitspace_stock']
                
                competitor.last_scraped = datetime.now()
                competitor_dict[product_code] = competitor
            else:
                st.error(f"Árukereső error ({product_code}): {arukereso_result.get('error', 'Unknown error')}")
                results['products'][product_code]['arukereso_error'] = arukereso_result.get('error', 'Unknown error')

        # Identify failed Thomann requests for retry (exclude discontinued products)
        failed_thomann = []
        for product_code, product_data in results['products'].items():
            if 'thomann_error' in product_data and product_code in competitor_dict:
                # Don't retry discontinued products (404 errors)
                error_message = product_data['thomann_error']
                if 'discontinued' not in error_message.lower() and '404' not in error_message:
                    failed_thomann.append(product_code)
                else:
                    st.info(f"⏭️ Skipping retry for discontinued product: {product_code}")
        
        # Retry failed Thomann requests with enhanced measures
        if failed_thomann:
            st.info(f"🔄 Starting retry phase for {len(failed_thomann)} failed Thomann products")
            
            for i, product_code in enumerate(failed_thomann):
                if progress_callback:
                    # Update progress for retry phase
                    retry_progress = len(product_codes) + i + 1
                    total_with_retries = len(product_codes) + len(failed_thomann)
                    progress_callback(retry_progress, total_with_retries, f"Retry: {product_code}")
                
                competitor = competitor_dict[product_code]
                st.write(f"🔄 Enhanced retry for {product_code} with fresh proxy and headers")
                
                # Use enhanced retry with extended delays
                retry_result = scrape_thomann_product_enhanced_retry(competitor.thomann_url)
                
                if retry_result['success']:
                    # Update database with successful retry - merge to reattach to session
                    competitor = session.merge(competitor)
                    competitor.thomann_price = retry_result['price']
                    competitor.thomann_stock = retry_result['stock']
                    competitor.last_scraped = datetime.now()
                    competitor_dict[product_code] = competitor
                    
                    # Update results
                    results['products'][product_code]['thomann'] = retry_result
                    if 'thomann_error' in results['products'][product_code]:
                        del results['products'][product_code]['thomann_error']
                    
                    st.success(f"✅ Retry successful for {product_code}: {retry_result['price']} Ft")
                else:
                    st.warning(f"⚠️ Enhanced retry also failed for {product_code}")
    
        # Count successful scrapes
        for product_data in results['products'].values():
            if 'thomann' in product_data or 'muziker' in product_data:
                results['successful_scrapes'] += 1
            else:
                results['failed_scrapes'] += 1
        
        # Commit all database changes using the existing session
        session.commit()
        results['success'] = True
        
    except Exception as e:
        session.rollback()
        results['success'] = False
        results['error'] = f"Database operation failed: {str(e)}"
    finally:
        session.close()
    
    return results

def test_scraping_single_product(product_code: str) -> dict:
    """
    Test scraping functionality on a single product for debugging.
    """
    session = get_session()
    if not session:
        return {'success': False, 'error': 'Database connection failed'}
    
    try:
        competitor = session.query(CompetitorScrape).filter_by(
            product_code=product_code
        ).first()
        
        if not competitor:
            return {'success': False, 'error': 'Product not found'}
        
        results = {
            'product_code': product_code,
            'thomann': None,
            'muziker': None,
            'arukereso': None
        }
        
        # Test Thomann
        if competitor.thomann_url:
            st.info(f"Testing Thomann scraping for {product_code}")
            thomann_result = scrape_thomann_product(competitor.thomann_url)
            results['thomann'] = thomann_result
            
        # Test Muziker
        if competitor.muziker_url:
            st.info(f"Testing Muziker scraping for {product_code}")
            muziker_result = scrape_muziker_product(competitor.muziker_url)
            results['muziker'] = muziker_result
            
        # Test Árukereső
        if competitor.arukereso_url:
            st.info(f"Testing Árukereső scraping for {product_code}")
            arukereso_result = scrape_arukereso_product(competitor.arukereso_url)
            results['arukereso'] = arukereso_result
        
        return {'success': True, 'results': results}
        
    except Exception as e:
        return {'success': False, 'error': str(e)}
    finally:
        session.close()