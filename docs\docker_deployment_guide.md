# Docker Deployment Guide for ERP Analytics Dashboard

This guide explains how to build, run, and deploy the ERP Analytics Dashboard using Docker containers.

## Prerequisites

1. Docker installed on your system ([Docker Installation Guide](https://docs.docker.com/get-docker/))
2. Docker Compose installed ([Docker Compose Installation Guide](https://docs.docker.com/compose/install/))
3. Basic familiarity with Docker commands
4. The ERP Analytics Dashboard codebase

## Local Development with Docker Compose

The easiest way to run the application locally is with Docker Compose, which sets up both the application and a PostgreSQL database.

### 1. Start the Application

```bash
# From the root directory of the project
docker-compose up
```

This will:
- Build the application container if it doesn't exist
- Start a PostgreSQL database container
- Connect the application to the database
- Make the application available at http://localhost:5000

### 2. Run in Background

To run the containers in the background (detached mode):

```bash
docker-compose up -d
```

### 3. View Logs

```bash
# View logs from all services
docker-compose logs

# View logs from just the web application
docker-compose logs web

# Follow logs (stream new logs as they come in)
docker-compose logs -f
```

### 4. Stop the Application

```bash
docker-compose down
```

### 5. Reset Everything

To remove all containers and volumes (WARNING: this will delete the database data):

```bash
docker-compose down -v
```

## Building and Running the Docker Image Directly

If you want to run the application container directly (without Docker Compose), you can build and run it manually.

### 1. Build the Docker Image

```bash
# From the root directory of the project
docker build -t erp-analytics-dashboard .
```

### 2. Run the Container

```bash
docker run -p 5000:5000 \
  -e DATABASE_URL=postgresql://username:password@host:port/database \
  -e DEPLOYMENT_ENV=docker \
  erp-analytics-dashboard
```

Replace the `DATABASE_URL` with your actual database connection string.

### 3. Stop the Container

```bash
# First, find the container ID
docker ps

# Then stop it
docker stop <container_id>
```

## Deployment to Production

For production deployments, there are several options depending on your infrastructure.

### Option 1: AWS ECS (Elastic Container Service)

1. Push your image to Amazon ECR (Elastic Container Registry)
2. Create an ECS task definition
3. Configure your RDS database connection
4. Deploy the task to an ECS cluster

See [AWS ECS Deployment Guide](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/Welcome.html) for detailed steps.

### Option 2: AWS App Runner

1. Push your image to Amazon ECR
2. Create a new App Runner service pointing to your image
3. Configure environment variables for database connection

See [AWS App Runner Guide](https://docs.aws.amazon.com/apprunner/latest/dg/what-is-apprunner.html) for detailed steps.

### Option 3: Kubernetes Deployment

If you're using Kubernetes (such as Amazon EKS, Google GKE, or your own cluster):

1. Push your image to a container registry
2. Create Kubernetes deployment and service manifests
3. Deploy to your Kubernetes cluster

## Environment Variables

The Docker container supports several environment variables to configure its behavior:

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://user:pass@host:port/dbname` |
| `DEPLOYMENT_ENV` | Deployment environment | `aws` for AWS or `docker` for Docker |
| `DB_HOST` | Database hostname (AWS mode) | `mydb.xxxxxxxxxxxx.us-east-1.rds.amazonaws.com` |
| `DB_PORT` | Database port (AWS mode) | `5432` |
| `DB_NAME` | Database name (AWS mode) | `erp_analytics` |
| `DB_USER` | Database username (AWS mode) | `postgres` |
| `DB_PASSWORD` | Database password (AWS mode) | `your-password` |

## Docker Image Customization

If you need to customize the Docker image:

1. Edit the `Dockerfile` to add dependencies or change configuration
2. Rebuild the image with `docker build -t erp-analytics-dashboard .`

## Troubleshooting

### Container Exits Immediately

If the container exits immediately after starting, check the logs:

```bash
docker logs <container_id>
```

Common issues include:
- Database connection problems
- Missing environment variables
- Permission errors

### Database Connection Issues

If the application can't connect to the database:

1. Verify the database connection string is correct
2. Ensure the database is reachable from the container
3. Check firewall or security group settings
4. Verify that the database user has appropriate permissions

### Port Conflicts

If you see an error like "port 5000 already in use":

1. Stop any other services using port 5000
2. Or map the container to a different port:
   ```bash
   docker run -p 8080:5000 erp-analytics-dashboard
   ```
   This makes the application available on port 8080 locally.