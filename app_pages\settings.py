import streamlit as st
import pandas as pd
from utils import (
    get_brands,
    get_countries,
    get_custom_settings,
    save_custom_setting,
    get_all_logistics_settings_for_brands,
    save_all_logistics_settings,
    get_all_margin_settings_for_brands,
    save_all_margin_settings,
    get_all_discount_settings_for_brands,
    save_all_discount_settings
)
from database import check_db_connection, DATABASE_URL
from config import config
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode, DataReturnMode, JsCode

def show():
    """Display the Settings tab content."""
    st.header("Settings")
    
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["Logistics by Brand", "Margin by Brand", "Discount by Brand", "General Settings", "System Info"])
    
    with tab1:
        show_logistics_by_brand_settings()
    
    with tab2:
        show_margin_by_brand_settings()
        
    with tab3:
        show_discount_by_brand_settings()
        
    with tab4:
        show_general_settings()
        
    with tab5:
        show_system_info()

def show_logistics_by_brand_settings():
    """Display and manage logistics costs (customs and shipping) by brand using AgGrid table."""
    st.subheader("Logistics Settings by Brand")
    
    # Detect theme for AgGrid
    is_dark_theme = st.session_state.get('dark_theme', True)
    ag_theme = 'streamlit' if is_dark_theme else 'alpine'
    
    # Get all brands
    brands = get_brands()
    
    if not brands:
        st.warning("No brands found in the database. Please upload product data first.")
        return
    
    # Load logistics data using optimized function
    with st.spinner("Loading logistics settings..."):
        logistics_data = get_all_logistics_settings_for_brands()
    
    if not logistics_data:
        st.error("Failed to load logistics data. Please check your database connection.")
        return
    
    # Store original data in session state for change detection
    if 'original_logistics_data' not in st.session_state:
        st.session_state.original_logistics_data = logistics_data.copy()
    
    # Convert to DataFrame for AgGrid
    df_data = []
    for brand, settings in logistics_data.items():
        df_data.append({
            'Brand': brand,
            'Customs EXW %': settings['customs_exw'],
            'Shipping EXW %': settings['shipping_exw'],
            'Customs FOB %': settings['customs_fob'],
            'Shipping FOB %': settings['shipping_fob'],
            'Default Shipping': settings['default_shipping'],
            'Purchase Currency': settings['purchase_currency'],
            'Sales Currency': settings['sales_currency']
        })
    
    df = pd.DataFrame(df_data)
    
    # Configure AgGrid
    gb = GridOptionsBuilder.from_dataframe(df)
    
    # Configure column properties with center alignment and filters
    gb.configure_column("Brand", 
                       editable=False, 
                       pinned='left', 
                       width=150,
                       filter='agTextColumnFilter',
                       filterParams={'filterOptions': ['contains', 'startsWith', 'endsWith']})
    
    gb.configure_column("Customs EXW %", 
                       editable=True, 
                       type=["numericColumn", "numberColumnFilter"], 
                       cellEditor='agNumberCellEditor',
                       cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Shipping EXW %", 
                       editable=True, 
                       type=["numericColumn", "numberColumnFilter"],
                       cellEditor='agNumberCellEditor',
                       cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Customs FOB %", 
                       editable=True, 
                       type=["numericColumn", "numberColumnFilter"],
                       cellEditor='agNumberCellEditor',
                       cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Shipping FOB %", 
                       editable=True, 
                       type=["numericColumn", "numberColumnFilter"],
                       cellEditor='agNumberCellEditor',
                       cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Default Shipping", 
                       editable=True,
                       cellEditor='agSelectCellEditor',
                       cellEditorParams={'values': ['EXW', 'FOB']},
                       filter='agSetColumnFilter',
                       filterParams={'values': ['EXW', 'FOB']},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Purchase Currency", 
                       editable=True,
                       cellEditor='agSelectCellEditor',
                       cellEditorParams={'values': ['HUF', 'USD', 'EUR']},
                       filter='agSetColumnFilter',
                       filterParams={'values': ['HUF', 'USD', 'EUR']},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    gb.configure_column("Sales Currency", 
                       editable=True,
                       cellEditor='agSelectCellEditor',
                       cellEditorParams={'values': ['HUF', 'USD', 'EUR']},
                       filter='agSetColumnFilter',
                       filterParams={'values': ['HUF', 'USD', 'EUR']},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    # Configure grid options
    gb.configure_default_column(groupable=False, sortable=True, filterable=True)
    gb.configure_grid_options(enableRangeSelection=True, 
                             enableCellChangeFlash=True,
                             suppressRowDeselection=True,
                             animateRows=True,
                             enableFilter=True,
                             floatingFilter=True)
    gb.configure_selection('single')
    
    grid_options = gb.build()
    
    # Add custom CSS for center-aligned headers
    st.markdown("""
    <style>
    .ag-header-cell-text {
        text-align: center !important;
    }
    .ag-header-cell {
        text-align: center !important;
    }
    .center-header .ag-header-cell-text {
        text-align: center !important;
        justify-content: center !important;
    }
    .ag-cell {
        display: flex !important;
        align-items: center !important;
    }
    .ag-floating-filter-input {
        text-align: center !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Display the grid
    grid_response = AgGrid(
        df,
        gridOptions=grid_options,
        update_mode=GridUpdateMode.VALUE_CHANGED,
        data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
        fit_columns_on_grid_load=True,
        height=450,
        theme=ag_theme,
        allow_unsafe_jscode=True
    )
    
    # Save All button
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("Save All Changes", type="primary"):
            updated_df = grid_response['data']
            
            # Convert DataFrame back to logistics data format
            updated_logistics_data = {}
            for _, row in updated_df.iterrows():
                brand = row['Brand']
                updated_logistics_data[brand] = {
                    'customs_exw': float(row['Customs EXW %']),
                    'shipping_exw': float(row['Shipping EXW %']),
                    'customs_fob': float(row['Customs FOB %']),
                    'shipping_fob': float(row['Shipping FOB %']),
                    'default_shipping': str(row['Default Shipping']),
                    'purchase_currency': str(row['Purchase Currency']),
                    'sales_currency': str(row['Sales Currency'])
                }
            
            # Get original data for change detection
            original_data = st.session_state.get('original_logistics_data', {})
            
            # Save only changes using optimized bulk operations
            with st.spinner("Saving logistics changes..."):
                success, changes_count, total_operations = save_all_logistics_settings(updated_logistics_data, original_data)
            
            if success:
                if changes_count == 0:
                    st.info("No changes detected - nothing to save!")
                else:
                    st.success(f"Successfully saved {changes_count} changes out of {total_operations} operations!")
                    # Update original data to reflect saved changes
                    st.session_state.original_logistics_data = updated_logistics_data.copy()
                st.rerun()
            else:
                st.error("Failed to save settings. Please try again.")
    
    with col2:
        if st.button("Reset Changes"):
            # Clear session state to reload original data
            if 'original_logistics_data' in st.session_state:
                del st.session_state.original_logistics_data
            st.rerun()
    
    with col3:
        st.write(f"**Total Brands:** {len(df)} | **Editable Fields:** 7 per brand")
    
    # Show performance improvement info
    st.info("**Performance:** Fast loading with single query + bulk save operations. Only changed values are saved to the database.")
    
    # Show summary statistics
    with st.expander("Summary Statistics", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Average Rates:**")
            st.write(f"• Customs EXW: {df['Customs EXW %'].mean():.1f}%")
            st.write(f"• Shipping EXW: {df['Shipping EXW %'].mean():.1f}%")
            st.write(f"• Customs FOB: {df['Customs FOB %'].mean():.1f}%")
            st.write(f"• Shipping FOB: {df['Shipping FOB %'].mean():.1f}%")
        
        with col2:
            st.write("**Most Common Settings:**")
            st.write(f"• Default Shipping: {df['Default Shipping'].mode().iloc[0] if not df['Default Shipping'].mode().empty else 'N/A'}")
            st.write(f"• Purchase Currency: {df['Purchase Currency'].mode().iloc[0] if not df['Purchase Currency'].mode().empty else 'N/A'}")
            st.write(f"• Sales Currency: {df['Sales Currency'].mode().iloc[0] if not df['Sales Currency'].mode().empty else 'N/A'}")
    
    # Import/Export section
    st.divider()
    st.subheader("Import/Export Brand Settings")
    
    # Export button
    if st.button("Export All Brand Settings", type="secondary"):
        export_all_brand_settings()
    
    st.markdown("---")
    
    # Use full container width for import interface
    from components.data_loader import upload_file_for_brand_settings
    upload_file_for_brand_settings()

def show_margin_by_brand_settings():
    """Display and manage margin percentage settings by brand and category using optimized AgGrid."""
    # Detect theme for AgGrid
    is_dark_theme = st.session_state.get('dark_theme', True)
    ag_theme = 'streamlit' if is_dark_theme else 'alpine'
    
    # Get all brands
    brands = get_brands()
    
    if not brands:
        st.warning("No brands found in the database. Please upload product data first.")
        return
    
    # Load margin data using optimized function
    with st.spinner("Loading margin settings..."):
        margin_data = get_all_margin_settings_for_brands()
    
    if not margin_data:
        st.error("Failed to load margin data. Please check your database connection.")
        return
    
    # Store original data in session state for change detection
    if 'original_margin_data' not in st.session_state:
        st.session_state.original_margin_data = margin_data.copy()
    
    # Convert to DataFrame for AgGrid
    df_data = []
    for brand, settings in margin_data.items():
        df_data.append({
            'Brand': brand,
            'A %': settings['margin_a'],
            'B %': settings['margin_b'],
            'C %': settings['margin_c'],
            'D %': settings['margin_d'],
            'EOL %': settings['margin_eol'],
            'Export %': settings['margin_export'],
            'Default Category': settings['default_category']
        })
    
    df = pd.DataFrame(df_data)
    
    # Configure AgGrid
    gb = GridOptionsBuilder.from_dataframe(df)
    
    # Configure column properties with center alignment and filters
    gb.configure_column("Brand", 
                       editable=False, 
                       pinned='left', 
                       width=150,
                       filter='agTextColumnFilter',
                       filterParams={'filterOptions': ['contains', 'startsWith', 'endsWith']})
    
    # Configure margin percentage columns
    margin_columns = ['A %', 'B %', 'C %', 'D %', 'EOL %', 'Export %']
    for col in margin_columns:
        gb.configure_column(col, 
                           editable=True, 
                           type=["numericColumn", "numberColumnFilter"], 
                           cellEditor='agNumberCellEditor',
                           cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                           cellStyle={'textAlign': 'center'},
                           headerClass='center-header')
    
    # Configure default category column (Export excluded from default options)
    gb.configure_column("Default Category", 
                       editable=True,
                       cellEditor='agSelectCellEditor',
                       cellEditorParams={'values': ['A', 'B', 'C', 'D', 'EOL']},
                       filter='agSetColumnFilter',
                       filterParams={'values': ['A', 'B', 'C', 'D', 'EOL']},
                       cellStyle={'textAlign': 'center'},
                       headerClass='center-header')
    
    # Configure grid options
    gb.configure_default_column(groupable=False, sortable=True, filterable=True)
    gb.configure_grid_options(enableRangeSelection=True, 
                             enableCellChangeFlash=True,
                             suppressRowDeselection=True,
                             animateRows=True,
                             enableFilter=True,
                             floatingFilter=True)
    gb.configure_selection('single')
    
    grid_options = gb.build()
    
    # Add custom CSS for center-aligned headers
    st.markdown("""
    <style>
    .ag-header-cell-text {
        text-align: center !important;
    }
    .ag-header-cell {
        text-align: center !important;
    }
    .center-header .ag-header-cell-text {
        text-align: center !important;
        justify-content: center !important;
    }
    .ag-cell {
        display: flex !important;
        align-items: center !important;
    }
    .ag-floating-filter-input {
        text-align: center !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Display the grid
    grid_response = AgGrid(
        df,
        gridOptions=grid_options,
        update_mode=GridUpdateMode.VALUE_CHANGED,
        data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
        fit_columns_on_grid_load=True,
        height=450,
        theme=ag_theme,
        allow_unsafe_jscode=True
    )
    
    # Save All button
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("Save All Changes", type="primary", key="save_margin_changes"):
            updated_df = grid_response['data']
            
            # Convert DataFrame back to margin data format
            updated_margin_data = {}
            for _, row in updated_df.iterrows():
                brand = row['Brand']
                updated_margin_data[brand] = {
                    'margin_a': float(row['A %']),
                    'margin_b': float(row['B %']),
                    'margin_c': float(row['C %']),
                    'margin_d': float(row['D %']),
                    'margin_eol': float(row['EOL %']),
                    'margin_export': float(row['Export %']),
                    'default_category': str(row['Default Category'])
                }
            
            # Get original data for change detection
            original_data = st.session_state.get('original_margin_data', {})
            
            # Save only changes using optimized bulk operations
            with st.spinner("Saving margin changes..."):
                success, changes_count, total_operations = save_all_margin_settings(updated_margin_data, original_data)
            
            if success:
                if changes_count == 0:
                    st.info("No changes detected - nothing to save!")
                else:
                    st.success(f"Successfully saved {changes_count} changes out of {total_operations} operations!")
                    # Update original data to reflect saved changes
                    st.session_state.original_margin_data = updated_margin_data.copy()
                st.rerun()
            else:
                st.error("Failed to save settings. Please try again.")
    
    with col2:
        if st.button("Reset Changes", key="reset_margin_changes"):
            # Clear session state to reload original data
            if 'original_margin_data' in st.session_state:
                del st.session_state.original_margin_data
            st.rerun()
    
    with col3:
        st.write(f"**Total Brands:** {len(df)} | **Editable Fields:** 7 per brand")
    
    # Show performance improvement info
    st.info("**Performance:** Fast loading with single query + bulk save operations. Only changed values are saved to the database.")
    
    # Export functionality
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Export Current Settings", key="export_margin_settings"):
            # Create export data from current grid state
            current_df = grid_response['data']
            csv = current_df.to_csv(index=False)
            
            st.download_button(
                label="Download Margin Settings CSV",
                data=csv,
                file_name="margin_by_brand_settings.csv",
                mime="text/csv",
                key="download_margin_csv"
            )
    
    # Show summary statistics
    with st.expander("Summary Statistics", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Average Margins by Category")
            for category in ['A %', 'B %', 'C %', 'D %', 'EOL %', 'Export %']:
                avg_margin = df[category].mean()
                st.write(f"**{category}:** {avg_margin:.1f}%")
        
        with col2:
            st.subheader("Default Category Distribution")
            default_counts = df['Default Category'].value_counts()
            for category, count in default_counts.items():
                percentage = (count / len(df)) * 100
                st.write(f"**{category}:** {count} brands ({percentage:.1f}%)")

def show_discount_by_brand_settings():
    """Display and manage discount percentage settings by brand and category using optimized AgGrid."""
    # Detect theme for AgGrid
    is_dark_theme = st.session_state.get('dark_theme', True)
    ag_theme = 'streamlit' if is_dark_theme else 'alpine'
    
    # Get all brands
    brands = get_brands()
    
    if not brands:
        st.warning("No brands found in the database. Please upload product data first.")
        return
    
    # Load discount data using optimized function
    with st.spinner("Loading discount settings..."):
        discount_data = get_all_discount_settings_for_brands()
    
    if not discount_data:
        st.error("Failed to load discount data. Please check your database connection.")
        return
    
    # Store original data in session state for change detection
    if 'original_discount_data' not in st.session_state:
        st.session_state.original_discount_data = discount_data.copy()
    
    # Convert to DataFrame for AgGrid
    df_data = []
    for brand, settings in discount_data.items():
        df_data.append({
            'Brand': brand,
            'I %': settings['discount_i'],
            'II %': settings['discount_ii'],
            'III %': settings['discount_iii'],
            'IV %': settings['discount_iv']
        })
    
    df = pd.DataFrame(df_data)
    
    # Configure AgGrid
    gb = GridOptionsBuilder.from_dataframe(df)
    
    # Configure column properties with center alignment and filters
    gb.configure_column("Brand", 
                       editable=False, 
                       pinned='left', 
                       width=150,
                       filter='agTextColumnFilter',
                       filterParams={'filterOptions': ['contains', 'startsWith', 'endsWith']})
    
    # Configure discount percentage columns
    discount_columns = ['I %', 'II %', 'III %', 'IV %']
    for col in discount_columns:
        gb.configure_column(col, 
                           editable=True, 
                           type=["numericColumn", "numberColumnFilter"], 
                           cellEditor='agNumberCellEditor',
                           cellEditorParams={'min': 0, 'max': 100, 'step': 0.1},
                           cellStyle={'textAlign': 'center'},
                           headerClass='center-header')
    
    # Configure grid options
    gb.configure_default_column(groupable=False, sortable=True, filterable=True)
    gb.configure_grid_options(enableRangeSelection=True, 
                             enableCellChangeFlash=True,
                             suppressRowDeselection=True,
                             animateRows=True,
                             enableFilter=True,
                             floatingFilter=True)
    gb.configure_selection('single')
    
    grid_options = gb.build()
    
    # Add custom CSS for center-aligned headers
    st.markdown("""
    <style>
    .ag-header-cell-text {
        text-align: center !important;
    }
    .ag-header-cell {
        text-align: center !important;
    }
    .center-header .ag-header-cell-text {
        text-align: center !important;
        justify-content: center !important;
    }
    .ag-cell {
        display: flex !important;
        align-items: center !important;
    }
    .ag-floating-filter-input {
        text-align: center !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Display the grid
    grid_response = AgGrid(
        df,
        gridOptions=grid_options,
        update_mode=GridUpdateMode.VALUE_CHANGED,
        data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
        fit_columns_on_grid_load=True,
        height=450,
        theme=ag_theme,
        allow_unsafe_jscode=True
    )
    
    # Save All button
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("Save All Changes", type="primary", key="save_discount_changes"):
            updated_df = grid_response['data']
            
            # Convert DataFrame back to discount data format
            updated_discount_data = {}
            for _, row in updated_df.iterrows():
                brand = row['Brand']
                updated_discount_data[brand] = {
                    'discount_i': float(row['I %']),
                    'discount_ii': float(row['II %']),
                    'discount_iii': float(row['III %']),
                    'discount_iv': float(row['IV %'])
                }
            
            # Get original data for change detection
            original_data = st.session_state.get('original_discount_data', {})
            
            # Save only changes using optimized bulk operations
            with st.spinner("Saving discount changes..."):
                success, changes_count, total_operations = save_all_discount_settings(updated_discount_data, original_data)
            
            if success:
                if changes_count == 0:
                    st.info("No changes detected - nothing to save!")
                else:
                    st.success(f"Successfully saved {changes_count} changes out of {total_operations} operations!")
                    # Update original data to reflect saved changes
                    st.session_state.original_discount_data = updated_discount_data.copy()
                st.rerun()
            else:
                st.error("Failed to save settings. Please try again.")
    
    with col2:
        if st.button("Reset Changes", key="reset_discount_changes"):
            # Clear session state to reload original data
            if 'original_discount_data' in st.session_state:
                del st.session_state.original_discount_data
            st.rerun()
    
    with col3:
        st.write(f"**Total Brands:** {len(df)} | **Editable Fields:** 4 per brand")
    
    # Show performance improvement info
    st.info("**Performance:** Fast loading with single query + bulk save operations. Only changed values are saved to the database.")
    
    # Export functionality
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Export Current Settings", key="export_discount_settings"):
            # Create export data from current grid state
            current_df = grid_response['data']
            csv = current_df.to_csv(index=False)
            
            st.download_button(
                label="Download Discount Settings CSV",
                data=csv,
                file_name="discount_by_brand_settings.csv",
                mime="text/csv",
                key="download_discount_csv"
            )
    
    # Show summary statistics
    with st.expander("Summary Statistics", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Average Discounts by Category")
            for category in ['I %', 'II %', 'III %', 'IV %']:
                avg_discount = df[category].mean()
                st.write(f"**{category}:** {avg_discount:.1f}%")



def show_general_settings():
    """Display and manage general settings."""
    st.subheader("General Settings")
    
    # Default Currency
    st.write("**Default Currency**")
    
    # Get existing default currency setting
    existing_currency_settings = get_custom_settings('default_currency', 'global')
    default_currency = "HUF"
    if existing_currency_settings:
        default_currency = existing_currency_settings[0].value
    
    currencies = ["HUF", "EUR", "USD", "RON"]
    new_default_currency = st.selectbox(
        "Default Currency for New Imports",
        currencies,
        index=currencies.index(default_currency) if default_currency in currencies else 0
    )
    
    if st.button("Save Default Currency"):
        if save_custom_setting('default_currency', 'global', 'default', new_default_currency):
            st.success("Default currency updated successfully!")
        else:
            st.error("Failed to update default currency. Please try again.")
    
    # Exchange Rates
    st.write("**Exchange Rates**")
    
    # Get existing exchange rate settings
    existing_eur_rate = get_custom_settings('exchange_rate', 'EUR')
    existing_usd_rate = get_custom_settings('exchange_rate', 'USD')
    existing_ron_rate = get_custom_settings('exchange_rate', 'RON')
    
    eur_rate = 380.0
    usd_rate = 350.0
    ron_rate = 76.0
    
    if existing_eur_rate:
        eur_rate = existing_eur_rate[0].value
    if existing_usd_rate:
        usd_rate = existing_usd_rate[0].value
    if existing_ron_rate:
        ron_rate = existing_ron_rate[0].value
    
    with st.form("exchange_rates_form"):
        new_eur_rate = st.number_input("EUR to HUF", min_value=1.0, value=eur_rate, step=1.0)
        new_usd_rate = st.number_input("USD to HUF", min_value=1.0, value=usd_rate, step=1.0)
        new_ron_rate = st.number_input("RON to HUF", min_value=1.0, value=ron_rate, step=1.0)
        
        submit_button = st.form_submit_button("Save Exchange Rates")
        
        if submit_button:
            success = True
            
            if new_eur_rate != eur_rate:
                if not save_custom_setting('exchange_rate', 'EUR', 'rate', new_eur_rate):
                    success = False
            
            if new_usd_rate != usd_rate:
                if not save_custom_setting('exchange_rate', 'USD', 'rate', new_usd_rate):
                    success = False
            
            if new_ron_rate != ron_rate:
                if not save_custom_setting('exchange_rate', 'RON', 'rate', new_ron_rate):
                    success = False
            
            if success:
                st.success("Exchange rates updated successfully!")
            else:
                st.error("Failed to update some exchange rates. Please try again.")

def show_system_info():
    """Display system and database information."""
    st.subheader("System Information")
    
    # Environment information
    st.write("### Environment")
    env_name = config.ENVIRONMENT_NAME
    st.info(f"Running in: **{env_name}**")
    
    # Database connection information
    st.write("### Database Connection")
    connection_success, connection_message = check_db_connection()
    
    if connection_success:
        st.success(connection_message)
    else:
        st.error(connection_message)
    
    # Data cleanup section
    st.write("### Data Cleanup Tools")
    st.write("Fix incorrectly stored product categories in brand fields by moving them to proper vendor product group fields and extracting real brand names.")
    
    if st.button("Clean Product Brands", key="clean_brands"):
        from utils import clean_product_brands
        updated_count = clean_product_brands()
        if updated_count > 0:
            st.rerun()  # Refresh the page to show updated data
    
    # Show AWS configuration status if relevant
    if config.IS_AWS:
        st.write("### AWS Configuration")
        aws_vars_ok, missing_vars = config.check_aws_env_vars()
        
        if aws_vars_ok:
            st.success("All required AWS environment variables are set.")
        else:
            st.error(f"Missing required AWS environment variables: {', '.join(missing_vars)}")
            st.write("""
            To deploy to AWS, make sure the following environment variables are set:
            - DB_HOST: The hostname of your AWS RDS PostgreSQL instance
            - DB_NAME: The name of your database
            - DB_USER: The username for your database
            - DB_PASSWORD: The password for your database
            """)
    
    # Show technical information for developers
    if config.ENABLE_DEBUG_FEATURES:
        st.write("### Technical Details")
        st.write("This information is visible in development environments only.")
        
        import platform
        import sys
        
        # System info
        system_info = {
            "Python Version": sys.version.split()[0],
            "Platform": platform.platform(),
            "Environment": config.ENVIRONMENT_NAME
        }
        
        # Redact database URL to avoid exposing credentials
        db_url_display = DATABASE_URL
        for secret in ["password", "user", "dbname", "host"]:
            if secret in db_url_display.lower():
                db_url_display = db_url_display.replace(db_url_display, "[DATABASE URL REDACTED]")
                break
                
        system_info["Database URL"] = db_url_display
        
        st.json(system_info)

def export_all_brand_settings():
    """Export all brand settings (Logistics, Margins, Discounts) to a single unified table."""
    import pandas as pd
    from io import BytesIO
    
    try:
        # Get all brands
        brands = get_brands()
        
        if not brands:
            st.error("No brands found in the database.")
            return
        
        # Use optimized functions to get all data
        with st.spinner("Loading all brand settings..."):
            logistics_data = get_all_logistics_settings_for_brands()
            margin_data = get_all_margin_settings_for_brands()
            discount_data = get_all_discount_settings_for_brands()
        
        # Create unified table with all settings
        unified_export_data = []
        for brand in brands:
            # Get logistics settings
            logistics_settings = logistics_data.get(brand, {})
            
            # Get margin settings  
            margin_settings = margin_data.get(brand, {})
            
            # Get discount settings
            discount_settings = discount_data.get(brand, {})
            
            # Combine all into one row
            unified_export_data.append({
                'Brand': brand,
                # Logistics columns
                'Customs_EXW_Percent': logistics_settings.get('customs_exw', 0.0),
                'Shipping_EXW_Percent': logistics_settings.get('shipping_exw', 0.0),
                'Customs_FOB_Percent': logistics_settings.get('customs_fob', 0.0),
                'Shipping_FOB_Percent': logistics_settings.get('shipping_fob', 0.0),
                'Default_Shipping': logistics_settings.get('default_shipping', 'EXW'),
                'Purchase_Currency': logistics_settings.get('purchase_currency', 'EUR'),
                'Sales_Currency': logistics_settings.get('sales_currency', 'EUR'),
                # Margin columns
                'Margin_A_Percent': margin_settings.get('margin_a', 0.0),
                'Margin_B_Percent': margin_settings.get('margin_b', 0.0),
                'Margin_C_Percent': margin_settings.get('margin_c', 0.0),
                'Margin_D_Percent': margin_settings.get('margin_d', 0.0),
                'Margin_EOL_Percent': margin_settings.get('margin_eol', 0.0),
                'Margin_Export_Percent': margin_settings.get('margin_export', 0.0),
                'Default_Margin_Category': margin_settings.get('default_category', 'A'),
                # Discount columns
                'Discount_I_Percent': discount_settings.get('discount_i', 0.0),
                'Discount_II_Percent': discount_settings.get('discount_ii', 0.0),
                'Discount_III_Percent': discount_settings.get('discount_iii', 0.0),
                'Discount_IV_Percent': discount_settings.get('discount_iv', 0.0)
            })
        
        unified_df = pd.DataFrame(unified_export_data)
        
        # Create Excel file
        buffer = BytesIO()
        
        try:
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                unified_df.to_excel(writer, sheet_name='Brand_Settings', index=False)
                
                # Auto-adjust column widths
                worksheet = writer.sheets['Brand_Settings']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
        except Exception as excel_error:
            # Fallback to CSV
            st.warning("Excel creation failed, providing CSV format...")
            csv_content = unified_df.to_csv(index=False)
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"brand_settings_unified_{timestamp}.csv"
            
            st.download_button(
                label="Download Brand Settings (CSV)",
                data=csv_content,
                file_name=filename,
                mime="text/csv"
            )
            
            st.success(f"Brand settings exported as CSV! File contains {len(brands)} brands with all settings.")
            return
        
        # Prepare Excel file for download
        buffer.seek(0)
        
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"brand_settings_unified_{timestamp}.xlsx"
        
        st.download_button(
            label="Download Brand Settings (Excel)",
            data=buffer.getvalue(),
            file_name=filename,
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
        st.success(f"Brand settings exported successfully! File contains {len(brands)} brands with all logistics, margin, and discount settings in one table.")
        
    except Exception as e:
        st.error(f"Error exporting brand settings: {str(e)}")
        st.info("You can try exporting individual tables using the 'Export Current Settings' buttons in each tab.")

def import_all_brand_settings(uploaded_file):
    """Import all brand settings from unified Excel/CSV file."""
    import pandas as pd
    
    try:
        # Try to read as Excel first, then CSV
        try:
            if uploaded_file.name.endswith('.xlsx'):
                df = pd.read_excel(uploaded_file)
            else:
                df = pd.read_csv(uploaded_file)
        except Exception as read_error:
            st.error(f"Failed to read file: {str(read_error)}")
            return
        
        # Define expected columns for unified format
        required_columns = [
            'Brand',
            # Logistics columns
            'Customs_EXW_Percent', 'Shipping_EXW_Percent', 'Customs_FOB_Percent', 'Shipping_FOB_Percent',
            'Default_Shipping', 'Purchase_Currency', 'Sales_Currency',
            # Margin columns
            'Margin_A_Percent', 'Margin_B_Percent', 'Margin_C_Percent', 'Margin_D_Percent',
            'Margin_EOL_Percent', 'Margin_Export_Percent', 'Default_Margin_Category',
            # Discount columns
            'Discount_I_Percent', 'Discount_II_Percent', 'Discount_III_Percent', 'Discount_IV_Percent'
        ]
        
        # Check for required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            st.error(f"Missing required columns: {', '.join(missing_columns)}")
            st.info(f"Available columns: {', '.join(df.columns.tolist())}")
            st.info("Please use the export function to see the correct format.")
            return
        
        # Show preview of data
        st.write("### Import Preview")
        st.dataframe(df.head())
        
        st.write(f"Total rows: {len(df)}")
        
        # Process upload button using proven pattern
        if st.button("Save to Database", key="save_brand_settings_btn"):
            try:
                # Import using the proven save function
                from utils import save_brand_settings_data
                
                count = save_brand_settings_data(df)
                
                if count > 0:
                    st.success(f"Successfully imported settings for {count} brands!")
                    
                    # Clear all cached data to force refresh
                    if 'original_logistics_data' in st.session_state:
                        del st.session_state.original_logistics_data
                    if 'original_margin_data' in st.session_state:
                        del st.session_state.original_margin_data
                    if 'original_discount_data' in st.session_state:
                        del st.session_state.original_discount_data
                    
                    st.info("Page will refresh to show updated settings...")
                    st.rerun()
                else:
                    st.warning("No records were imported. Please check your file and try again.")
                    
            except Exception as e:
                st.error(f"Error during import: {str(e)}")
                st.exception(e)
            
    except Exception as e:
        st.error(f"Error during import: {str(e)}")
        st.info("Please check your file format and try again.")
