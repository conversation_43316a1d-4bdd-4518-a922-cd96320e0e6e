To analyze Shure sales in 2025 by customer group, we need to join the sales data with the buyers table and group the results by the buyer category. Here's the SQL query to achieve this:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    b.buyer_category, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
JOIN 
    buyers b ON s.buyer_id = b.buyer_id
WHERE 
    p.brand ILIKE 'Shure' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name, b.buyer_category
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from Shure products sold in 2025, broken down by customer group. The results will include the brand, product code, product name, buyer category, and total revenue for each product and customer group.

🔍 SQL Query Used

📊 Results
tell me about ADPSM sales

To analyze sales for products with "ADPSM" in their name, we need to query the sales data for products that match this pattern. Here's how we can do that:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_name ILIKE '%ADPSM%'
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from sales of products with "ADPSM" in their name. The results will include the brand, product code, product name, and total revenue for each product.

🔍 SQL Query Used

No data found for this query.

ADXR sales in 2025

To analyze sales for products with "ADXR" in their name specifically for the year 2025, we can use the following query:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_name ILIKE '%ADXR%' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from sales of products with "ADXR" in their name during the year 2025. The results will include the brand, product code, product name, and total revenue for each product.

🔍 SQL Query Used

📊 Results
Chart Type

bar

ADXR=-A sales in 2025

To analyze sales for products with "ADXR=-A" in their name specifically for the year 2025, we can use the following query:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_name ILIKE '%ADXR=-A%' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from sales of products with "ADXR=-A" in their name during the year 2025. The results will include the brand, product code, product name, and total revenue for each product.

🔍 SQL Query Used

No data found for this query.

ADXR is termékkód. give me 2025 sales

To analyze sales for the product with the termékkód "ADXR" specifically for the year 2025, we can use the following query:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_id = 'ADXR' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from sales of the product with the termékkód "ADXR" during the year 2025. The results will include the brand, product code, product name, and total revenue for the product.

🔍 SQL Query Used

No data found for this query.

ADXR=-A is termékkód sales in 2025

To analyze sales for the product with the termékkód "ADXR=-A" specifically for the year 2025, we can use the following query:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_id = 'ADXR=-A' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide the total revenue from sales of the product with the termékkód "ADXR=-A" during the year 2025. The results will include the brand, product code, product name, and total revenue for the product.

🔍 SQL Query Used

📊 Results
show unit quantiy

To include the total quantity sold for the product with the termékkód "ADXR=-A" in 2025, we can modify the query to also sum the quantities. Here's the updated query:

SELECT 
    p.brand, 
    p.product_id AS termékkód, 
    p.product_name AS terméknév, 
    SUM(s.quantity) AS total_quantity,
    SUM(s.quantity * s.unit_price) AS total_revenue
FROM 
    products p
JOIN 
    sales s ON p.product_id = s.product_id
WHERE 
    p.product_id = 'ADXR=-A' AND 
    EXTRACT(YEAR FROM s.sale_date) = 2025
GROUP BY 
    p.brand, p.product_id, p.product_name
ORDER BY 
    total_revenue DESC

This query will provide both the total quantity sold and the total revenue from sales of the product with the termékkód "ADXR=-A" during the year 2025. The results will include the brand, product code, product name, total quantity, and total revenue for the product.

🔍 SQL Query Used

📊 Results
