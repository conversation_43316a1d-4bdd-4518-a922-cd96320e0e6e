import streamlit as st
import datetime
from utils import (
    get_brands,
    get_product_categories,
    calculate_kpis
)
from components.overview_visualizations import (
    create_kpi_metrics,
    create_sales_trend_chart,
    create_sales_by_brand_chart,
    create_stock_by_warehouse_chart,
    create_top_products_chart,
    create_top_buyers_chart
)

def show():
    """Display the Overview tab content."""
    st.header("Overview Dashboard")
    
    # Filter section (collapsed by default)
    with st.expander("Filters", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            # Date range filter
            today = datetime.datetime.now().date()
            first_day_of_year = datetime.date(today.year, 1, 1)
            
            start_date = st.date_input("Start Date", first_day_of_year)
            end_date = st.date_input("End Date", today)
            
        with col2:
            # Brand filter
            brands = ["All"] + get_brands()
            selected_brand = st.selectbox("Brand", brands)
            
            # Product category filter
            categories = ["All"] + get_product_categories()
            selected_category = st.selectbox("Product Category", categories)
    
    # Apply filters
    brand_filter = None if selected_brand == "All" else selected_brand
    category_filter = None if selected_category == "All" else selected_category
    
    # Fetch KPIs
    kpis = calculate_kpis(start_date, end_date)
    
    # Display KPI metrics
    create_kpi_metrics(kpis)
    
    # Display main charts
    col1, col2 = st.columns(2)
    
    with col1:
        # Sales trend over time
        create_sales_trend_chart(start_date, end_date, brand_filter)
        
        # Top products by revenue
        create_top_products_chart('revenue', 5, start_date, end_date)
    
    with col2:
        # Sales by brand
        create_sales_by_brand_chart(start_date, end_date)
        
        # Top buyers by revenue
        create_top_buyers_chart('revenue', 5, start_date, end_date)
    
    # Additional charts
    col1, col2 = st.columns(2)
    
    with col1:
        # Stock by warehouse
        create_stock_by_warehouse_chart()
    
    with col2:
        # Top products by stock value
        create_top_products_chart('stock_value', 5)
    
    # No dashboard image - keeping the UI clean
