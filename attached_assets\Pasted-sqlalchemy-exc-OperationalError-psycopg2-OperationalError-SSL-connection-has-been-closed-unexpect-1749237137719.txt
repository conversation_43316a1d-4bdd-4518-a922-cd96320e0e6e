sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) SSL connection has been closed unexpectedly (Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/exec_code.py", line 121, in exec_func_with_error_handling
    result = func()
             ^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 648, in code_to_exec
    exec(code, module.__dict__)
File "/home/<USER>/workspace/app.py", line 167, in <module>
    main()
File "/home/<USER>/workspace/app.py", line 159, in main
    pricing.show()
File "/home/<USER>/workspace/app_pages/pricing.py", line 72, in show
    show_competitor_scrape()
File "/home/<USER>/workspace/app_pages/pricing.py", line 1063, in show_competitor_scrape
    all_product_groups = [group for group in get_product_categories() if group]
                                             ^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/utils.py", line 1512, in get_product_categories
    session.close()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2521, in close
    self._close_impl(invalidate=False)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2590, in _close_impl
    transaction.close(invalidate)
File "<string>", line 2, in close
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1419, in close
    transaction.close()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2586, in close
    self._do_close()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2724, in _do_close
    self._close_impl()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2710, in _close_impl
    self._connection_rollback_impl()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2702, in _connection_rollback_impl
    self.connection._rollback_impl()
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1127, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1125, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 703, in do_rollback
    dbapi_connection.rollback()