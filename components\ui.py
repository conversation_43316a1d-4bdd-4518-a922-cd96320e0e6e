"""
UI components and layout utilities for the ERP Analytics Dashboard.
"""
import streamlit as st
from config import config
import json
import os

# Define CSS styles for various UI components
def load_css():
    """Load custom CSS styles."""
    st.markdown("""
    <style>
    /* Zero margin/padding on page elements */
    #root > div {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    
    /* Override sidebar container and title */
    [data-testid="stSidebar"] {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    
    [data-testid="stSidebar"] h1:first-child {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    
    /* Card-like container styling */
    .dashboard-card {
        border-radius: 5px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background-color: #FFFFFF;
        box-shadow: 0 2px 5px rgba(0,0,0,0.07);
    }
    
    /* KPI metrics styling */
    .metric-container {
        padding: 1rem;
        border-radius: 5px;
        text-align: center;
        border: 1px solid #E6ECF1;
        background-color: #FFFFFF;
    }
    
    .metric-value {
        font-size: 1.8rem;
        font-weight: bold;
        color: #0068C9;
    }
    
    .metric-label {
        font-size: 0.9rem;
        color: #555;
        margin-top: 0.3rem;
    }
    
    /* Collapsible section styling */
    .collapsible-header {
        cursor: pointer;
        padding: 0.7rem;
        background-color: #F5F7F9;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .collapsible-content {
        padding: 1rem;
        border: 1px solid #E6ECF1;
        border-radius: 0 0 5px 5px;
        display: none;
    }
    
    /* Filter section styling */
    .filter-container {
        background-color: #F8F9FA;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1.5rem;
    }
    
    /* Navigation styling */
    .nav-item {
        padding: 0.5rem 1rem;
        margin-bottom: 0.3rem;
        border-radius: 5px;
        background-color: #FFFFFF;
        transition: background-color 0.2s;
    }
    
    .nav-item:hover {
        background-color: #EAEAEA;
    }
    
    .nav-item.active {
        background-color: #0068C9;
        color: white;
    }
    
    /* Table styling */
    .styled-table th {
        background-color: #F2F7FF;
        color: #0B3D91;
        padding: 8px 15px;
        border-bottom: 2px solid #0068C9;
    }
    
    .styled-table td {
        padding: 5px 15px;
        border-bottom: 1px solid #E6ECF1;
    }
    
    .styled-table tr:nth-child(even) {
        background-color: #F8F9FA;
    }
    
    /* Improved buttons */
    .primary-button {
        background-color: #0068C9;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
    }
    
    .secondary-button {
        background-color: #F0F2F6;
        color: #0068C9;
        border: 1px solid #0068C9;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
    }
    
    /* Custom header with version info */
    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid #E6ECF1;
    }
    
    .version-info {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    /* Responsive tweaks */
    @media (max-width: 768px) {
        .dashboard-card {
            padding: 1rem;
        }
        
        .metric-value {
            font-size: 1.4rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)

def get_user_preferences():
    """Get user preferences from session state or initialize defaults."""
    if 'user_preferences' not in st.session_state:
        # Default preferences
        default_preferences = {
            "default_view": "Home",
            "show_filters_expanded": False,  # Default to collapsed filters
            "dark_mode": False,
            "data_decimal_places": 2,
            "currency_symbol": "Ft",
            "date_format": "YYYY-MM-DD",
            "table_rows_per_page": 10,
            "chart_color_scheme": "blue"
        }
        
        # Try to load from file if it exists
        pref_file = 'user_preferences.json'
        if os.path.exists(pref_file):
            try:
                with open(pref_file, 'r') as f:
                    saved_prefs = json.load(f)
                    # Merge saved prefs with defaults (keeping defaults for any missing keys)
                    default_preferences.update(saved_prefs)
            except Exception as e:
                st.warning(f"Could not load user preferences: {e}")
        
        st.session_state.user_preferences = default_preferences
    
    return st.session_state.user_preferences

def save_user_preferences(preferences):
    """Save user preferences to file."""
    pref_file = 'user_preferences.json'
    try:
        with open(pref_file, 'w') as f:
            json.dump(preferences, f)
        st.session_state.user_preferences = preferences
        return True
    except Exception as e:
        st.error(f"Could not save preferences: {e}")
        return False

def display_header():
    """Display the dashboard header with version information."""
    # Empty header - we want to remove the ERP Analytics Dashboard logo
    # Also add some CSS to remove margins and padding
    st.markdown("""
    <style>
        /* Extra CSS to ensure sidebar title and content start at the very top */
        [data-testid="stSidebar"] > div:first-child {
            padding-top: 0 !important;
            margin-top: 0 !important;
        }
        
        /* Extra CSS for sidebar heading */
        [data-testid="stSidebar"] h1 {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        
        /* Override any Streamlit inline styles */
        div:has(> [data-testid="stSidebar"]) {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
    </style>
    """, unsafe_allow_html=True)
    pass

def display_kpi_metrics(metrics):
    """
    Display KPI metrics in a row of cards.
    
    Args:
        metrics: Dictionary of metric labels and values
    """
    columns = st.columns(len(metrics))
    
    for i, (label, value) in enumerate(metrics.items()):
        with columns[i]:
            st.markdown(f"""
            <div class="metric-container">
                <div class="metric-value">{value}</div>
                <div class="metric-label">{label}</div>
            </div>
            """, unsafe_allow_html=True)

def create_collapsible_section(header, content_function, default_expanded=False, key=None):
    """
    Create a collapsible section with header and content.
    
    Args:
        header: String title for the section
        content_function: Function that will render the content
        default_expanded: Whether section starts expanded
        key: Unique key for this section
    """
    unique_key = key or f"collapsible_{header.replace(' ', '_').lower()}"
    
    # Initialize state if not present
    if unique_key not in st.session_state:
        st.session_state[unique_key] = default_expanded
    
    # Create expand/collapse button
    button_text = "▼" if st.session_state[unique_key] else "►"
    
    col1, col2 = st.columns([0.98, 0.02])
    with col1:
        if st.button(f"{header} {button_text}", key=f"btn_{unique_key}"):
            st.session_state[unique_key] = not st.session_state[unique_key]
    
    # Show content if expanded
    if st.session_state[unique_key]:
        content_function()

def create_filter_section(title="Filters", default_expanded=False, key=None):
    """Create a collapsible filter section."""
    unique_key = key or "filter_section"
    is_expanded = st.session_state.get(f"{unique_key}_expanded", default_expanded)
    
    with st.expander(title, expanded=is_expanded):
        # Return the container to allow caller to add filters
        return st.container()

def create_card(content_function, title=None):
    """Create a card-like container for content."""
    with st.container():
        if title:
            st.markdown(f"### {title}")
        
        st.markdown('<div class="dashboard-card">', unsafe_allow_html=True)
        content_function()
        st.markdown('</div>', unsafe_allow_html=True)

def display_data_table(df, use_pagination=True, rows_per_page=10, key=None):
    """
    Display a dataframe as a styled table with optional pagination.
    
    Args:
        df: Pandas dataframe to display
        use_pagination: Whether to paginate the table
        rows_per_page: Number of rows per page
        key: Unique key for this table
    """
    key = key or "data_table"
    
    if df is None or df.empty:
        st.info("No data available to display.")
        return
    
    if use_pagination:
        # Initialize page number in session state
        if f"{key}_page" not in st.session_state:
            st.session_state[f"{key}_page"] = 0
        
        page = st.session_state[f"{key}_page"]
        total_pages = (len(df) - 1) // rows_per_page + 1
        
        # Calculate start and end indices
        start_idx = page * rows_per_page
        end_idx = min(start_idx + rows_per_page, len(df))
        
        # Display pagination controls
        col1, col2, col3 = st.columns([1, 3, 1])
        
        with col1:
            if st.button("< Previous", disabled=(page == 0), key=f"{key}_prev"):
                st.session_state[f"{key}_page"] -= 1
                st.rerun()
        
        with col2:
            st.markdown(f"<div style='text-align: center;'>Page {page+1} of {total_pages}</div>", unsafe_allow_html=True)
        
        with col3:
            if st.button("Next >", disabled=(page >= total_pages - 1), key=f"{key}_next"):
                st.session_state[f"{key}_page"] += 1
                st.rerun()
        
        # Display the paginated dataframe
        st.dataframe(df.iloc[start_idx:end_idx], use_container_width=True)
    else:
        # Display the full dataframe
        st.dataframe(df, use_container_width=True)

def format_currency(value, currency_symbol=None, decimal_places=2):
    """Format a number as currency with the specified symbol."""
    if value is None:
        return "-"
    
    if currency_symbol is None:
        currency_symbol = get_user_preferences().get("currency_symbol", "")
    
    return f"{currency_symbol} {value:,.{decimal_places}f}"

def format_percent(value, decimal_places=1):
    """Format a number as a percentage."""
    if value is None:
        return "-"
    
    return f"{value:,.{decimal_places}f}%"

def format_number(value, decimal_places=0):
    """Format a number with thousand separators."""
    if value is None:
        return "-"
    
    if decimal_places == 0:
        return f"{int(value):,}"
    else:
        return f"{value:,.{decimal_places}f}"