ImportError: libstdc++.so.6: cannot open shared object file: No such file or directory
Traceback:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/exec_code.py", line 121, in exec_func_with_error_handling
    result = func()
             ^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 648, in code_to_exec
    exec(code, module.__dict__)
File "/home/<USER>/workspace/app.py", line 7, in <module>
    from app_pages import overview, purchase_analytics, upload, settings, unified_sales_analytics, pricing, user_management, ai_analytics, content_creation
File "/home/<USER>/workspace/app_pages/overview.py", line 3, in <module>
    from utils import (
File "/home/<USER>/workspace/utils.py", line 1, in <module>
    import pandas as pd
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/__init__.py", line 49, in <module>
    from pandas.core.api import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/api.py", line 47, in <module>
    from pandas.core.groupby import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/groupby/__init__.py", line 1, in <module>
    from pandas.core.groupby.generic import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/groupby/generic.py", line 68, in <module>
    from pandas.core.frame import DataFrame
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/frame.py", line 149, in <module>
    from pandas.core.generic import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/generic.py", line 193, in <module>
    from pandas.core.window import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/window/__init__.py", line 1, in <module>
    from pandas.core.window.ewm import (
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/pandas/core/window/ewm.py", line 11, in <module>
    import pandas._libs.window.aggregations as window_aggregations