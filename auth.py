"""
Authentication module for ERP Analytics Dashboard.
Handles user authentication, session management, and role-based access control.
"""

import streamlit as st
import bcrypt
import datetime
from database import get_session, close_session
from models import User
from config import config
from sqlalchemy import and_

class AuthManager:
    """Handles all authentication operations."""
    
    @staticmethod
    def is_auth_required():
        """Check if authentication is required based on environment."""
        # Skip auth only in Replit development mode (not deployed)
        if config.IS_REPLIT and not config.IS_REPLIT_DEPLOYMENT:
            return False
        
        # Always require auth in deployments (Replit, AWS, Docker, etc.)
        return True
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using bcrypt."""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """Verify a password against its hash."""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    @staticmethod
    def create_user(username: str, password: str, role: str = 'user') -> bool:
        """Create a new user in the database."""
        session = get_session()
        try:
            # Check if user already exists
            existing_user = session.query(User).filter(User.username == username).first()
            if existing_user:
                return False
            
            # Create new user
            password_hash = AuthManager.hash_password(password)
            new_user = User(
                username=username,
                password_hash=password_hash,
                role=role,
                created_date=datetime.datetime.now(),
                is_active=True
            )
            
            session.add(new_user)
            session.commit()
            return True
            
        except Exception as e:
            session.rollback()
            st.error(f"Error creating user: {str(e)}")
            return False
        finally:
            close_session(session)
    
    @staticmethod
    def authenticate_user(username: str, password: str) -> dict:
        """Authenticate user credentials and return user info."""
        session = get_session()
        try:
            user = session.query(User).filter(
                and_(User.username == username, User.is_active == True)
            ).first()
            
            if user and AuthManager.verify_password(password, user.password_hash):
                # Update last login
                user.last_login = datetime.datetime.now()
                session.commit()
                
                return {
                    'success': True,
                    'user_id': user.id,
                    'username': user.username,
                    'role': user.role
                }
            else:
                return {'success': False, 'message': 'Invalid username or password'}
                
        except Exception as e:
            return {'success': False, 'message': f'Authentication error: {str(e)}'}
        finally:
            close_session(session)
    
    @staticmethod
    def get_all_users():
        """Get list of all users for admin management."""
        session = get_session()
        try:
            users = session.query(User).filter(User.is_active == True).all()
            return [{
                'id': user.id,
                'username': user.username,
                'role': user.role,
                'created_date': user.created_date,
                'last_login': user.last_login
            } for user in users]
        except Exception as e:
            st.error(f"Error fetching users: {str(e)}")
            return []
        finally:
            close_session(session)
    
    @staticmethod
    def delete_user(username: str) -> bool:
        """Delete a user (soft delete by setting is_active=False)."""
        session = get_session()
        try:
            user = session.query(User).filter(User.username == username).first()
            if user:
                user.is_active = False
                session.commit()
                return True
            return False
        except Exception as e:
            st.error(f"Error deleting user: {str(e)}")
            return False
        finally:
            close_session(session)
    
    @staticmethod
    def reset_password(username: str, new_password: str) -> bool:
        """Reset a user's password."""
        session = get_session()
        try:
            user = session.query(User).filter(User.username == username).first()
            if user:
                user.password_hash = AuthManager.hash_password(new_password)
                session.commit()
                return True
            return False
        except Exception as e:
            st.error(f"Error resetting password: {str(e)}")
            return False
        finally:
            close_session(session)
    
    @staticmethod
    def ensure_admin_exists():
        """Ensure at least one admin user exists. Create default if none found."""
        session = get_session()
        try:
            admin_count = session.query(User).filter(
                and_(User.role == 'admin', User.is_active == True)
            ).count()
            
            if admin_count == 0:
                # Create default admin user
                default_password = "admin123"  # Should be changed on first login
                AuthManager.create_user("admin", default_password, "admin")
                st.info("Default admin user created: username='admin', password='admin123' - Please change this password!")
                
        except Exception as e:
            st.error(f"Error checking admin users: {str(e)}")
        finally:
            close_session(session)


def require_auth():
    """Decorator function to require authentication for pages."""
    if not AuthManager.is_auth_required():
        # Development mode - skip authentication
        st.session_state.authenticated = True
        st.session_state.user_role = 'admin'  # Default to admin in dev mode
        st.session_state.username = 'developer'
        return True
    
    # Check if user is already authenticated
    if st.session_state.get('authenticated', False):
        return True
    
    # Show login form
    show_login_form()
    return False


def require_admin():
    """Check if current user has admin role."""
    if not require_auth():
        return False
    
    if st.session_state.get('user_role') != 'admin':
        st.error("Access denied. Admin privileges required.")
        return False
    
    return True


def show_login_form():
    """Display the login form."""
    st.title("🔐 ERP Analytics Dashboard")
    st.subheader("Please log in to continue")
    
    # Ensure admin user exists
    AuthManager.ensure_admin_exists()
    
    with st.form("login_form"):
        username = st.text_input("Username", key="login_username", help="Enter your username")
        password = st.text_input("Password", type="password", key="login_password", help="Enter your password")
        submit_button = st.form_submit_button("Login")
        
        # Add HTML to help browsers recognize this as a login form
        st.markdown("""
        <script>
        // Help browsers recognize login form for password saving
        document.addEventListener('DOMContentLoaded', function() {
            const usernameInput = document.querySelector('input[aria-label="Username"]');
            const passwordInput = document.querySelector('input[aria-label="Password"]');
            const form = document.querySelector('form[data-testid="stForm"]');
            
            if (usernameInput) {
                usernameInput.setAttribute('name', 'username');
                usernameInput.setAttribute('autocomplete', 'username');
                usernameInput.setAttribute('id', 'username');
            }
            
            if (passwordInput) {
                passwordInput.setAttribute('name', 'password');
                passwordInput.setAttribute('autocomplete', 'current-password');
                passwordInput.setAttribute('id', 'password');
            }
            
            if (form) {
                form.setAttribute('name', 'login');
            }
        });
        </script>
        """, unsafe_allow_html=True)
        
        if submit_button:
            if username and password:
                auth_result = AuthManager.authenticate_user(username, password)
                
                if auth_result['success']:
                    st.session_state.authenticated = True
                    st.session_state.user_id = auth_result['user_id']
                    st.session_state.username = auth_result['username']
                    st.session_state.user_role = auth_result['role']
                    st.success("Login successful!")
                    st.rerun()
                else:
                    st.error(auth_result['message'])
            else:
                st.error("Please enter both username and password")
    
    # Development mode indicator
    if not AuthManager.is_auth_required():
        st.info("🔧 Development Mode: Authentication bypassed")


def logout():
    """Log out the current user."""
    st.session_state.authenticated = False
    st.session_state.user_id = None
    st.session_state.username = None
    st.session_state.user_role = None
    st.rerun()


def get_current_user():
    """Get current user information."""
    return {
        'username': st.session_state.get('username'),
        'role': st.session_state.get('user_role'),
        'user_id': st.session_state.get('user_id')
    }