﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProductQuantities" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="ProductQuantities" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ProductQuantity">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Warehouse" type="xs:string" minOccurs="0" />
              <xs:element name="WarehouseName" type="xs:string" minOccurs="0" />
              <xs:element name="WarehouseSite" type="xs:string" minOccurs="0" />
              <xs:element name="Product" type="xs:string" minOccurs="0" />
              <xs:element name="ProductCode" type="xs:string" minOccurs="0" />
              <xs:element name="Quantity" type="xs:string" minOccurs="0" />
              <xs:element name="StrictAllocate" type="xs:string" minOccurs="0" />
              <xs:element name="NonStrictAllocate" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>