import streamlit as st
import os

st.title("Environment Test")
st.write("Testing basic Streamlit functionality...")

# Test environment variables
st.write("Environment check:")
st.write(f"- Python path: {os.sys.executable}")
st.write(f"- Working directory: {os.getcwd()}")

# Test basic imports that don't require pandas/numpy
try:
    import requests
    st.success("✓ requests import successful")
except Exception as e:
    st.error(f"✗ requests import failed: {e}")

try:
    import openai
    st.success("✓ openai import successful")
except Exception as e:
    st.error(f"✗ openai import failed: {e}")

try:
    import sqlalchemy
    st.success("✓ sqlalchemy import successful")
except Exception as e:
    st.error(f"✗ sqlalchemy import failed: {e}")

# Test pandas/numpy specifically
try:
    import pandas as pd
    import numpy as np
    st.success("✓ pandas/numpy import successful")
except Exception as e:
    st.error(f"✗ pandas/numpy import failed: {e}")
    st.text(str(e))

st.write("Basic Streamlit functionality is working if you can see this page.")