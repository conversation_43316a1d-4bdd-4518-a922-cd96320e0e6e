<?xml version="1.0" encoding="UTF-8" ?>
<CustomersUp>
	<Customer>
		<id>1231</id> --Symbol be<PERSON><PERSON> (Customer.Id)
		<code>P12776</code> --<PERSON><PERSON><PERSON> (Customer.Code)
		<customerstatus>0</customerstatus> --A partner vev<PERSON><PERSON><PERSON><PERSON> szerepel-e (Customer.CustomerStatus)
		<supplierstatus>1</supplierstatus> --A partner sz<PERSON>ll<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> szerepel-e (Customer.SupplierStatus)
		<name><PERSON><PERSON>, a Nagy Varázsló</name> --<PERSON><PERSON><PERSON>ve (Customer.Name)
		<searchname><PERSON><PERSON>, a Nagy Varázsló</searchname> --<PERSON><PERSON><PERSON> (Customer.SearchName)
		<customercategory>Pintinox/Pohár</customercategory> --Vev<PERSON>csoport (CustomerCategory.Name)
		<suppliercategory>Pintinox/Pohár</suppliercategory> --Sz<PERSON>ll<PERSON>tócsoport (SupplierCategory.Name)
		<currency>HUF</currency> --<PERSON><PERSON><PERSON><PERSON><PERSON> (Currency.Name)
		<invoicecountry>Hun</invoicecountry> --Számlázási cím - ország (Customer.InvoiceCountry)
		<invoiceregion>Baranya</invoiceregion> --Számlázási cím - megye (Customer.InvoiceRegion)
		<invoicezip>7624-2</invoicezip> --Számlázási cím - irányítószám (Customer.InvoiceZip)
		<invoicecity>Pécs</invoicecity> --Számlázási cím - város (Customer.InvoiceCity)
		<invoicestreet>Báthory István utca</invoicestreet> --Számlázási cím - utca (Customer.InvoiceStreet)
		<invoicehousenumber>20/a</invoicehousenumber> --Számlázási cím - házszám (Customer.InvoiceHouseNumber)
		<mailcountry>Hun</mailcountry> --Levelezési cím - ország (Customer.MailCountry)
		<mailregion>Baranya</mailregion> --Levelezési cím - megye (Customer.MailRegion)
		<mailname>Könyvelő</mailname> --Levelezési név (Customer.MailName)
		<mailzip>7624-2</mailzip> --Levelezési cím - irányítószám (Customer.MailZip)
		<mailcity>Pécs</mailcity> --Levelezési cím - város (Customer.MailCity)
		<mailstreet>Báthory István utca</mailstreet> --Levelezési cím - utca (Customer.MailStreet)
		<mailhousenumber>20/a</mailhousenumber> --Levelezési cím - házszám (Customer.MailHouseNumber)
		<paymentmethod>Átutalás</paymentmethod> --Fizetési mód (PaymentMethod.Name)
		<paymentmethodtoleranceday>20</paymentmethodtoleranceday>
		<pricecategory>3</pricecategory> --Vevőhöz tartozó árkategória Symbol belső azonosítója (Customer.PriceCategory)
		<pricecategoryname>Lista ár</pricecategoryname> --Árkategória neve (PriceCategory.Name)
		<discountpercent>13.5</discountpercent> --Kedvezmény % (Customer.DiscountPercent)
		<transportmode>Személyes átvétel</transportmode>  --Átvétel módja (TransportMode.Name)
		<taxnumber>********-2-41</taxnumber> --Adószám (Customer.TaxNumber)
		<eutaxnumber>HU********-2-41</eutaxnumber> --Vevő közösségi adószám (Customer.EUTaxNumber)
		<bankaccount>********-********</bankaccount> --Vevő bankszámlája (Customer.BankAccount)
		<bankaccountiban>HU********-********</bankaccountiban> --Vevp IBAN száma (Customer.BankAccountIBAN)
		<bankname>OTP Bank</bankname> --Vevő bankszámlájához tartozó bank neve (Customer.BankName)
		<bankswiftcode>OTPVHUHB</bankswiftcode> --Vevő bankszámlájához tartozó SWIFT kód (Customer.BankSwiftCode)
		<contactname>Balázs Piri Balázs</contactname> -- Kapcsolattartó neve (Customer.ContactName)
		<phone>70-785-4587</phone> --Vevő telefonszáma (Customer.Phone)
		<fax>1-456-7989</fax> --Vevő faxszáma (Customer.Fax)
		<sms>70-4587854</sms> --Vevő SMS száma (Customer.Sms)
		<email><EMAIL></email> --Vevő email címe (Customer.Email)
		<webusername>username</webusername> --Vevő web felhasználóneve (Customer.WebUsername)
		<webpassword>password</webpassword> --Vevő web jelszaba (Customer.WebPassword)
		<iscompany>0</iscompany> -- Cég/Magánszemély (Customer.IsCompany)
		<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> --Vevő megjegyzés (Customer.Comment)
		<deleted>0</deleted> --A vevő törölt állapotú-e (Customer.Deleted)
		<strexa>aaa</strexa> -- Vevő egyedi szöveges mező (Customer.StrExA)
		<strexb>bbb</strexb> -- Vevő egyedi szöveges mező (Customer.StrExB)
		<strexc>ccc</strexc> -- Vevő egyedi szöveges mező (Customer.StrExC)
		<strexd>ddd</strexd> -- Vevő egyedi szöveges mező (Customer.StrExD)
		<dateexa>2010-07-12</dateexa> -- Vevő egyedi dátum mező (Customer.DateExA)
		<dateexb>2010-07-12</dateexb> -- Vevő egyedi dátum mező (Customer.DateExB)
		<numexa>111</numexa> -- Vevő egyedi szám mező (Customer.NumExA)
		<numexb>222</numexb> -- Vevő egyedi szám mező (Customer.NumExB)
		<numexc>333</numexc> -- Vevő egyedi szám mező (Customer.NumExC)
		<boolexa>0</boolexa> -- Vevő egyedi logikai mező (Customer.BoolExA)
		<boolexb>1</boolexb> -- Vevő egyedi logikai mező (Customer.BoolExB)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Vevő egyedi kiválasztó mező (Customer.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Vevő egyedi kiválasztó mező (Customer.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Vevő egyedi kiválasztó mező (Customer.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Vevő egyedi kiválasztó mező (Customer.LookupExD)
		<customeraddresses> 
			<customeraddress>
				<preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>AB123</companytaxnumber> -- Telephely adószáma (CustomerAddress.TaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
			<customeraddress>
			    <preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>AB123</companytaxnumber> -- Telephely adószáma (CustomerAddress.TaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
		</customeraddresses>
		<customercontacts>
			<customercontact>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<id>12317</id> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
				<customercontact>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<id>12317</id> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
		</customercontacts>
	</Customer>
		<Customer>
		<id>1231</id> --Symbol belső azonosító (Customer.Id)
		<code>P12776</code> --Vevő kódja (Customer.Code)
		<customerstatus>0</customerstatus> --A partner vevőként szerepel-e (Customer.CustomerStatus)
		<supplierstatus>1</supplierstatus> --A partner szállítóként szerepel-e (Customer.SupplierStatus)
		<name>Óz, a Nagy Varázsló</name> --Vevő neve (Customer.Name)
		<searchname>Óz, a Nagy Varázsló</searchname> --Vevő keresőnév (Customer.SearchName)
		<customercategory>Pintinox/Pohár</customercategory> --Vevőcsoport (CustomerCategory.Name)
		<suppliercategory>Pintinox/Pohár</suppliercategory> --Szállítócsoport (SupplierCategory.Name)
		<currency>HUF</currency> --Pénznem (Currency.Name)
		<invoicecountry>Hun</invoicecountry> --Számlázási cím - ország (Customer.InvoiceCountry)
		<invoiceregion>Baranya</invoiceregion> --Számlázási cím - megye (Customer.InvoiceRegion)
		<invoicezip>7624-2</invoicezip> --Számlázási cím - irányítószám (Customer.InvoiceZip)
		<invoicecity>Pécs</invoicecity> --Számlázási cím - város (Customer.InvoiceCity)
		<invoicestreet>Báthory István utca</invoicestreet> --Számlázási cím - utca (Customer.InvoiceStreet)
		<invoicehousenumber>20/a</invoicehousenumber> --Számlázási cím - házszám (Customer.InvoiceHouseNumber)
		<mailcountry>Hun</mailcountry> --Levelezési cím - ország (Customer.MailCountry)
		<mailregion>Baranya</mailregion> --Levelezési cím - megye (Customer.MailRegion)
		<mailname>Könyvelő</mailname> --Levelezési név (Customer.MailName)
		<mailzip>7624-2</mailzip> --Levelezési cím - irányítószám (Customer.MailZip)
		<mailcity>Pécs</mailcity> --Levelezési cím - város (Customer.MailCity)
		<mailstreet>Báthory István utca</mailstreet> --Levelezési cím - utca (Customer.MailStreet)
		<mailhousenumber>20/a</mailhousenumber> --Levelezési cím - házszám (Customer.MailHouseNumber)
		<paymentmethod>Átutalás</paymentmethod> --Fizetési mód (PaymentMethod.Name)
		<paymentmethodtoleranceday>20</paymentmethodtoleranceday>
		<pricecategory>3</pricecategory> --Vevőhöz tartozó árkategória Symbol belső azonosítója (Customer.PriceCategory)
		<pricecategoryname>Lista ár</pricecategoryname> --Árkategória neve (PriceCategory.Name)
		<discountpercent>13.5</discountpercent> --Kedvezmény % (Customer.DiscountPercent)
		<transportmode>Személyes átvétel</transportmode>  --Átvétel módja (TransportMode.Name)
		<taxnumber>********-2-41</taxnumber> --Adószám (Customer.TaxNumber)
		<eutaxnumber>HU********-2-41</eutaxnumber> --Vevő közösségi adószám (Customer.EUTaxNumber)
		<bankaccount>********-********</bankaccount> --Vevő bankszámlája (Customer.BankAccount)
		<bankaccountiban>HU********-********</bankaccountiban> --Vevp IBAN száma (Customer.BankAccountIBAN)
		<bankname>OTP Bank</bankname> --Vevő bankszámlájához tartozó bank neve (Customer.BankName)
		<bankswiftcode>OTPVHUHB</bankswiftcode> --Vevő bankszámlájához tartozó SWIFT kód (Customer.BankSwiftCode)
		<contactname>Balázs Piri Balázs</contactname> -- Kapcsolattartó neve (Customer.ContactName)
		<phone>70-785-4587</phone> --Vevő telefonszáma (Customer.Phone)
		<fax>1-456-7989</fax> --Vevő faxszáma (Customer.Fax)
		<sms>70-4587854</sms> --Vevő SMS száma (Customer.Sms)
		<email><EMAIL></email> --Vevő email címe (Customer.Email)
		<webusername>username</webusername> --Vevő web felhasználóneve (Customer.WebUsername)
		<webpassword>password</webpassword> --Vevő web jelszaba (Customer.WebPassword)
		<iscompany>0</iscompany> -- Cég/Magánszemély (Customer.IsCompany)
		<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> --Vevő megjegyzés (Customer.Comment)
		<deleted>0</deleted> --A vevő törölt állapotú-e (Customer.Deleted)
		<strexa>aaa</strexa> -- Vevő egyedi szöveges mező (Customer.StrExA)
		<strexb>bbb</strexb> -- Vevő egyedi szöveges mező (Customer.StrExB)
		<strexc>ccc</strexc> -- Vevő egyedi szöveges mező (Customer.StrExC)
		<strexd>ddd</strexd> -- Vevő egyedi szöveges mező (Customer.StrExD)
		<dateexa>2010-07-12</dateexa> -- Vevő egyedi dátum mező (Customer.DateExA)
		<dateexb>2010-07-12</dateexb> -- Vevő egyedi dátum mező (Customer.DateExB)
		<numexa>111</numexa> -- Vevő egyedi szám mező (Customer.NumExA)
		<numexb>222</numexb> -- Vevő egyedi szám mező (Customer.NumExB)
		<numexc>333</numexc> -- Vevő egyedi szám mező (Customer.NumExC)
		<boolexa>0</boolexa> -- Vevő egyedi logikai mező (Customer.BoolExA)
		<boolexb>1</boolexb> -- Vevő egyedi logikai mező (Customer.BoolExB)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Vevő egyedi kiválasztó mező (Customer.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Vevő egyedi kiválasztó mező (Customer.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Vevő egyedi kiválasztó mező (Customer.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Vevő egyedi kiválasztó mező (Customer.LookupExD)
		<customeraddresses> 
			<customeraddress>
				<preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>AB123</companytaxnumber> -- Telephely adószáma (CustomerAddress.TaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
			<customeraddress>
			    <preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>AB123</companytaxnumber> -- Telephely adószáma (CustomerAddress.TaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
		</customeraddresses>
		<customercontacts>
			<customercontact>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<id>12317</id> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
				<customercontact>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<id>12317</id> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
		</customercontacts>
	</Customer>
</CustomersUp>