FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY pyproject.toml uv.lock ./

# Install Python dependencies
RUN pip install --no-cache-dir uv && \
    uv pip install --no-deps -e .

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p assets

# Make port 5000 available
EXPOSE 5000

# Create config directory for Streamlit
RUN mkdir -p .streamlit
RUN echo '\
[server]\n\
headless = true\n\
port = 5000\n\
address = "0.0.0.0"\n\
enableCORS = false\n\
enableXsrfProtection = true\n\
' > .streamlit/config.toml

# Run the application
CMD ["streamlit", "run", "app.py"]