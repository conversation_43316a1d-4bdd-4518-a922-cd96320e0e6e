import streamlit as st
import pandas as pd
import io
import os
import time
from utils import (
    parse_excel_data, 
    save_buyers_data, 
    save_products_data,
    save_product_categories_data,
    save_prices_data,
    save_stock_data,
    save_sales_data,
    save_purchases_data,
    save_competitor_scrape_data,
    save_brand_settings_data,
    get_sample_excel_files,
    open_excel_file
)

def upload_file(file_type):
    """Upload and process an Excel file based on its type."""
    st.subheader(f"Upload {file_type.title()} File")
    
    # Check if sample files are available
    sample_files = get_sample_excel_files()
    has_sample = file_type in sample_files
    
    # Offer tabs for uploading a new file or using a sample file
    tab1, tab2 = st.tabs(["Upload New File", "Use Sample Data"])
    
    with tab1:
        uploaded_file = st.file_uploader(f"Choose a {file_type} Excel file", type=["xlsx", "xls"])
        
        if uploaded_file is not None:
            st.write("File uploaded successfully!")
            
            # Show preview of the data
            preview_df = preview_data(uploaded_file, file_type)
            
            if preview_df is None:
                st.error("Failed to preview the file. Please check the file format.")
                return
                
            # Import options
            st.subheader("Import Options")
            
            # Data handling options for existing records
            import_mode = "update"
            selected_columns = None  # By default, no column selection
            
            # Special handling for sales and purchase data
            if file_type in ['sales', 'purchases']:
                # Default for sales and purchases is date-based replacement
                import_mode = "date_replace"
                
                # Find unique dates in the uploaded file
                try:
                    # Check for and remove summary rows (rows without Termékkód)
                    if 'Termékkód' in preview_df.columns:
                        df_dates = preview_df.dropna(subset=['Termékkód'])
                    else:
                        df_dates = preview_df
                    
                    # Extract unique dates based on file type and check for existing records
                    date_column = None
                    date_field = None
                    model_class = None
                    record_type = ""
                    
                    if file_type == 'sales':
                        date_column = 'Értékesítés kelte'
                        record_type = 'sales'
                        from models import Sale
                        model_class = Sale
                        date_field = Sale.sale_date
                    elif file_type == 'purchases':
                        date_column = 'Kelt'
                        record_type = 'purchase'
                        from models import Purchase
                        model_class = Purchase
                        date_field = Purchase.invoice_date
                    
                    if date_column and date_column in df_dates.columns:
                        unique_dates = pd.to_datetime(df_dates[date_column].dropna()).dt.date.unique()
                        
                        # Check if there are existing records with these dates
                        if len(unique_dates) > 0:
                            from database import get_session
                            from sqlalchemy import func
                            
                            session = get_session()
                            if session:
                                try:
                                    # Find which dates already have data
                                    existing_dates = []
                                    for date in unique_dates:
                                        count = session.query(func.count(model_class.id)).filter(func.date(date_field) == date).scalar()
                                        if count > 0:
                                            existing_dates.append((date, count))
                                    
                                    session.close()
                                    
                                    # Show warning if dates will be overwritten
                                    if existing_dates:
                                        st.warning(f"⚠️ The following dates in your import already have {record_type} data in the database:")
                                        date_info = ""
                                        for date, count in existing_dates:
                                            date_info += f"- {date.strftime('%Y-%m-%d')}: {count} existing records\n"
                                        st.info(date_info)
                                        st.warning("These records will be replaced with the new data.")
                                        
                                        # Confirm overwrite
                                        st.info(f"This import will delete all existing {record_type} records for the dates in your import file and replace them with the new data.")
                                except Exception as e:
                                    session.close()
                                    st.error(f"Error checking existing {record_type} dates: {str(e)}")
                except Exception as e:
                    st.error(f"Error processing dates in {file_type} file: {str(e)}")
            
            # Column selection and import modes for Products, Buyers, Prices, and Competitor Scrape
            if file_type in ['products', 'buyers', 'prices', 'competitor_scrape']:
                # Standard import mode options
                import_mode = st.radio(
                    f"How to handle existing {file_type} records:",
                    ["update", "disregard", "delete"],
                    index=0,
                    help="Update: Modify existing records with new data, keep the rest unchanged.\n"
                         "Disregard: Skip existing records, only add new ones.\n"
                         "Delete: Remove all existing records before importing new data (caution!)."
                )
            elif file_type == 'product_categories':
                # Special handling for product categories
                import_mode = st.radio(
                    f"How to handle existing product category connections:",
                    ["update", "disregard", "delete"],
                    index=0,
                    help="Update: For each product ID, delete all existing categories and add the new ones.\n"
                         "Disregard: Keep existing categories, only add new ones.\n"
                         "Delete: Remove ALL existing category connections before importing new data (caution!)."
                )
                
                if import_mode == "update":
                    st.info("""
                    **Update Mode**: For each product ID in your import, all existing category connections will be deleted 
                    and replaced with the new ones. This ensures products have exactly the categories specified in your import file.
                    """)
                elif import_mode == "delete":
                    st.warning("""
                    **Delete Mode**: ALL existing product-category connections will be deleted before import.
                    Products not included in your import will have NO categories after import.
                    """)
            
            # Pricing-specific options
            if file_type == 'prices':
                currency = st.selectbox(
                    "Currency",
                    options=["HUF", "EUR", "USD"],
                    index=0
                )
            else:
                currency = "HUF"
            
            # Column mapping
            if file_type in ['products', 'buyers', 'product_categories', 'prices', 'competitor_scrape']:
                # Define the expected fields for each file type
                expected_fields = {}
                required_field = ""
                
                # Get the expected columns for this file type based on the model
                if file_type == 'products':
                    expected_fields = {
                        'Termékkód': 'Product ID (required)',
                        'Terméknév': 'Product Name',
                        'Gyártói csoport': 'Manufacturer Group',
                        'Termékcsoport': 'Product Group',
                        'Gyártó': 'Brand',
                        'Elsődleges szállító': 'Primary Supplier'
                    }
                    required_field = 'Termékkód'
                elif file_type == 'buyers':
                    expected_fields = {
                        'Vevőkód': 'Buyer ID (required)',
                        'Vevőnév': 'Buyer Name',
                        'Számlázási ország': 'Country',
                        'Vevőcsoport': 'Buyer Category',
                        'WEB hozzáférés': 'Web Access',
                        'Számlázási cím': 'Address'
                    }
                    required_field = 'Vevőkód'
                elif file_type == 'prices':
                    expected_fields = {
                        'Termékkód': 'Product ID (required)',
                        'Beszerzési ár': 'Purchase Price',
                        'Bolti ár': 'Store Price',
                        'Webes ár': 'Online Price',
                        'Export ár': 'Export Price',
                        'Lista ár': 'List Price'
                    }
                    required_field = 'Termékkód'
                elif file_type == 'product_categories':
                    expected_fields = {
                        'Termékkód': 'Product ID (required)',
                        'Webes termékcsoport': 'Category Name'
                    }
                    required_field = 'Termékkód'
                elif file_type == 'competitor_scrape':
                    expected_fields = {
                        'Termékkód': 'Product Code (required)',
                        'Thomann URL': 'Thomann URL',
                        'Thomann Price': 'Thomann Price',
                        'Muziker URL': 'Muziker URL',
                        'Muziker Price': 'Muziker Price',
                        'Árukereső URL': 'Árukereső URL',
                        'Árgép URL': 'Árgép URL',
                        'R55 Price': 'R55 Price',
                        'R55 Stock': 'R55 Stock',
                        'Kytary Price': 'Kytary Price',
                        'Kytary Stock': 'Kytary Stock',
                        'Mezzo Price': 'Mezzo Price',
                        'Mezzo Stock': 'Mezzo Stock',
                        'Allegro Price': 'Allegro Price',
                        'Allegro Stock': 'Allegro Stock',
                        'Páko Price': 'Páko Price',
                        'Páko Stock': 'Páko Stock',
                        'Mango Price': 'Mango Price',
                        'Mango Stock': 'Mango Stock',
                        'Pláza Price': 'Pláza Price',
                        'Pláza Stock': 'Pláza Stock',
                        'Diszkont Price': 'Diszkont Price',
                        'Diszkont Stock': 'Diszkont Stock',
                        'Hitspace Price': 'Hitspace Price',
                        'Hitspace Stock': 'Hitspace Stock'
                    }
                    required_field = 'Termékkód'
                
                # Display the column selection interface
                st.subheader("Column Mapping")
                st.write("Match columns from your file to the required database fields. Only the identifier field is required.")
                
                # Dictionary to store column mappings
                column_mapping = {}
                
                # Get file columns as options - add a "None" option for fields we don't want to import
                file_columns = [''] + list(preview_df.columns)
                
                # Make each field a selectbox to map to a file column
                # Required fields are highlighted and preselected if they match
                for field_name, field_label in expected_fields.items():
                    # Try to find a matching column in the file
                    default_idx = 0  # Default to empty/None
                    
                    # Create alternative names for fields that might have different representations
                    alternative_names = {
                        'Számlázási ország': ['ország', 'country', 'orszag', 'szamlazasi orszag'],
                        'WEB hozzáférés': ['web', 'online', 'hozzáférés', 'access', 'hozzaferes', 'web access', 'web hozzaferes'],
                        'Beszerzési ár': ['beszerzési', 'beszerzesi', 'besz ár', 'besz ar', 'purchase', 'buying', 'cost'],
                        'Bolti ár': ['bolti', 'store', 'shop', 'retail', 'bolt ár', 'bolt ar', 'store price'],
                        'Webes ár': ['webes', 'web', 'online', 'web ár', 'web ar', 'online price', 'web price'],
                        'Export ár': ['export', 'exp', 'exp ár', 'exp ar', 'export price'],
                        'Lista ár': ['lista', 'list', 'msrp', 'list price', 'lista ár', 'lista ar', 'listaár'],
                        'Kategória': ['kategoria', 'category', 'term.kat', 'term kat', 'termékkategória', 'termekkategoria', 'product category', 'cat', 'kat']
                    }
                    
                    # Specific handling for special fields to ensure proper matching
                    if field_name == 'Számlázási ország':
                        # Explicit search for country columns
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            # Look for exact keyword matches first
                            if col.lower() == 'ország' or col.lower() == 'country' or 'számlázási ország' in col.lower():
                                default_idx = i
                                break
                                
                            # More general matching
                            if 'orszag' in col.lower() or 'country' in col.lower():
                                default_idx = i
                                break
                    
                    elif field_name == 'WEB hozzáférés':
                        # Explicit search for web access columns
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            # Look for specific keywords
                            if col.lower() == 'web access' or 'web hozzáférés' in col.lower() or 'web hozzaferes' in col.lower():
                                default_idx = i
                                break
                                
                            # More general matching
                            if 'web' in col.lower() and ('access' in col.lower() or 'hozzaf' in col.lower()):
                                default_idx = i
                                break
                    
                    # Special handling for price fields
                    elif field_name in ['Beszerzési ár', 'Bolti ár', 'Webes ár', 'Export ár', 'Lista ár']:
                        price_field_key = field_name.replace(' ár', '').lower()
                        
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            col_lower = col.lower()
                            
                            # Look for exact matches first
                            if col_lower == field_name.lower():
                                default_idx = i
                                break
                                
                            # Handle various price column formats
                            if price_field_key in col_lower and ('ár' in col_lower or 'ar' in col_lower or 'price' in col_lower):
                                default_idx = i
                                break
                                
                            # Handle English translations
                            if field_name == 'Beszerzési ár' and ('purchase' in col_lower or 'cost' in col_lower or 'buying' in col_lower):
                                default_idx = i
                                break
                            elif field_name == 'Bolti ár' and ('store' in col_lower or 'retail' in col_lower or 'shop' in col_lower):
                                default_idx = i
                                break
                            elif field_name == 'Webes ár' and ('web' in col_lower or 'online' in col_lower):
                                default_idx = i
                                break
                            elif field_name == 'Export ár' and ('export' in col_lower or 'exp ' in col_lower):
                                default_idx = i
                                break
                            elif field_name == 'Lista ár' and ('list' in col_lower or 'msrp' in col_lower or 'srp' in col_lower):
                                default_idx = i
                                break
                    
                    # Special handling for web category field
                    elif field_name == 'Webes termékcsoport':
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            col_lower = col.lower()
                            
                            # Look for exact matches first
                            if col_lower == field_name.lower() or col_lower == 'web category':
                                default_idx = i
                                break
                                
                            # Handle various web category column formats
                            if ('web' in col_lower or 'online' in col_lower) and ('termékcsop' in col_lower or 'termekcsop' in col_lower or 'cat' in col_lower or 'kategória' in col_lower):
                                default_idx = i
                                break
                                
                            # Try with English terms
                            if 'web' in col_lower and 'category' in col_lower:
                                default_idx = i
                                break
                    
                    # Special handling for regular category field
                    elif field_name == 'Kategória':
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            col_lower = col.lower()
                            
                            # Look for exact matches first
                            if col_lower == field_name.lower() or col_lower == 'category':
                                default_idx = i
                                break
                                
                            # Try with different spellings and formats
                            if 'kategoria' in col_lower or 'kategória' in col_lower or 'category' in col_lower:
                                default_idx = i
                                break
                    
                    # For all other fields, find exact or close matches
                    else:
                        for i, col in enumerate(file_columns):
                            if not col:
                                continue
                                
                            # Special handling for Brand field - must be exact match to avoid confusion with "Gyártói csoport"
                            if field_name == 'Gyártó':
                                if col.lower() == 'gyártó' or col.lower() == 'brand' or col.lower() == 'manufacturer':
                                    default_idx = i
                                    break
                                continue
                            
                            # Look for exact field name match or field name in column
                            if col.lower() == field_name.lower() or field_name.lower() in col.lower():
                                default_idx = i
                                break
                                
                            # English-Hungarian translation for standard fields
                            if field_name == 'Termékkód' and ('product id' in col.lower() or 'product code' in col.lower() or 'item code' in col.lower() or 'item id' in col.lower() or 'sku' in col.lower()):
                                default_idx = i
                                break
                            elif field_name == 'Terméknév' and ('product name' in col.lower() or 'item name' in col.lower() or 'description' in col.lower()):
                                default_idx = i
                                break
                            elif field_name == 'Vevőkód' and ('customer id' in col.lower() or 'customer code' in col.lower() or 'buyer id' in col.lower() or 'buyer code' in col.lower()):
                                default_idx = i
                                break
                            elif field_name == 'Vevőnév' and ('customer name' in col.lower() or 'buyer name' in col.lower()):
                                default_idx = i
                                break
                    
                    # Create the column mapping selectbox with a distinctive style for required fields
                    if field_name == required_field:
                        # Highlight required fields
                        st.markdown(f"**{field_label}** :red[*]")
                        selected_col = st.selectbox(
                            f"Map to column in file",
                            options=file_columns,
                            index=default_idx,
                            key=f"map_{field_name}",
                            label_visibility="collapsed"
                        )
                    else:
                        st.write(field_label)
                        selected_col = st.selectbox(
                            f"Map to column in file",
                            options=file_columns,
                            index=default_idx,
                            key=f"map_{field_name}",
                            label_visibility="collapsed"
                        )
                    
                    # Only add non-empty selections to the mapping
                    if selected_col:
                        column_mapping[field_name] = selected_col
                
                # Check if required field is mapped
                if required_field not in column_mapping:
                    st.error(f"The required field '{required_field}' must be mapped to a column in your file.")
                    # Disable the upload button by setting a session state variable
                    st.session_state.mapping_valid = False
                else:
                    # Enable the upload button
                    st.session_state.mapping_valid = True
                    
                    # Show import summary
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("#### Fields To Import")
                        mapped_fields = ""
                        for field, col in column_mapping.items():
                            mapped_fields += f"- {field} ← {col}\n"
                        
                        st.markdown(mapped_fields)
                        
                    with col2:
                        st.markdown("#### Fields Not Imported")
                        missing_fields = ""
                        for field in expected_fields.keys():
                            if field not in column_mapping:
                                missing_fields += f"- {field}\n"
                        
                        if missing_fields:
                            st.markdown(missing_fields)
                        else:
                            st.markdown("All expected fields will be imported.")
                
                # Save the column mapping for use in the import function
                selected_columns = column_mapping
            
            # Process upload button
            upload_button = st.button("Save to Database", key="process_upload_btn")
            if upload_button:
                try:
                    # Display warning for delete mode
                    if import_mode == "delete":
                        st.warning(f"⚠️ WARNING: This will delete ALL existing {file_type} records! ⚠️")
                        proceed = st.button("I understand, proceed with deletion and import", key="confirm_delete")
                        if not proceed:
                            st.stop()
                    
                    if file_type in ['stock', 'prices', 'product_categories']:
                        # Check if products exist
                        from database import get_session
                        from models import Product
                        
                        session = get_session()
                        if session:
                            try:
                                product_count = session.query(Product).count()
                                session.close()
                                
                                if product_count == 0:
                                    st.warning("Please import products data first before importing stock/price/category data.")
                                    st.info("The correct order for importing data is: products → product_categories → prices → buyers → stock → sales → purchases")
                                    return
                            except Exception as e:
                                session.close()
                                st.error(f"Error checking products: {str(e)}")
                                return
                    
                    if file_type in ['sales']:
                        # Check if buyers and products exist
                        from database import get_session
                        from models import Product, Buyer
                        
                        session = get_session()
                        if session:
                            try:
                                product_count = session.query(Product).count()
                                buyer_count = session.query(Buyer).count()
                                session.close()
                                
                                if product_count == 0:
                                    st.warning("Please import products data first before importing sales data.")
                                    st.info("The correct order for importing data is: products → product_categories → prices → buyers → stock → sales → purchases")
                                    return
                                    
                                if buyer_count == 0:
                                    st.warning("Please import buyers data first before importing sales data.")
                                    st.info("The correct order for importing data is: products → product_categories → prices → buyers → stock → sales → purchases")
                                    return
                            except Exception as e:
                                session.close()
                                st.error(f"Error checking prerequisites: {str(e)}")
                                return
                    
                    # Show a progress message
                    progress_message = st.empty()
                    progress_message.info("Processing data... Please wait.")
                    
                    # Create progress bar
                    progress_bar = st.progress(0)
                    
                    # Get the file content as bytes
                    bytes_data = uploaded_file.getvalue()
                    bio = io.BytesIO(bytes_data)
                    
                    # Set up timing variables
                    start_time = time.time()
                    parsing_time = 0
                    db_time = 0
                    count = 0
                    
                    try:
                        # Parse the data from the Excel file
                        parsing_start = time.time()
                        df = parse_excel_data(bio, file_type, selected_columns)
                        parsing_time = time.time() - parsing_start
                        
                        if df is None or len(df) == 0:
                            progress_message.error("No valid data found in the file. Please check your Excel file and try again.")
                            return
                        
                        # Update progress
                        progress_bar.progress(10)
                        progress_message.info(f"Data parsed successfully. Importing {len(df)} records...")
                        
                        # Import the data based on the file type
                        db_start = time.time()
                        if file_type == 'buyers':
                            count = save_buyers_data(df, import_mode)
                        elif file_type == 'products':
                            count = save_products_data(df, import_mode)
                        elif file_type == 'product_categories':
                            count = save_product_categories_data(df, import_mode)
                        elif file_type == 'prices':
                            count = save_prices_data(df, currency, import_mode)
                        elif file_type == 'stock':
                            count = save_stock_data(df, import_mode)
                        elif file_type == 'sales':
                            count = save_sales_data(df, import_mode)
                        elif file_type == 'purchases':
                            count = save_purchases_data(df, import_mode)
                        elif file_type == 'competitor_scrape':
                            count = save_competitor_scrape_data(df, import_mode)
                        else:
                            progress_message.error(f"Unknown file type: {file_type}")
                            return
                        
                        db_time = time.time() - db_start
                        
                    except Exception as e:
                        progress_message.error(f"Error processing data: {str(e)}")
                        st.exception(e)
                        return
                    
                    # Calculate total time
                    total_time = time.time() - start_time
                    
                    # Update progress to 100%
                    progress_bar.progress(100)
                    
                    # Clear the progress message
                    progress_message.empty()
                    
                    # Create a summary box
                    st.markdown("### Import Summary")
                    summary_cols = st.columns(3)
                    
                    with summary_cols[0]:
                        st.metric(label="Records Processed", value=f"{count:,}")
                    with summary_cols[1]:
                        st.metric(label="Total Time", value=f"{total_time:.2f} sec")
                    with summary_cols[2]:
                        if count > 0 and db_time > 0:
                            records_per_sec = count / db_time
                            st.metric(label="Import Speed", value=f"{records_per_sec:.1f} rec/sec")
                        else:
                            st.metric(label="Import Speed", value="0 rec/sec")
                    
                    # Show detailed timing information
                    timing_cols = st.columns(2)
                    with timing_cols[0]:
                        st.info(f"⏱️ **Timing Details:**\n"
                                f"- Parsing time: {parsing_time:.2f} sec\n"
                                f"- Database import: {db_time:.2f} sec\n"
                                f"- Total time: {total_time:.2f} sec")
                                
                    with timing_cols[1]:
                        # Show file-specific tips
                        if file_type == 'products':
                            st.info("💡 **Tip:** Now that you've imported products, you can add:\n"
                                   "- Product categories\n"
                                   "- Prices\n"
                                   "- Stock information")
                        elif file_type == 'buyers':
                            st.info("💡 **Tip:** Now you can import sales records for these buyers.")
                        elif file_type == 'sales' or file_type == 'purchases':
                            st.info("💡 **Tip:** Visit the Analytics pages to view reports based on your data.")
                    
                    # Show success message
                    if count > 0:
                        st.success(f"✅ Successfully processed {count:,} {file_type} records!")
                        
                        # Show a more detailed success message with both icons and emojis for better visibility
                        st.markdown("""
                        <div style="padding: 15px; border-radius: 5px; border-left: 5px solid #4CAF50; background-color: #f8f8f8; margin: 20px 0;">
                            <h3 style="color: #4CAF50; margin-top: 0;">✅ Import Successfully Completed</h3>
                            <p>The data has been imported into the database and is now available for use in the application.</p>
                            <p>You can now navigate to the analytics pages to view reports based on your imported data.</p>
                        </div>
                        """, unsafe_allow_html=True)
                    else:
                        st.warning("No records were imported. Please check your file and try again.")
                except Exception as e:
                    st.error(f"Error during import: {str(e)}")
                    st.exception(e)
    
    with tab2:
        if has_sample:
            st.info(f"Sample data for {file_type} is available. You can use this to test the application with pre-populated data.")
            
            # Button to load sample data
            if st.button("Load Sample Data", key="load_sample_btn"):
                file_path = sample_files[file_type]
                try:
                    # Read and parse the sample file
                    df = open_excel_file(file_path)
                    
                    if df is not None:
                        st.write("Sample Data Preview:")
                        st.dataframe(df.head(5))
                        
                        st.write(f"Total rows: {len(df)}")
                        
                        # Process based on file type
                        progress_message = st.empty()
                        progress_message.info("Processing sample data... Please wait.")
                        
                        # Create progress bar
                        progress_bar = st.progress(0)
                        
                        try:
                            # Import the data based on the file type
                            if file_type == 'buyers':
                                count = save_buyers_data(df)
                            elif file_type == 'products':
                                count = save_products_data(df)
                            elif file_type == 'product_categories':
                                count = save_product_categories_data(df)
                            elif file_type == 'prices':
                                count = save_prices_data(df, 'HUF')
                            elif file_type == 'stock':
                                count = save_stock_data(df)
                            elif file_type == 'sales':
                                count = save_sales_data(df)
                            elif file_type == 'purchases':
                                count = save_purchases_data(df)
                            
                            # Update to 100%
                            progress_bar.progress(100)
                            progress_message.empty()
                            
                            # Show success message
                            st.success(f"Successfully imported {count} sample {file_type} records!")
                        except Exception as e:
                            progress_message.error(f"Error importing sample data: {str(e)}")
                            st.exception(e)
                    else:
                        st.error(f"Could not read sample file: {file_path}")
                        
                except Exception as e:
                    st.error(f"Error loading sample data: {str(e)}")
        else:
            st.warning(f"No sample data available for {file_type}. Please upload your own Excel file.")
            
            # Provide guidance on expected file format
            if file_type == 'buyers':
                expected_format = """
                Expected columns:
                - Vevőkód (Buyer ID) - required
                - Vevőnév (Buyer Name)
                - Számlázási ország (Country)
                - Vevőcsoport (Buyer Category)
                - WEB hozzáférés (Web Access)
                """
            elif file_type == 'products':
                expected_format = """
                Expected columns:
                - Termékkód (Product ID) - required
                - Terméknév (Product Name)
                - Gyártói csoport (Manufacturer Group)
                - Termékcsoport (Product Group)
                - Gyártó (Brand)
                - Elsődleges szállító (Primary Supplier)
                """
            elif file_type == 'prices':
                expected_format = """
                Expected columns:
                - Termékkód (Product ID) - required
                - Beszerzési ár (Purchase Price)
                - Bolti ár (Store Price)
                - Webes ár (Online Price)
                - Export ár (Export Price)
                - Lista ár (List Price)
                """
            else:
                expected_format = f"Please upload an Excel file with {file_type} data."
                
            st.code(expected_format)

def preview_data(file, file_type):
    """
    Show a preview of the Excel data.
    
    Returns:
        DataFrame: The pandas DataFrame with the Excel data if successful, None otherwise
    """
    try:
        # Get a copy of the file content as bytes IO
        bytes_data = file.getvalue()
        bio = io.BytesIO(bytes_data)
        
        # Parse the data from the Excel file
        df = pd.read_excel(bio)
        
        st.write("Data Preview:")
        st.dataframe(df.head(10))
        
        st.write(f"Total rows: {len(df)}")
        
        # Show column names for debugging
        columns = df.columns.tolist()
        st.write("Column names:", ", ".join(columns))
        
        # For sales data, check if required columns exist
        if file_type == 'sales':
            required_columns = ['Termékkód', 'Vevőkód', 'Értékesítés kelte', 'Mennyiség', 'Értékesítési egységár']
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                st.error(f"Missing required columns for sales data: {', '.join(missing_columns)}")
                
                # Check for similar columns (slight spelling differences)
                for req_col in missing_columns:
                    similar_cols = [col for col in columns if req_col.lower() in col.lower()]
                    if similar_cols:
                        st.warning(f"Found similar columns for '{req_col}': {', '.join(similar_cols)}")
            else:
                st.success("All required columns for sales data are present")
                
            # Check date column specifically
            if 'Értékesítés kelte' in columns:
                # Show a sample of the date values
                date_sample = df['Értékesítés kelte'].head(5).tolist()
                st.write(f"Sample dates: {date_sample}")
            else:
                st.error("Sales date column 'Értékesítés kelte' not found")
        
        return df
        
    except Exception as e:
        st.error(f"Error parsing Excel file: {str(e)}")
        st.exception(e)
        return None

def upload_file_for_brand_settings():
    """Upload and process brand settings file using the proven data loader system."""
    st.subheader("Import Brand Settings")
    
    uploaded_file = st.file_uploader(
        "Choose a brand settings Excel file", 
        type=["xlsx", "xls", "csv"],
        help="Upload Excel or CSV file with unified brand settings format"
    )
    
    if uploaded_file is not None:
        st.write("File uploaded successfully!")
        
        # Show preview of the data
        preview_df = preview_brand_settings_data(uploaded_file)
        
        if preview_df is None:
            st.error("Failed to preview the file. Please check the file format.")
            return
            
        # Import options
        st.subheader("Import Options")
        
        # For brand settings, we always update existing records
        import_mode = "update"
        
        st.info("Import mode: Update existing brand settings with new data")
        
        # Process upload button using proven pattern
        upload_button = st.button("Save to Database", key="process_brand_settings_upload_btn")
        if upload_button:
            try:
                # Display progress
                progress_message = st.empty()
                progress_message.info("Processing brand settings data... Please wait.")
                
                # Create progress bar
                progress_bar = st.progress(0)
                
                # Use the proven save function
                count = save_brand_settings_data(preview_df)
                
                # Update progress
                progress_bar.progress(1.0)
                
                # Clear progress indicators
                progress_bar.empty()
                progress_message.empty()
                
                if count > 0:
                    st.markdown(f"""
                    <div style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 12px; border-radius: 4px; margin: 10px 0;">
                        <h4 style="margin: 0 0 8px 0;">✅ Import Successful!</h4>
                        <p style="margin: 0;">Successfully imported settings for {count} brands!</p>
                        <p style="margin: 8px 0 0 0;"><strong>Next:</strong> Navigate to the Settings page to view and manage your imported brand settings.</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Clear all session state caches to force table refresh
                    cache_keys_to_clear = [
                        'original_logistics_data', 'original_margin_data', 'original_discount_data',
                        'logistics_data_cache', 'margin_data_cache', 'discount_data_cache',
                        'brands_cache', 'all_logistics_settings', 'all_margin_settings', 'all_discount_settings'
                    ]
                    
                    for key in cache_keys_to_clear:
                        if key in st.session_state:
                            del st.session_state[key]
                    
                    # Force page refresh to show updated data
                    st.success("Import complete! The page will refresh to show updated settings...")
                    time.sleep(1)  # Brief pause for user to see message
                    st.rerun()
                else:
                    st.warning("No records were imported. Please check your file and try again.")
                    
            except Exception as e:
                st.error(f"Error during import: {str(e)}")
                st.exception(e)

def preview_brand_settings_data(uploaded_file):
    """Preview brand settings data with proper validation."""
    try:
        # Try to read as Excel first, then CSV
        if uploaded_file.name.endswith('.csv'):
            df = pd.read_csv(uploaded_file)
        else:
            df = pd.read_excel(uploaded_file)
        
        # Define expected columns for unified format
        required_columns = [
            'Brand',
            # Logistics columns
            'Customs_EXW_Percent', 'Shipping_EXW_Percent', 'Customs_FOB_Percent', 'Shipping_FOB_Percent',
            'Default_Shipping', 'Purchase_Currency', 'Sales_Currency',
            # Margin columns
            'Margin_A_Percent', 'Margin_B_Percent', 'Margin_C_Percent', 'Margin_D_Percent',
            'Margin_EOL_Percent', 'Margin_Export_Percent', 'Default_Margin_Category',
            # Discount columns
            'Discount_I_Percent', 'Discount_II_Percent', 'Discount_III_Percent', 'Discount_IV_Percent'
        ]
        
        # Check for required columns
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            st.error(f"Missing required columns: {', '.join(missing_columns)}")
            st.info(f"Available columns: {', '.join(df.columns.tolist())}")
            st.info("Please use the export function to see the correct format.")
            return None
        
        # Show preview of data
        st.write("### Data Preview")
        st.dataframe(df.head(), use_container_width=True)
        
        st.write(f"**Total rows:** {len(df)}")
        st.write(f"**Columns found:** {len(df.columns)}")
        
        # Show column validation success
        st.success("✅ All required columns found!")
        
        return df
        
    except Exception as e:
        st.error(f"Error reading file: {str(e)}")
        return None