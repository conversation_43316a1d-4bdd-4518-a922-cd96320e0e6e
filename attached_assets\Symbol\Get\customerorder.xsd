﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="customerorders" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="customerorders" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="customerorder">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="date" type="xs:string" minOccurs="0" />
              <xs:element name="expirationdays" type="xs:string" minOccurs="0" />
              <xs:element name="orderid" type="xs:string" minOccurs="0" />
              <xs:element name="customer" type="xs:string" minOccurs="0" />
              <xs:element name="customerid" type="xs:string" minOccurs="0" />
              <xs:element name="customercode" type="xs:string" minOccurs="0" />
              <xs:element name="customeremail" type="xs:string" minOccurs="0" />
              <xs:element name="country" type="xs:string" minOccurs="0" />
              <xs:element name="region" type="xs:string" minOccurs="0" />
              <xs:element name="zip" type="xs:string" minOccurs="0" />
              <xs:element name="city" type="xs:string" minOccurs="0" />
              <xs:element name="street" type="xs:string" minOccurs="0" />
              <xs:element name="housenumber" type="xs:string" minOccurs="0" />
              <xs:element name="transportid" type="xs:string" minOccurs="0" />
              <xs:element name="transportname" type="xs:string" minOccurs="0" />
              <xs:element name="transportcountry" type="xs:string" minOccurs="0" />
              <xs:element name="transportregion" type="xs:string" minOccurs="0" />
              <xs:element name="transportzip" type="xs:string" minOccurs="0" />
              <xs:element name="transportcity" type="xs:string" minOccurs="0" />
              <xs:element name="transportstreet" type="xs:string" minOccurs="0" />
              <xs:element name="transporthousenumber" type="xs:string" minOccurs="0" />
              <xs:element name="transportcontactname" type="xs:string" minOccurs="0" />
              <xs:element name="currency" type="xs:string" minOccurs="0" />
              <xs:element name="currencyrate" type="xs:string" minOccurs="0" />
              <xs:element name="transportmode" type="xs:string" minOccurs="0" />
              <xs:element name="transporttargetid" type="xs:string" minOccurs="0" />
              <xs:element name="transportdate" type="xs:string" minOccurs="0" />
              <xs:element name="vouchersequencecode" type="xs:string" minOccurs="0" />
              <xs:element name="paymentmethod" type="xs:string" minOccurs="0" />
              <xs:element name="paymentmethodtolerance" type="xs:string" minOccurs="0" />
              <xs:element name="warehouse" type="xs:string" minOccurs="0" />
              <xs:element name="notifyphone" type="xs:string" minOccurs="0" />
              <xs:element name="notifysms" type="xs:string" minOccurs="0" />
              <xs:element name="notifyemail" type="xs:string" minOccurs="0" />
              <xs:element name="splitforbid" type="xs:string" minOccurs="0" />
              <xs:element name="banktrid" type="xs:string" minOccurs="0" />
              <xs:element name="division" type="xs:string" minOccurs="0" />
              <xs:element name="comment" type="xs:string" minOccurs="0" />
              <xs:element name="closedmanually" type="xs:string" minOccurs="0" />
              <xs:element name="strexa" type="xs:string" minOccurs="0" />
              <xs:element name="strexb" type="xs:string" minOccurs="0" />
              <xs:element name="strexc" type="xs:string" minOccurs="0" />
              <xs:element name="strexd" type="xs:string" minOccurs="0" />
              <xs:element name="dateexa" type="xs:string" minOccurs="0" />
              <xs:element name="dateexb" type="xs:string" minOccurs="0" />
              <xs:element name="dateexc" type="xs:string" minOccurs="0" />
              <xs:element name="dateexd" type="xs:string" minOccurs="0" />
              <xs:element name="numexa" type="xs:string" minOccurs="0" />
              <xs:element name="numexb" type="xs:string" minOccurs="0" />
              <xs:element name="numexc" type="xs:string" minOccurs="0" />
              <xs:element name="numexd" type="xs:string" minOccurs="0" />
              <xs:element name="boolexa" type="xs:string" minOccurs="0" />
              <xs:element name="boolexb" type="xs:string" minOccurs="0" />
              <xs:element name="boolexc" type="xs:string" minOccurs="0" />
              <xs:element name="boolexd" type="xs:string" minOccurs="0" />
              <xs:element name="lookupexa" type="xs:string" minOccurs="0" />
              <xs:element name="lookupexb" type="xs:string" minOccurs="0" />
              <xs:element name="lookupexc" type="xs:string" minOccurs="0" />
              <xs:element name="lookupexd" type="xs:string" minOccurs="0" />
              <xs:element name="feedbackurl" type="xs:string" minOccurs="0" />
              <xs:element name="errorurl" type="xs:string" minOccurs="0" />
              <xs:element name="detail" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="productcode" type="xs:string" minOccurs="0" />
                    <xs:element name="productid" type="xs:string" minOccurs="0" />
                    <xs:element name="productname" type="xs:string" minOccurs="0" />
                    <xs:element name="quantity" type="xs:string" minOccurs="0" />
                    <xs:element name="vat" type="xs:string" minOccurs="0" />
                    <xs:element name="vatname" type="xs:string" minOccurs="0" />
                    <xs:element name="unipricenet" type="xs:string" minOccurs="0" />
                    <xs:element name="uniprice" type="xs:string" minOccurs="0" />
                    <xs:element name="netvalue" type="xs:string" minOccurs="0" />
                    <xs:element name="grossvalue" type="xs:string" minOccurs="0" />
                    <xs:element name="discountpercent" type="xs:string" minOccurs="0" />
                    <xs:element name="mustmanufacturing" type="xs:string" minOccurs="0" />
                    <xs:element name="allocate" type="xs:string" minOccurs="0" />
                    <xs:element name="detailstatus" type="xs:string" minOccurs="0" />
                    <xs:element name="division" type="xs:string" minOccurs="0" />
                    <xs:element name="comment" type="xs:string" minOccurs="0" />
                    <xs:element name="strexa" type="xs:string" minOccurs="0" />
                    <xs:element name="strexb" type="xs:string" minOccurs="0" />
                    <xs:element name="strexc" type="xs:string" minOccurs="0" />
                    <xs:element name="strexd" type="xs:string" minOccurs="0" />
                    <xs:element name="dateexa" type="xs:string" minOccurs="0" />
                    <xs:element name="dateexb" type="xs:string" minOccurs="0" />
                    <xs:element name="dateexc" type="xs:string" minOccurs="0" />
                    <xs:element name="dateexd" type="xs:string" minOccurs="0" />
                    <xs:element name="numexa" type="xs:string" minOccurs="0" />
                    <xs:element name="numexb" type="xs:string" minOccurs="0" />
                    <xs:element name="numexc" type="xs:string" minOccurs="0" />
                    <xs:element name="numexd" type="xs:string" minOccurs="0" />
                    <xs:element name="boolexa" type="xs:string" minOccurs="0" />
                    <xs:element name="boolexb" type="xs:string" minOccurs="0" />
                    <xs:element name="boolexc" type="xs:string" minOccurs="0" />
                    <xs:element name="boolexd" type="xs:string" minOccurs="0" />
                    <xs:element name="lookupexa" type="xs:string" minOccurs="0" />
                    <xs:element name="lookupexb" type="xs:string" minOccurs="0" />
                    <xs:element name="lookupexc" type="xs:string" minOccurs="0" />
                    <xs:element name="lookupexd" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>