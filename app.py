import streamlit as st
import os
import sys
# Add the current directory to path to ensure local modules are used
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
# Import local modules using relative imports
from app_pages import overview, purchase_analytics, upload, settings, unified_sales_analytics, pricing, user_management, ai_analytics, content_creation
from database import init_db, get_session, check_db_connection
from components.ui import load_css, display_header, get_user_preferences
from config import config
from auth import require_auth, require_admin, logout, get_current_user, AuthManager

# Set page configuration - don't use pages
st.set_page_config(
    page_title="ERP Analytics Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Use negative margins to forcefully move content up 
# This is a more aggressive approach to overcome Streamlit's default spacing
st.markdown("""
<style>
    /* Hide Streamlit standard elements */
    #MainMenu {visibility: hidden;}
    header {visibility: hidden;}
    footer {visibility: hidden;}
    
    /* Apply negative margin to entire app to compensate for top space */
    .main .block-container {
        margin-top: -75px !important;
    }
    
    /* Apply negative margin to sidebar for header adjustment */
    [data-testid="stSidebar"] > div {
        margin-top: -75px !important;
    }
    
    /* Hide unwanted default navigation */
    [data-testid="stSidebarNavItems"] {display: none !important;}
    
    /* Make navigation buttons uniform and centered */
    [data-testid="stSidebar"] .stButton > button {
        width: 100% !important;
        text-align: center !important;
        min-height: 1.8rem !important;
        margin-bottom: 0.1rem !important;
        padding: 0.3rem 0.5rem !important;
        font-size: 0.85rem !important;
    }
    
    /* Reduce spacing between sections */
    [data-testid="stSidebar"] .stMarkdown {
        margin-bottom: 0.1rem !important;
        margin-top: 0.3rem !important;
        padding-bottom: 0 !important;
        padding-top: 0 !important;
    }
    
    /* Make section headings more compact */
    [data-testid="stSidebar"] .stMarkdown h3 {
        margin-bottom: 0.2rem !important;
        margin-top: 0.3rem !important;
        font-size: 0.9rem !important;
    }
    
    /* Reduce overall sidebar padding */
    [data-testid="stSidebar"] > div {
        padding-top: 1rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    /* Ensure controls like filters start at top too */
    .stExpander {
        margin-top: 0 !important;
    }
    
    /* Remove space between sidebar elements */
    .stSidebar .sidebar-content {
        gap: 0 !important;
    }
    
    /* Fix padding in main content and sidebar */
    .main .block-container, [data-testid="stSidebar"] {
        padding-top: 0 !important;
    }
    
    /* Make headers and titles compact */
    h1, h2, h3, h4, h5, h6 {
        margin-top: 0 !important;
        padding-top: 0 !important;
        line-height: 1.2 !important;
    }
    
    /* Disable tooltips for navigation buttons */
    .stTooltipIcon {
        display: none !important;
    }
</style>
""", unsafe_allow_html=True)

# Load custom CSS
load_css()

# Initialize the database - force initialization now to avoid errors later
db_initialized = init_db()
if not db_initialized:
    st.error("Failed to initialize database connection. Please check your database configuration.")

def show_agent_settings_page():
    """Agent Settings page - placeholder for future AI agent configuration"""
    st.title("🤖 Agent Settings")
    st.markdown("Configure AI agents and automation settings")
    
    st.info("This page is under development. Future features will include:")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **Content Creation Agents:**
        - Research agent configuration
        - Writing style preferences
        - Content quality thresholds
        - Language model selection
        """)
    
    with col2:
        st.markdown("""
        **Analytics Agents:**
        - Data analysis automation
        - Report generation schedules
        - Alert thresholds
        - Custom insights configuration
        """)

def show_general_settings_page():
    """General Settings page - placeholder for system-wide configuration"""
    st.title("🔧 General Settings")
    st.markdown("System-wide configuration and preferences")
    
    st.info("This page is under development. Future features will include:")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **System Configuration:**
        - Default language settings
        - Currency and localization
        - Backup and maintenance
        - Performance optimization
        """)
    
    with col2:
        st.markdown("""
        **Integration Settings:**
        - API key management
        - External service connections
        - Data export preferences
        - Security configurations
        """)

# The main application
def main():
    # Check authentication first
    if not require_auth():
        return
    
    # Initialize user preferences
    user_prefs = get_user_preferences()
    
    # Get current user info for role-based features
    current_user = get_current_user()
    is_admin = current_user.get('role') == 'admin'
    
    # Create sidebar for navigation
    with st.sidebar:
        # Load default view from user preferences
        if 'current_page' not in st.session_state:
            st.session_state.current_page = user_prefs.get('default_view', 'Home')
        

        
        # Create organized navigation menu
        if st.button("🏠 Home", key="nav_Home"):
            st.session_state.current_page = "Home"
            st.rerun()
        
        st.markdown("📊 **Analytics**")
        if st.button("Overview", key="nav_Overview"):
            st.session_state.current_page = "Overview"
            st.rerun()
        if st.button("Sales", key="nav_Sales_Analytics"):
            st.session_state.current_page = "Sales Analytics"
            st.rerun()
        if st.button("Purchase and Stock", key="nav_Purchase_Stock"):
            st.session_state.current_page = "Purchase Analytics"
            st.rerun()
        
        st.markdown("💰 **Sales**")
        if st.button("Pricing", key="nav_Pricing"):
            st.session_state.current_page = "Pricing"
            st.rerun()
        if st.button("Pricing Settings", key="nav_Pricing_Settings"):
            st.session_state.current_page = "Settings"
            st.rerun()
        
        st.markdown("🎯 **Marketing**")
        if st.button("Content Creator", key="nav_Content_Creator"):
            st.session_state.current_page = "Content Creator"
            st.rerun()
        if st.button("Agent Settings", key="nav_Agent_Settings"):
            st.session_state.current_page = "Agent Settings"
            st.rerun()
        
        if is_admin:
            st.markdown("⚙️ **Admin**")
            if st.button("Database", key="nav_Database"):
                st.session_state.current_page = "Data"
                st.rerun()
            if st.button("User Management", key="nav_User_Management"):
                st.session_state.current_page = "User Management"
                st.rerun()
            if st.button("General Settings", key="nav_General_Settings"):
                st.session_state.current_page = "General Settings"
                st.rerun()
        
        # Theme toggle and logout buttons
        if 'dark_theme' not in st.session_state:
            st.session_state.dark_theme = True  # Default to dark theme
        
        theme_label = "🌙 Dark Mode" if st.session_state.dark_theme else "☀️ Light Mode"
        if st.button(theme_label, key="theme_toggle"):
            st.session_state.dark_theme = not st.session_state.dark_theme
            st.rerun()
        
        if st.button("🚪 Logout", key="logout_btn", type="secondary"):
            logout()
        
        # Apply theme-specific CSS based on toggle
        if st.session_state.dark_theme:
            # Force dark theme CSS
            st.markdown("""
            <style>
                .stApp {
                    background-color: #0e1117 !important;
                    color: #fafafa !important;
                }
                .stSidebar {
                    background-color: #262730 !important;
                }
                .stButton > button {
                    background-color: #262730 !important;
                    color: #fafafa !important;
                    border: 1px solid #555 !important;
                }
                .stSelectbox > div > div {
                    background-color: #262730 !important;
                    color: #fafafa !important;
                }
                .stDateInput > div > div {
                    background-color: #262730 !important;
                    color: #fafafa !important;
                }
                .stRadio > div {
                    background-color: transparent !important;
                    color: #fafafa !important;
                }
            </style>
            """, unsafe_allow_html=True)
        else:
            # Force light theme CSS
            st.markdown("""
            <style>
                .stApp {
                    background-color: #ffffff !important;
                    color: #262730 !important;
                }
                .stSidebar {
                    background-color: #f0f2f6 !important;
                }
                .stButton > button {
                    background-color: #ffffff !important;
                    color: #262730 !important;
                    border: 1px solid #cccccc !important;
                }
                .stSelectbox > div > div {
                    background-color: #ffffff !important;
                    color: #262730 !important;
                }
                .stDateInput > div > div {
                    background-color: #ffffff !important;
                    color: #262730 !important;
                }
                .stRadio > div {
                    background-color: transparent !important;
                    color: #262730 !important;
                }
            </style>
            """, unsafe_allow_html=True)
        

    
    # Display the header
    display_header()
    
    # Display the selected page
    selected_page = st.session_state.current_page
    
    if selected_page == "Home":
        ai_analytics.show_ai_analytics()
    elif selected_page == "Overview":
        overview.show()
    elif selected_page == "Sales Analytics":
        unified_sales_analytics.show()
    elif selected_page == "Purchase Analytics":
        purchase_analytics.show()
    elif selected_page == "Pricing":
        pricing.show()
    elif selected_page == "Settings":
        if is_admin:
            settings.show()
        else:
            st.error("Access denied. Admin privileges required.")
    elif selected_page == "Content Creator":
        content_creation.show_content_creation()
    elif selected_page == "Agent Settings":
        show_agent_settings_page()
    elif selected_page == "Data":
        if is_admin:
            upload.show()
        else:
            st.error("Access denied. Admin privileges required.")
    elif selected_page == "User Management":
        if is_admin:
            user_management.show()
        else:
            st.error("Access denied. Admin privileges required.")
    elif selected_page == "General Settings":
        if is_admin:
            show_general_settings_page()
        else:
            st.error("Access denied. Admin privileges required.")

# Run the main app directly
if __name__ == "__main__":
    main()
