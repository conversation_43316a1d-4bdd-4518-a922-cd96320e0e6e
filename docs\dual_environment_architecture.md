# Dual Environment Architecture Documentation

This document explains the implementation of the dual environment architecture for the ERP Analytics Dashboard, allowing it to run in both Replit development and AWS production environments.

## Overview

The application is designed with an environment-aware configuration system that automatically detects and adapts to different runtime environments (Replit or AWS). This allows for seamless development in Replit while preparing for production deployment on AWS.

## Key Components

### Environment Detection

The application detects its environment using environment variables in `config.py`:

```python
# Environment detection
IS_REPLIT = 'REPL_ID' in os.environ
IS_AWS = os.environ.get('DEPLOYMENT_ENV') == 'aws'
```

- If the `REPL_ID` environment variable is present, the app knows it's running in Replit
- If the `DEPLOYMENT_ENV` environment variable is set to 'aws', the app knows it's running in AWS

### Environment-Specific Database Configuration

Database connection details are determined dynamically based on the detected environment:

```python
def DATABASE_URL(self):
    """
    Returns the database URL based on the current environment.
    Ensures that a valid string is always returned.
    """
    # In Replit, use the provided PostgreSQL database
    if self.IS_REPLIT:
        db_url = os.environ.get('DATABASE_URL', '')
        if db_url:
            return db_url
    
    # In AWS, construct the URL from environment variables
    if self.IS_AWS:
        aws_vars_ok, missing_vars = self.check_aws_env_vars()
        if aws_vars_ok:
            db_host = os.environ.get('DB_HOST')
            db_name = os.environ.get('DB_NAME')
            db_user = os.environ.get('DB_USER')
            db_password = os.environ.get('DB_PASSWORD')
            return f"postgresql://{db_user}:{db_password}@{db_host}/{db_name}"
```

- In Replit: Uses the built-in PostgreSQL database via the `DATABASE_URL` environment variable
- In AWS: Constructs the connection string from individual environment variables (`DB_HOST`, `DB_NAME`, etc.)

### Connection Pooling for AWS

The application uses connection pooling when running in AWS to improve performance:

```python
# Create engine with appropriate configuration
if config.IS_AWS:
    # Use connection pooling in AWS for better performance
    engine = create_engine(
        config.DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_recycle=1800,
        pool_pre_ping=True
    )
else:
    # Standard configuration for Replit
    engine = create_engine(config.DATABASE_URL)
```

Connection pooling settings:
- `pool_size`: The number of connections to keep open in the pool
- `max_overflow`: How many connections beyond `pool_size` can be created when needed
- `pool_recycle`: Time in seconds after which a connection will be recycled (prevents stale connections)
- `pool_pre_ping`: Tests connections with a ping before using them to ensure they're still valid

### Human-Readable Environment Information

The application provides user-friendly environment names for display in the UI:

```python
def ENVIRONMENT_NAME(self):
    """Return a human-readable environment name for UI display"""
    if self.IS_REPLIT:
        return "Replit Development"
    elif self.IS_AWS:
        return "AWS Production"
    else:
        return "Local Development"
```

### Database Health Checks

The system includes a health check function to verify database connectivity:

```python
def check_db_connection():
    """
    Check if database connection is working.
    Returns a tuple of (bool, str) indicating success/failure and a message.
    """
    try:
        # Create a temporary session and test query
        session = get_session()
        session.execute(text("SELECT 1"))
        session.close()
        
        # Generate environment-specific success message
        env_name = "Replit Development" if config.IS_REPLIT else "AWS Production"
        return True, f"Connected to database in {env_name} environment"
    except Exception as e:
        return False, f"Database connection error: {str(e)}"
```

### System Information Display

The application provides a System Info tab in the Settings page that displays:

1. The current environment (Replit Development or AWS Production)
2. Database connection status
3. Technical details (when debug features are enabled)

## Debug Features

Development-specific features are enabled based on the environment:

```python
ENABLE_DEBUG_FEATURES = not IS_AWS  # Enable debug features in non-production environments
```

This ensures that technical details and debugging tools are available in Replit but not in the AWS production environment.

## AWS-Specific Configuration Validation

The system validates that all required AWS environment variables are set:

```python
def check_aws_env_vars(self):
    """Check if all required AWS environment variables are set."""
    missing_vars = [var for var in self.AWS_ENV_VARS if not os.environ.get(var)]
    return len(missing_vars) == 0, missing_vars
```

## Best Practices for Dual Environment Architecture

1. **Environment Variable Management**
   - Use a consistent approach to environment variables
   - Document all required variables for each environment
   - Use default values where appropriate

2. **Conditional Configuration**
   - Keep environment-specific logic centralized in `config.py`
   - Use the `config` object to access environment information throughout the app

3. **Error Handling**
   - Implement graceful fallbacks when environment-specific features are unavailable
   - Provide clear error messages that are environment-aware

4. **Testing**
   - Test the application in both environments before deployment
   - Use the System Info tab to verify environment detection

5. **Security**
   - Ensure sensitive information (database credentials) is properly handled in both environments
   - Use appropriate security groups and network access controls in AWS

## Development Workflow

1. Develop and test features in Replit
2. Test deployment configuration using the System Info tab
3. Deploy to AWS following the deployment guide
4. Verify proper environment detection in AWS using the System Info tab