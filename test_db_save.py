#!/usr/bin/env python3
"""
Test script to verify database saving functionality for competitor scraping.
"""

from database import get_session
from models import CompetitorScrape
from datetime import datetime

def test_database_save():
    """Test if we can properly save data to the CompetitorScrape table."""
    session = get_session()
    if not session:
        print("❌ Database connection failed")
        return False
    
    try:
        # Test if we can create a new record
        test_record = CompetitorScrape(
            product_code="TEST-001",
            thomann_price=100.0,
            thomann_stock="In Stock",
            muziker_price=110.0,
            muziker_stock="Available",
            last_scraped=datetime.now()
        )
        
        session.add(test_record)
        session.commit()
        print("✅ Successfully created test record")
        
        # Test if we can update an existing record using merge (like the scraping code)
        existing_record = session.query(CompetitorScrape).filter_by(product_code="TEST-001").first()
        if existing_record:
            # Merge to reattach to session (simulating the scraping fix)
            existing_record = session.merge(existing_record)
            existing_record.thomann_price = 95.0
            existing_record.r55_price = 120.0
            existing_record.last_scraped = datetime.now()
            session.commit()
            print("✅ Successfully updated test record using merge")
        
        # Verify the update worked
        updated_record = session.query(CompetitorScrape).filter_by(product_code="TEST-001").first()
        if updated_record and updated_record.thomann_price == 95.0 and updated_record.r55_price == 120.0:
            print("✅ Database update persistence verified")
        else:
            print("❌ Database update not persisted correctly")
        
        # Clean up test record
        session.delete(updated_record)
        session.commit()
        print("✅ Successfully deleted test record")
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ Database test failed: {str(e)}")
        return False
    finally:
        session.close()

def test_scraping_integration():
    """Test that simulates the scraping process with multiple updates."""
    session = get_session()
    if not session:
        print("❌ Database connection failed")
        return False
    
    try:
        # Create initial record
        test_record = CompetitorScrape(
            product_code="INTEGRATION-TEST",
            import_date=datetime.now()
        )
        session.add(test_record)
        session.commit()
        print("✅ Created initial competitor record")
        
        # Simulate multiple scraping updates (like the real scraping process)
        updates = [
            {'thomann_price': 150.0, 'thomann_stock': 'In Stock'},
            {'muziker_price': 160.0, 'muziker_stock': 'Available'},
            {'r55_price': 145.0, 'r55_stock': '5+ Available'},
            {'kytary_price': 155.0, 'kytary_stock': 'In Stock'}
        ]
        
        for i, update_data in enumerate(updates):
            # Query the record
            competitor = session.query(CompetitorScrape).filter_by(product_code="INTEGRATION-TEST").first()
            
            # Merge to reattach (critical for preventing detached instance errors)
            competitor = session.merge(competitor)
            
            # Apply updates
            for field, value in update_data.items():
                setattr(competitor, field, value)
            
            competitor.last_scraped = datetime.now()
            session.commit()
            print(f"✅ Update {i+1} committed successfully")
        
        # Verify final state
        final_record = session.query(CompetitorScrape).filter_by(product_code="INTEGRATION-TEST").first()
        if (final_record and 
            final_record.thomann_price == 150.0 and 
            final_record.muziker_price == 160.0 and
            final_record.r55_price == 145.0 and
            final_record.kytary_price == 155.0):
            print("✅ All scraping updates persisted correctly")
        else:
            print("❌ Some updates were not persisted")
        
        # Clean up
        session.delete(final_record)
        session.commit()
        print("✅ Test cleanup completed")
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ Integration test failed: {str(e)}")
        return False
    finally:
        session.close()

if __name__ == "__main__":
    print("Testing database save functionality...")
    basic_success = test_database_save()
    integration_success = test_scraping_integration()
    
    if basic_success and integration_success:
        print("🎉 All database tests passed - scraping should save correctly")
    else:
        print("💥 Some database tests failed")