﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProductPrices" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="ProductPrices" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ProductPrice">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="product" type="xs:string" minOccurs="0" />
              <xs:element name="productcode" type="xs:string" minOccurs="0" />
              <xs:element name="price" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="pricecategory" type="xs:string" minOccurs="0" />
                    <xs:element name="pricecategoryName" type="xs:string" minOccurs="0" />
                    <xs:element name="priceCurrency" type="xs:string" minOccurs="0" />
                    <xs:element name="quantityunit" type="xs:string" minOccurs="0" />
                    <xs:element name="value" type="xs:string" minOccurs="0" />
                    <xs:element name="validfrom" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>