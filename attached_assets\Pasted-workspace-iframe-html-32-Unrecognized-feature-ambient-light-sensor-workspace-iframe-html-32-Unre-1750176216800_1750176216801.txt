workspace_iframe.html:32 Unrecognized feature: 'ambient-light-sensor'.
workspace_iframe.html:32 Unrecognized feature: 'battery'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-not-rendered'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-out-of-viewport'.
workspace_iframe.html:32 Unrecognized feature: 'layout-animations'.
workspace_iframe.html:32 Unrecognized feature: 'legacy-image-formats'.
workspace_iframe.html:32 Unrecognized feature: 'navigation-override'.
workspace_iframe.html:32 Unrecognized feature: 'oversized-images'.
workspace_iframe.html:32 Unrecognized feature: 'publickey-credentials'.
workspace_iframe.html:32 Unrecognized feature: 'speaker-selection'.
workspace_iframe.html:32 Unrecognized feature: 'unoptimized-images'.
workspace_iframe.html:32 Unrecognized feature: 'unsized-media'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowfullscreen'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowpaymentrequest'.
writing_mode_assistant.js:2 Uncaught Error: Extension context invalidated.
    at t.updateActiveElementStatus (writing_mode_assistant.js:2:73996)
    at HTMLDocument.<anonymous> (writing_mode_assistant.js:2:74462)
index.BqDl3eRM.js:41 Unrecognized feature: 'document-domain'.
Ve @ index.BqDl3eRM.js:41
index.BqDl3eRM.js:41 Unrecognized feature: 'vr'.
Ve @ index.BqDl3eRM.js:41
index.BqDl3eRM.js:41 Unrecognized feature: 'wake-lock'.
Ve @ index.BqDl3eRM.js:41
2
                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
about:srcdoc:4 Brand currency cache loaded: {Alesis: {…}, Alto Pro: {…}}
Console.js:61 Received component message for unregistered ComponentInstance! {isStreamlitMessage: true, type: 'streamlit:componentReady', apiVersion: 1}
Mt.forEach.n.<computed> @ Console.js:61
ComponentRegistry.onMessageEvent @ index.BqDl3eRM.js:464
postMessage
e.sendBackMsg @ streamlit.js:186
e.setComponentReady @ streamlit.js:73
i.componentDidMount @ StreamlitReact.js:73
Cl @ react-dom.production.min.js:260
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 flag allow_unsafe_jscode is on.
Mt.forEach.n.<computed> @ Console.js:61
parseGridoptions @ AgGrid.tsx:133
vPe @ AgGrid.tsx:99
ra @ react-dom.production.min.js:184
Ia @ react-dom.production.min.js:197
Sl @ react-dom.production.min.js:291
bd @ react-dom.production.min.js:279
fd @ react-dom.production.min.js:279
vd @ react-dom.production.min.js:279
od @ react-dom.production.min.js:267
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 ***Received Props {width: 824, disabled: false, args: {…}, theme: {…}}
Console.js:61 *** Processed State {gridHeight: 600, gridOptions: {…}, isRowDataEdited: false, api: undefined, enterprise_features_enabled: false, …}
Console.js:61 AG Grid: error #200 Unable to use enableRangeSelection as CellSelectionModule is not registered. Check if you have registered the module:
import { ModuleRegistry } from 'ag-grid-community'; 
import { CellSelectionModule } from 'ag-grid-enterprise'; 

ModuleRegistry.registerModules([ CellSelectionModule ]); 

For more info see: https://www.ag-grid.com/react-data-grid/modules/ 
See https://www.ag-grid.com/react-data-grid/errors/200?_version_=33.0.3&gridId=1&gridScoped=false&rowModelType=clientSide&moduleName=CellSelection&reasonOrId=enableRangeSelection
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:777
Ke @ main.esm.mjs:765
Xe @ main.esm.mjs:777
at @ main.esm.mjs:864
gt @ main.esm.mjs:931
assertModuleRegistered @ main.esm.mjs:27307
(anonymous) @ main.esm.mjs:34947
(anonymous) @ main.esm.mjs:34946
processOptions @ main.esm.mjs:34910
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of version 32.2.1, using `rowSelection` with the values "single" or "multiple" has been deprecated. Use the object value instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, rowMultiSelectWithClick is deprecated. Use `rowSelection.enableSelectionWithoutKeys` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, suppressRowDeselection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, suppressRowClickSelection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, groupSelectsChildren is deprecated. Use `rowSelection.groupSelects = "descendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, groupSelectsFiltered is deprecated. Use `rowSelection.groupSelects = "filteredDescendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, enableRangeSelection is deprecated. Use `cellSelection = true` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: As of v32.2, enableFillHandle is deprecated. Use `cellSelection.handle` instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:34972
processOptions @ main.esm.mjs:34971
processGridOptions @ main.esm.mjs:34811
postConstruct @ main.esm.mjs:34803
(anonymous) @ main.esm.mjs:4976
initBeans @ main.esm.mjs:4976
init @ main.esm.mjs:4955
init @ main.esm.mjs:5027
yo @ main.esm.mjs:4932
bo @ main.esm.mjs:5022
create @ main.esm.mjs:32142
(anonymous) @ index.esm.mjs:2653
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: invalid colDef property 'filterable' did you mean any of these: suppressFiltersToolPanel, filterValueGetter, filterParams, floatingFilterComponent, floatingFilterComponentParams, headerCheckboxSelectionFilteredOnly, filter, getQuickFilterText.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:35009
checkProperties @ main.esm.mjs:35003
processOptions @ main.esm.mjs:34875
validateColDef @ main.esm.mjs:34869
xn @ main.esm.mjs:3478
yn @ main.esm.mjs:3379
fn @ main.esm.mjs:3366
vn @ main.esm.mjs:3305
createColsFromColDefs @ main.esm.mjs:3571
setColumnDefs @ main.esm.mjs:3806
setColumnsAndData @ main.esm.mjs:31298
(anonymous) @ main.esm.mjs:31289
(anonymous) @ main.esm.mjs:24532
(anonymous) @ main.esm.mjs:24532
i.forEach.o @ main.esm.mjs:71
(anonymous) @ main.esm.mjs:75
o @ main.esm.mjs:67
dispatchToListeners @ main.esm.mjs:81
dispatchEvent @ main.esm.mjs:51
dispatchLocalEvent @ main.esm.mjs:1570
register @ main.esm.mjs:24562
setComp @ main.esm.mjs:12060
(anonymous) @ index.esm.mjs:2193
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
ld @ react-dom.production.min.js:272
Wo @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: to see all the valid colDef properties please check: https://www.ag-grid.com/react-data-grid/column-properties/
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
checkProperties @ main.esm.mjs:35013
processOptions @ main.esm.mjs:34875
validateColDef @ main.esm.mjs:34869
xn @ main.esm.mjs:3478
yn @ main.esm.mjs:3379
fn @ main.esm.mjs:3366
vn @ main.esm.mjs:3305
createColsFromColDefs @ main.esm.mjs:3571
setColumnDefs @ main.esm.mjs:3806
setColumnsAndData @ main.esm.mjs:31298
(anonymous) @ main.esm.mjs:31289
(anonymous) @ main.esm.mjs:24532
(anonymous) @ main.esm.mjs:24532
i.forEach.o @ main.esm.mjs:71
(anonymous) @ main.esm.mjs:75
o @ main.esm.mjs:67
dispatchToListeners @ main.esm.mjs:81
dispatchEvent @ main.esm.mjs:51
dispatchLocalEvent @ main.esm.mjs:1570
register @ main.esm.mjs:24562
setComp @ main.esm.mjs:12060
(anonymous) @ index.esm.mjs:2193
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
ld @ react-dom.production.min.js:272
Wo @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: invalid colDef property 'precision' did you mean any of these: suppressColumnsToolPanel, suppressFiltersToolPanel, headerCheckboxSelection, headerCheckboxSelectionFilteredOnly, headerCheckboxSelectionCurrentPageOnly, suppressFloatingFilterButton, cellEditorPopupPosition, checkboxSelection.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
(anonymous) @ main.esm.mjs:35009
checkProperties @ main.esm.mjs:35003
processOptions @ main.esm.mjs:34875
validateColDef @ main.esm.mjs:34869
xn @ main.esm.mjs:3478
yn @ main.esm.mjs:3379
fn @ main.esm.mjs:3366
vn @ main.esm.mjs:3305
createColsFromColDefs @ main.esm.mjs:3571
setColumnDefs @ main.esm.mjs:3806
setColumnsAndData @ main.esm.mjs:31298
(anonymous) @ main.esm.mjs:31289
(anonymous) @ main.esm.mjs:24532
(anonymous) @ main.esm.mjs:24532
i.forEach.o @ main.esm.mjs:71
(anonymous) @ main.esm.mjs:75
o @ main.esm.mjs:67
dispatchToListeners @ main.esm.mjs:81
dispatchEvent @ main.esm.mjs:51
dispatchLocalEvent @ main.esm.mjs:1570
register @ main.esm.mjs:24562
setComp @ main.esm.mjs:12060
(anonymous) @ index.esm.mjs:2193
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
ld @ react-dom.production.min.js:272
Wo @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 AG Grid: error #101 Could not find '
                    function(params) {
                        if (!params.value) return '';
                        var brand = params.data['Gyártó'];
                        var currencyMap = {"Alesis": "USD", "Alto Pro": "USD"};
                        var currency = currencyMap[brand] || 'EUR';
                        console.log('Rendering brand:', brand, 'currency:', currency, 'value:', params.value);
                        return Math.round(params.value) + ' ' + currency;
                    }
                    ' component. It was configured as "cellRenderer: '
                    function(params) {
                        if (!params.value) return '';
                        var brand = params.data['Gyártó'];
                        var currencyMap = {"Alesis": "USD", "Alto Pro": "USD"};
                        var currency = currencyMap[brand] || 'EUR';
                        console.log('Rendering brand:', brand, 'currency:', currency, 'value:', params.value);
                        return Math.round(params.value) + ' ' + currency;
                    }
                    '" but it wasn't found in the list of registered components.
          Did you mean: [agAnimateShowChangeCellRenderer,agAnimateSlideCellRenderer]?
 If using a custom component check it has been registered correctly. 
See https://www.ag-grid.com/react-data-grid/errors/101?_version_=33.0.3&propertyName=cellRenderer&componentName=%0A++++++++++++++++++++function%28params%29+%7B%0A++++++++++++++++++++++++if+%28%21params.value%29+return+%27%27%3B%0A++++++++++++++++++++++++var+brand+%3D+params.data%5B%27Gy%C3%A1rt%C3%B3%27%5D%3B%0A++++++++++++++++++++++++var+currencyMap+%3D+%7B%22Alesis%22%3A+%22USD%22%2C+%22Alto+Pro%22%3A+%22USD%22%7D%3B%0A++++++++++++++++++++++++var+currency+%3D+currencyMap%5Bbrand%5D+%7C%7C+%27EUR%27%3B%0A++++++++++++++++++++++++console.log%28%27Rendering+brand%3A%27%2C+brand%2C+%27currency%3A%27%2C+currency%2C+%27value%3A%27%2C+params.value%29%3B%0A++++++++++++++++++++++++return+Math.round%28params.value%29+%2B+%27+%27+%2B+currency%3B%0A++++++++++++++++++++%7D%0A++++++++++++++++++++&agGridDefaults=%7B%7D&jsComps=%7B%7D
Mt.forEach.n.<computed> @ Console.js:61
(anonymous) @ main.esm.mjs:774
Ke @ main.esm.mjs:765
Ze @ main.esm.mjs:774
at @ main.esm.mjs:864
pt @ main.esm.mjs:928
missingUserComponent @ main.esm.mjs:34824
getUserComponent @ main.esm.mjs:24495
v @ main.esm.mjs:4570
getCompDetails @ main.esm.mjs:4578
eo @ main.esm.mjs:4775
showValue @ main.esm.mjs:5997
setComp @ main.esm.mjs:5962
(anonymous) @ index.esm.mjs:1806
ol @ react-dom.production.min.js:243
Cl @ react-dom.production.min.js:262
bl @ react-dom.production.min.js:259
yl @ react-dom.production.min.js:258
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
ld @ react-dom.production.min.js:272
Wo @ react-dom.production.min.js:127
(anonymous) @ react-dom.production.min.js:282
xd @ react-dom.production.min.js:280
od @ react-dom.production.min.js:269
w @ scheduler.production.min.js:13
E @ scheduler.production.min.js:14
Console.js:61 Attached grid return event cellValueChanged
Console.js:61 Received component message for unregistered ComponentInstance! {isStreamlitMessage: true, type: 'streamlit:setFrameHeight', height: 600}