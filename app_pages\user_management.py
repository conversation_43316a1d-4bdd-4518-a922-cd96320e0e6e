"""
User Management page for admin users.
Handles user creation, deletion, and password management.
"""

import streamlit as st
import pandas as pd
from auth import AuthManager, require_admin, get_current_user
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode, DataReturnMode

def show():
    """Display the User Management page."""
    if not require_admin():
        return
    
    st.header("👥 User Management")
    st.subheader("Manage system users and permissions")
    
    # Get current user info
    current_user = get_current_user()
    
    # Create tabs for different management functions
    tab1, tab2 = st.tabs(["User List", "Add New User"])
    
    with tab1:
        show_user_list(current_user)
    
    with tab2:
        show_add_user_form()

def show_user_list(current_user):
    """Display list of all users with management options."""
    st.subheader("Active Users")
    
    # Get all users
    users = AuthManager.get_all_users()
    
    if not users:
        st.warning("No users found in the system.")
        return
    
    # Convert to DataFrame for display
    df_data = []
    for user in users:
        df_data.append({
            'Username': user['username'],
            'Role': user['role'],
            'Created': user['created_date'].strftime('%Y-%m-%d') if user['created_date'] else 'N/A',
            'Last Login': user['last_login'].strftime('%Y-%m-%d %H:%M') if user['last_login'] else 'Never',
            'Actions': user['username']  # For action buttons
        })
    
    df = pd.DataFrame(df_data)
    
    if len(df) > 0:
        # Display user table using AgGrid for better functionality
        gb = GridOptionsBuilder.from_dataframe(df)
        
        # Configure columns
        gb.configure_column("Username", width=150, pinned='left')
        gb.configure_column("Role", width=100)
        gb.configure_column("Created", width=120)
        gb.configure_column("Last Login", width=150)
        gb.configure_column("Actions", width=200)
        
        # Configure grid options
        gb.configure_default_column(resizable=True, sortable=True, filterable=True)
        gb.configure_selection('single')
        
        # Detect theme for AgGrid
        is_dark_theme = st.session_state.get('dark_theme', True)
        ag_theme = 'streamlit' if is_dark_theme else 'alpine'
        
        grid_options = gb.build()
        
        # Display the grid
        grid_response = AgGrid(
            df,
            gridOptions=grid_options,
            update_mode=GridUpdateMode.SELECTION_CHANGED,
            data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
            fit_columns_on_grid_load=True,
            height=400,
            theme=ag_theme,
            allow_unsafe_jscode=True
        )
        
        # User management actions
        st.subheader("User Actions")
        
        # Get selected user
        selected_rows = grid_response.get('selected_rows', [])
        if selected_rows is not None and len(selected_rows) > 0:
            # selected_rows is a DataFrame, access using iloc
            selected_user = selected_rows.iloc[0]['Username']
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("🔄 Reset Password", type="secondary"):
                    show_reset_password_form(selected_user)
            
            with col2:
                if st.button("🗑️ Delete User", type="secondary"):
                    if selected_user != current_user['username']:
                        show_delete_user_confirmation(selected_user)
                    else:
                        st.error("You cannot delete your own account!")
            
            with col3:
                if st.button("👑 Change Role", type="secondary"):
                    show_change_role_form(selected_user, current_user)
        else:
            st.info("Select a user from the table above to perform actions.")

def show_add_user_form():
    """Display form to add new users."""
    st.subheader("Add New User")
    
    with st.form("add_user_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            new_username = st.text_input("Username", help="Must be unique")
            new_role = st.selectbox("Role", ["user", "admin"], help="Admin users can manage other users and upload data")
        
        with col2:
            new_password = st.text_input("Password", type="password", help="Minimum 6 characters recommended")
            confirm_password = st.text_input("Confirm Password", type="password")
        
        submit_button = st.form_submit_button("Create User", type="primary")
        
        if submit_button:
            if not new_username or not new_password:
                st.error("Username and password are required!")
            elif len(new_password) < 6:
                st.error("Password should be at least 6 characters long!")
            elif new_password != confirm_password:
                st.error("Passwords do not match!")
            else:
                success = AuthManager.create_user(new_username, new_password, new_role)
                if success:
                    st.success(f"User '{new_username}' created successfully with role '{new_role}'!")
                    st.rerun()
                else:
                    st.error("Failed to create user. Username might already exist.")

def show_reset_password_form(username):
    """Display password reset form."""
    st.subheader(f"Reset Password for: {username}")
    
    with st.form(f"reset_password_{username}"):
        new_password = st.text_input("New Password", type="password", key=f"new_pwd_{username}")
        confirm_password = st.text_input("Confirm New Password", type="password", key=f"confirm_pwd_{username}")
        
        col1, col2 = st.columns(2)
        with col1:
            reset_button = st.form_submit_button("Reset Password", type="primary")
        with col2:
            cancel_button = st.form_submit_button("Cancel")
        
        if reset_button:
            if not new_password:
                st.error("Password cannot be empty!")
            elif len(new_password) < 6:
                st.error("Password should be at least 6 characters long!")
            elif new_password != confirm_password:
                st.error("Passwords do not match!")
            else:
                success = AuthManager.reset_password(username, new_password)
                if success:
                    st.success(f"Password reset successfully for user '{username}'!")
                    st.rerun()
                else:
                    st.error("Failed to reset password.")
        
        if cancel_button:
            st.rerun()

def show_delete_user_confirmation(username):
    """Display user deletion confirmation."""
    st.subheader(f"Delete User: {username}")
    st.warning(f"Are you sure you want to delete user '{username}'? This action cannot be undone.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🗑️ Yes, Delete User", type="primary", key=f"confirm_delete_{username}"):
            success = AuthManager.delete_user(username)
            if success:
                st.success(f"User '{username}' has been deleted.")
                st.rerun()
            else:
                st.error("Failed to delete user.")
    
    with col2:
        if st.button("❌ Cancel", key=f"cancel_delete_{username}"):
            st.rerun()

def show_change_role_form(username, current_user):
    """Display role change form."""
    st.subheader(f"Change Role for: {username}")
    
    # Get current user role
    users = AuthManager.get_all_users()
    current_role = next((u['role'] for u in users if u['username'] == username), 'user')
    
    with st.form(f"change_role_{username}"):
        new_role = st.selectbox(
            "New Role", 
            ["user", "admin"], 
            index=0 if current_role == "user" else 1,
            key=f"role_{username}"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            change_button = st.form_submit_button("Change Role", type="primary")
        with col2:
            cancel_button = st.form_submit_button("Cancel")
        
        if change_button:
            if new_role == current_role:
                st.info("Role is already set to this value.")
            else:
                # Update user role by recreating user entry (simple approach)
                # In a production system, you'd have a dedicated update_role method
                st.info(f"Role change from '{current_role}' to '{new_role}' for user '{username}' requires manual database update.")
                st.warning("This feature will be implemented in the next update.")
        
        if cancel_button:
            st.rerun()