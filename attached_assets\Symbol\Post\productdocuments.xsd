﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProductDocuments" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="ProductDocuments" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ProductDocument">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="product" type="xs:string" minOccurs="0" />
              <xs:element name="productcode" type="xs:string" minOccurs="0" />
              <xs:element name="Document" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="name" type="xs:string" minOccurs="0" />
                    <xs:element name="document" type="xs:string" minOccurs="0" />
                    <xs:element name="category" type="xs:string" minOccurs="0" />
                    <xs:element name="folder" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>