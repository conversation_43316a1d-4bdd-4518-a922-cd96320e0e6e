"""
Sales Overview visualizations for the ERP Analytics Dashboard.
Contains functions for rendering sales performance metrics and trend charts.
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import numpy as np

def format_large_number(value):
    """Format large numbers to #,##M and #,##K format for better readability."""
    if value >= 1_000_000:
        return f"{value/1_000_000:.2f}M"
    elif value >= 1_000:
        return f"{value/1_000:.2f}K"
    else:
        return f"{value:.0f}"

def display_metric_with_growth(label, value, comparison_value=None, format_str="{:,.0f} Ft", percentage=False):
    """
    Display a metric with growth indicator.
    
    Args:
        label: Metric label
        value: Current value
        comparison_value: Previous period value for comparison
        format_str: String format for the value display
        percentage: Whether the value is a percentage
    """
    def format_number(value, format_str="{:,.0f} Ft"):
        """Format a number with thousands separator and optional currency."""
        if value is None:
            return "N/A"
        
        # Replace comma with space for Hungarian formatting
        formatted = format_str.format(value).replace(",", " ")
        return formatted
    
    def calculate_growth(current, previous):
        """Calculate the growth percentage between two values."""
        if previous is None or previous == 0:
            return None
        return ((current - previous) / abs(previous)) * 100
    
    # Format the main value
    formatted_value = format_number(value, format_str)
    
    # Set up columns for the metric display
    container = st.container()
    
    # Calculate and format growth if comparison value is provided
    growth_text = ""
    growth_color = ""
    
    if comparison_value is not None and value is not None:
        growth_pct = calculate_growth(value, comparison_value)
        
        if growth_pct is not None:
            # Determine growth indicator and style
            if growth_pct > 0:
                indicator = "↑"
                growth_color = "green"
            elif growth_pct < 0:
                indicator = "↓"
                growth_color = "red"
            else:
                indicator = "→"
                growth_color = "gray"
                
            # Calculate absolute difference
            abs_diff = value - comparison_value
            
            # Format absolute difference
            if percentage:
                # For percentages, show pp (percentage points) difference
                abs_diff_formatted = f"{abs(abs_diff):.1f}pp"
            else:
                # For regular numbers, use the same format as the main value
                abs_diff_formatted = format_number(abs(abs_diff), format_str)
            
            # Build the growth text
            growth_text = f"{indicator} {abs(growth_pct):.1f}% ({abs_diff_formatted})"
    
    # Render the metric with HTML using a safer approach
    with container:
        st.markdown(f"**{label}**", unsafe_allow_html=False)
        st.markdown(f"### {formatted_value}", unsafe_allow_html=False)
        if growth_text:
            st.markdown(f"<span style='color:{growth_color};font-size:0.8rem;'>{growth_text}</span>", unsafe_allow_html=True)

def safe_year_shift(date, years):
    """Safely shift a date by a number of years, handling leap years and other edge cases."""
    try:
        # Simple case: just add years
        return date.replace(year=date.year + years)
    except ValueError:
        # Handle Feb 29 in leap years
        if date.month == 2 and date.day == 29:
            # Move to Feb 28 in non-leap year
            return date.replace(year=date.year + years, day=28)
        # For other cases, we shouldn't hit this
        return date

def create_sales_trend_chart(primary_df, comparison_df=None, time_view="daily"):
    """
    Create a Zebra BI-style sales trend chart with current and previous year data.
    Automatically adapts to Streamlit's light/dark theme.
    
    Args:
        primary_df: DataFrame with current year data (must have 'date' and 'sales_value' columns)
        comparison_df: DataFrame with previous year data (optional)
        time_view: The time aggregation level - "daily", "weekly", or "monthly"
        
    Returns:
        A plotly figure object
    """
    
    # Get theme setting from session state (set by theme toggle in navigation)
    is_dark_theme = st.session_state.get('dark_theme', True)
    
    # Define color schemes for light and dark themes
    if is_dark_theme:
        theme_colors = {
            'bg_color': 'rgba(14, 17, 23, 0.0)',  # Transparent to inherit Streamlit's dark background
            'paper_bg': 'rgba(14, 17, 23, 0.0)',  # Transparent 
            'title_color': '#FAFAFA',  # Light text
            'text_color': '#FAFAFA',   # Light text
            'grid_color': 'rgba(255, 255, 255, 0.1)',  # Subtle light grid
            'zero_line': 'rgba(255, 255, 255, 0.2)',   # Subtle light zero line
            'previous_year': 'rgba(75, 85, 99, 0.8)',  # Dark gray for previous year bars
            'previous_year_border': 'rgba(107, 114, 128, 0.9)',
            'legend_bg': 'rgba(31, 41, 55, 0.7)',  # Dark legend background
            'legend_border': 'rgba(75, 85, 99, 0.5)',
            'hover_bg': 'rgba(31, 41, 55, 0.95)',  # Dark hover background
            'hover_border': 'rgba(75, 85, 99, 0.9)'
        }
    else:
        theme_colors = {
            'bg_color': 'rgba(250, 250, 252, 0.95)',
            'paper_bg': 'rgba(250, 250, 252, 0.95)', 
            'title_color': '#333333',
            'text_color': '#555555',
            'grid_color': 'rgba(230, 230, 235, 0.65)',
            'zero_line': 'rgba(230, 230, 235, 0.85)',
            'previous_year': 'rgba(220, 220, 230, 0.8)',
            'previous_year_border': 'rgba(200, 200, 210, 0.9)',
            'legend_bg': 'rgba(255, 255, 255, 0.7)',
            'legend_border': 'rgba(211, 211, 211, 0.5)',
            'hover_bg': 'rgba(255, 255, 255, 0.95)',
            'hover_border': 'rgba(211, 211, 211, 0.9)'
        }
    # Validate dataframes first
    if primary_df is None or len(primary_df) == 0:
        # Create an informational chart instead of returning None
        fig = go.Figure()
        fig.add_annotation(
            text="Not enough data available for this chart.<br>Please upload sales data for current and previous year.",
            showarrow=False,
            font=dict(size=14)
        )
        fig.update_layout(
            title="Sales Trend Over Time",
            height=400
        )
        return fig
        
    if 'sales_value' not in primary_df.columns:
        # Create an informational chart
        fig = go.Figure()
        fig.add_annotation(
            text="Sales value data missing.<br>Please check your data format.",
            showarrow=False,
            font=dict(size=14)
        )
        fig.update_layout(
            title="Sales Trend Over Time",
            height=400
        )
        return fig
        
    # Create subplot with secondary y-axis
    fig = make_subplots(specs=[[{"secondary_y": True}]])
    
    # Ensure date column is datetime and handle None values
    if primary_df is not None and 'date' in primary_df.columns:
        primary_df['date'] = pd.to_datetime(primary_df['date'])
        # Sort by date
        primary_df = primary_df.sort_values('date')
    
    # Check if we have comparison data
    has_comparison = False
    if comparison_df is not None:
        if len(comparison_df) > 0:
            if 'sales_value' in comparison_df.columns and 'date' in comparison_df.columns:
                has_comparison = True
                # Ensure date column is datetime for comparison_df
                comparison_df['date'] = pd.to_datetime(comparison_df['date'])
                
                # Sort by date
                comparison_df = comparison_df.sort_values('date')
    
    if has_comparison:
        
        # Shift comparison dates to align with primary dates (if needed)
        # This is important if we're comparing different year periods
        if len(comparison_df) == len(primary_df):
            # Direct alignment - keep the comparison dates but align values
            aligned_comp_df = pd.DataFrame({
                'date': primary_df['date'],
                'sales_value': comparison_df['sales_value'].values
            })
        else:
            # More complex alignment needed
            # Create a date mapping by month-day (ignoring year)
            comp_date_map = {}
            for _, row in comparison_df.iterrows():
                month_day = (row['date'].month, row['date'].day)
                comp_date_map[month_day] = row['sales_value']
            
            # Create aligned comparison data
            aligned_values = []
            for date in primary_df['date']:
                month_day = (date.month, date.day)
                if month_day in comp_date_map:
                    aligned_values.append(comp_date_map[month_day])
                else:
                    aligned_values.append(0)  # No matching date in comparison
            
            aligned_comp_df = pd.DataFrame({
                'date': primary_df['date'],
                'sales_value': aligned_values
            })
        
        # Calculate growth/delta values
        primary_df['delta'] = primary_df['sales_value'] - aligned_comp_df['sales_value']
        
        # Calculate AC/PY ratio (percentage of previous year)
        # Using a safer approach to avoid errors
        primary_df['ratio'] = 100  # Default value
        
        # Only calculate ratios if we have valid comparison data
        try:
            for idx, row in primary_df.iterrows():
                # Find matching date in aligned comparison dataframe
                comp_rows = aligned_comp_df[aligned_comp_df['date'] == row['date']]
                
                if not comp_rows.empty:
                    comp_value = comp_rows['sales_value'].iloc[0]
                    # Avoid division by zero
                    if comp_value > 0:
                        primary_df.at[idx, 'ratio'] = 100 * row['sales_value'] / comp_value
        except Exception as e:
            st.warning(f"Error calculating ratio values: {str(e)}")
            # Keep the default value of 100
        
        # Create base bars for Previous Year (PY)
        fig.add_trace(
            go.Bar(
                x=primary_df['date'],
                y=aligned_comp_df['sales_value'],
                name='Previous Year',
                marker=dict(
                    color=theme_colors['previous_year'],
                    line=dict(color=theme_colors['previous_year_border'], width=1)
                ),
                hovertemplate='<b>%{x}</b><br>Previous Year: %{y:,.0f} Ft<extra></extra>',
                opacity=0.9
            )
        )
        
        # Add delta bars on top with color based on growth/decline
        # Split into positive and negative delta for different colors
        positive_delta = primary_df[primary_df['delta'] > 0].copy()
        negative_delta = primary_df[primary_df['delta'] < 0].copy()
        
        if len(positive_delta) > 0:
            fig.add_trace(
                go.Bar(
                    x=positive_delta['date'],
                    y=positive_delta['delta'],
                    name='Growth',
                    marker=dict(
                        color='rgba(66, 183, 87, 0.85)',  # Softer green for growth
                        line=dict(color='rgba(56, 163, 77, 1)', width=1)
                    ),
                    hovertemplate='<b>%{x}</b><br>Growth: +%{y:,.0f} Ft<extra></extra>',
                    opacity=0.95
                )
            )
            
        if len(negative_delta) > 0:
            fig.add_trace(
                go.Bar(
                    x=negative_delta['date'],
                    y=negative_delta['delta'],
                    name='Decline',
                    marker=dict(
                        color='rgba(244, 67, 54, 0.8)',  # Red for decline
                        line=dict(color='rgba(214, 57, 44, 1)', width=1)
                    ),
                    hovertemplate='<b>%{x}</b><br>Decline: %{y:,.0f} Ft<extra></extra>',
                    opacity=0.95
                )
            )
        
        # Add the ratio line on secondary y-axis
        fig.add_trace(
            go.Scatter(
                x=primary_df['date'],
                y=primary_df['ratio'],
                name='AC/PY Ratio',
                line=dict(
                    color='rgba(33, 150, 243, 0.95)', 
                    width=2.5,
                    shape='spline',  # Smooth curve
                    smoothing=0.3
                ),
                mode='lines+markers',
                marker=dict(
                    size=7,
                    symbol='circle',
                    color='rgba(33, 150, 243, 0.95)',
                    line=dict(color='rgba(255, 255, 255, 0.8)', width=1)
                ),
                hovertemplate='<b>%{x}</b><br>AC/PY Ratio: <b>%{y:.1f}%</b><extra></extra>',
                yaxis='y2'
            )
        )
        
        # Add a 100% reference line
        fig.add_trace(
            go.Scatter(
                x=[primary_df['date'].min(), primary_df['date'].max()],
                y=[100, 100],
                name='100% Reference',
                line=dict(
                    color='rgba(0, 0, 0, 0.25)', 
                    width=1.5, 
                    dash='dot'
                ),
                hoverinfo='skip',
                yaxis='y2'
            )
        )
    else:
        # Simple chart without comparison
        fig.add_trace(
            go.Bar(
                x=primary_df['date'],
                y=primary_df['sales_value'],
                name='Sales Value',
                marker=dict(
                    color='rgba(33, 150, 243, 0.85)',  # Blue for current year with transparency
                    line=dict(color='rgba(25, 118, 210, 1)', width=1),
                    opacity=0.95
                ),
                hovertemplate='<b>%{x}</b><br>Sales Value: <b>%{y:,.0f} Ft</b><extra></extra>'
            )
        )
    
    # Customize the layout based on time_view
    # Set appropriate title and tick formats based on time aggregation
    title_suffix = ""
    tick_format = '%b %d, %Y'  # Default format
    
    if time_view.lower() == "weekly":
        title_suffix = " (Weekly View)"
        tick_format = '%Y-W%W'  # Year and week number
    elif time_view.lower() == "monthly":
        title_suffix = " (Monthly View)"
        tick_format = '%b %Y'  # Month and year
    else:  # daily
        title_suffix = " (Daily View)"
    
    fig.update_layout(
        barmode='stack',
        plot_bgcolor=theme_colors['bg_color'],
        paper_bgcolor=theme_colors['paper_bg'],
        height=500,
        xaxis=dict(
            title=dict(
                text="Date",
                font=dict(family="Arial, sans-serif", size=14, color=theme_colors['text_color'])
            ),
            gridcolor=theme_colors['grid_color'],
            zerolinecolor=theme_colors['zero_line'],
            tickfont=dict(family="Arial, sans-serif", size=12, color=theme_colors['text_color']),
            tickformat=tick_format,
            tickangle=-30 if time_view == "monthly" else 0
        ),
        yaxis=dict(
            title=dict(
                text="Sales Value (Ft)",
                font=dict(family="Arial, sans-serif", size=14, color=theme_colors['text_color'])
            ),
            gridcolor=theme_colors['grid_color'],
            zerolinecolor=theme_colors['zero_line'],
            tickformat=',d',
            ticksuffix=' Ft',
            tickfont=dict(family="Arial, sans-serif", size=12, color=theme_colors['text_color'])
        ),
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5,
            bgcolor=theme_colors['legend_bg'],
            bordercolor=theme_colors['legend_border'],
            borderwidth=1,
            font=dict(family="Arial, sans-serif", size=12, color=theme_colors['text_color'])
        ),
        margin=dict(l=50, r=50, t=80, b=80),
        hoverlabel=dict(
            bgcolor=theme_colors['hover_bg'],
            bordercolor=theme_colors['hover_border'],
            font=dict(family="Arial, sans-serif", size=12, color=theme_colors['text_color'])
        )
    )
    
    # Configure the secondary y-axis for the ratio
    if has_comparison:
        fig.update_layout(
            yaxis2=dict(
                title=dict(
                    text="AC/PY Ratio (%)",
                    font=dict(
                        family="Arial, sans-serif", 
                        size=14, 
                        color="rgba(33, 150, 243, 0.95)"  # Keep blue for ratio axis
                    )
                ),
                tickfont=dict(
                    family="Arial, sans-serif", 
                    size=12, 
                    color="rgba(33, 150, 243, 0.95)"  # Keep blue for ratio axis
                ),
                tickformat='.0f',
                ticksuffix='%',
                gridcolor='rgba(33, 150, 243, 0.08)',
                zerolinecolor='rgba(33, 150, 243, 0.2)',
                overlaying='y',
                side='right',
                range=[0, max(200, primary_df['ratio'].max() * 1.1) if not primary_df['ratio'].empty else 200],  # Dynamic range with ceiling of at least 200%
                showgrid=True
            )
        )
    
    return fig

def render_sales_overview(session, start_date, end_date, comparison_start_date=None, comparison_end_date=None,
                         countries=None, product_groups=None, brands=None, buyer_categories=None,
                         buyer_names=None, suppliers=None):
    """
    Render the sales overview section including metrics and trend chart.
    
    Args:
        session: SQLAlchemy database session
        start_date: Start date for current period
        end_date: End date for current period
        comparison_start_date: Start date for comparison period (optional)
        comparison_end_date: End date for comparison period (optional)
        countries: List of countries to filter by (optional)
        product_groups: List of product groups to filter by (optional)
        brands: List of brands to filter by (optional)
        buyer_categories: List of buyer categories to filter by (optional)
        buyer_names: List of specific buyer names to filter by (optional)
        suppliers: List of suppliers to filter by (optional)
    """
    from models import Sale, Buyer, Product
    from sqlalchemy.sql import func
    from sqlalchemy import and_, or_
    
    try:
        # Base query for primary period
        base_query = session.query(
            func.date(Sale.sale_date).label('date'),
            func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
            func.sum(Sale.quantity).label('units_sold'),
            func.count(Sale.id.distinct()).label('transaction_count'),
            func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
        ).join(Buyer).join(Product)
        
        # Apply date filter for primary period
        base_query = base_query.filter(and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date
        ))
        
        # Apply other filters
        if countries:
            base_query = base_query.filter(Buyer.country.in_(countries))
        
        if product_groups:
            base_query = base_query.filter(Product.product_group.in_(product_groups))
            
        if brands:
            base_query = base_query.filter(Product.brand.in_(brands))
            
        if buyer_categories:
            base_query = base_query.filter(Buyer.buyer_category.in_(buyer_categories))
            
        if buyer_names:
            base_query = base_query.filter(Buyer.buyer_name.in_(buyer_names))
            
        if suppliers:
            base_query = base_query.filter(Product.primary_supplier.in_(suppliers))
        
        # Group by date and execute query
        primary_sales = base_query.group_by(func.date(Sale.sale_date)).all()
        
        # Get total metrics for the primary period
        totals_query = session.query(
            func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
            func.sum(Sale.quantity).label('total_units_sold'),
            func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit'),
            func.count(Sale.id.distinct()).label('total_transactions')
        ).join(Buyer).join(Product).filter(and_(
            Sale.sale_date >= start_date,
            Sale.sale_date <= end_date
        ))
        
        # Apply the same filters as the base query
        if countries:
            totals_query = totals_query.filter(Buyer.country.in_(countries))
        
        if product_groups:
            totals_query = totals_query.filter(Product.product_group.in_(product_groups))
            
        if brands:
            totals_query = totals_query.filter(Product.brand.in_(brands))
            
        if buyer_categories:
            totals_query = totals_query.filter(Buyer.buyer_category.in_(buyer_categories))
            
        if buyer_names:
            totals_query = totals_query.filter(Buyer.buyer_name.in_(buyer_names))
            
        if suppliers:
            totals_query = totals_query.filter(Product.primary_supplier.in_(suppliers))
        
        # Execute totals query
        totals = totals_query.first()
        
        primary_data = {
            'total_revenue': getattr(totals, 'total_revenue', 0) or 0,
            'total_units_sold': getattr(totals, 'total_units_sold', 0) or 0,
            'total_profit': getattr(totals, 'total_profit', 0) or 0,
            'total_transactions': getattr(totals, 'total_transactions', 0) or 0,
            'display_metric_with_growth': display_metric_with_growth
        }
        
        # Similar queries for comparison period if dates are provided
        comparison_sales = []
        comparison_data = {
            'total_revenue': 0,
            'total_units_sold': 0,
            'total_profit': 0,
            'total_transactions': 0
        }
        
        if comparison_start_date and comparison_end_date:
            # Base query for comparison period
            comp_query = session.query(
                func.date(Sale.sale_date).label('date'),
                func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                func.sum(Sale.quantity).label('units_sold'),
                func.count(Sale.id.distinct()).label('transaction_count'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
            ).join(Buyer).join(Product)
            
            # Apply date filter for comparison period
            comp_query = comp_query.filter(and_(
                Sale.sale_date >= comparison_start_date,
                Sale.sale_date <= comparison_end_date
            ))
            
            # Apply other filters
            if countries:
                comp_query = comp_query.filter(Buyer.country.in_(countries))
            
            if product_groups:
                comp_query = comp_query.filter(Product.product_group.in_(product_groups))
                
            if brands:
                comp_query = comp_query.filter(Product.brand.in_(brands))
                
            if buyer_categories:
                comp_query = comp_query.filter(Buyer.buyer_category.in_(buyer_categories))
                
            if buyer_names:
                comp_query = comp_query.filter(Buyer.buyer_name.in_(buyer_names))
                
            if suppliers:
                comp_query = comp_query.filter(Product.primary_supplier.in_(suppliers))
            
            # Group by date and execute query
            comparison_sales = comp_query.group_by(func.date(Sale.sale_date)).all()
            
            # Get total metrics for the comparison period
            comp_totals_query = session.query(
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity).label('total_units_sold'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit'),
                func.count(Sale.id.distinct()).label('total_transactions')
            ).join(Buyer).join(Product).filter(and_(
                Sale.sale_date >= comparison_start_date,
                Sale.sale_date <= comparison_end_date
            ))
            
            # Apply the same filters as the base query
            if countries:
                comp_totals_query = comp_totals_query.filter(Buyer.country.in_(countries))
            
            if product_groups:
                comp_totals_query = comp_totals_query.filter(Product.product_group.in_(product_groups))
                
            if brands:
                comp_totals_query = comp_totals_query.filter(Product.brand.in_(brands))
                
            if buyer_categories:
                comp_totals_query = comp_totals_query.filter(Buyer.buyer_category.in_(buyer_categories))
                
            if buyer_names:
                comp_totals_query = comp_totals_query.filter(Buyer.buyer_name.in_(buyer_names))
                
            if suppliers:
                comp_totals_query = comp_totals_query.filter(Product.primary_supplier.in_(suppliers))
            
            # Execute comparison totals query
            comp_totals = comp_totals_query.first()
            
            comparison_data = {
                'total_revenue': getattr(comp_totals, 'total_revenue', 0) or 0,
                'total_units_sold': getattr(comp_totals, 'total_units_sold', 0) or 0,
                'total_profit': getattr(comp_totals, 'total_profit', 0) or 0,
                'total_transactions': getattr(comp_totals, 'total_transactions', 0) or 0
            }
        
        # Calculate profit margin
        if primary_data['total_revenue'] > 0:
            profit_margin = (primary_data['total_profit'] / primary_data['total_revenue']) * 100
        else:
            profit_margin = 0
            
        if comparison_data['total_revenue'] > 0:
            comparison_margin = (comparison_data['total_profit'] / comparison_data['total_revenue']) * 100
        else:
            comparison_margin = 0
        
        # Render Sales Performance Overview metrics
        st.subheader("Sales Performance Overview")
        
        kpi_cols = st.columns(6)
        
        # Revenue KPI
        with kpi_cols[0]:
            st.metric(
                label="Revenue",
                value=format_large_number(primary_data['total_revenue']),
                delta=f"{((primary_data['total_revenue'] / comparison_data['total_revenue']) - 1) * 100:.1f}%" 
                      if comparison_data['total_revenue'] > 0 else None
            )
        
        # Profit KPI
        with kpi_cols[1]:
            st.metric(
                label="Profit",
                value=format_large_number(primary_data['total_profit']),
                delta=f"{((primary_data['total_profit'] / comparison_data['total_profit']) - 1) * 100:.1f}%" 
                      if comparison_data['total_profit'] > 0 else None
            )
        
        # Units Sold KPI
        with kpi_cols[2]:
            st.metric(
                label="Units Sold",
                value=format_large_number(primary_data['total_units_sold']),
                delta=f"{((primary_data['total_units_sold'] / comparison_data['total_units_sold']) - 1) * 100:.1f}%" 
                      if comparison_data['total_units_sold'] > 0 else None
            )
        
        # Transaction KPI
        with kpi_cols[3]:
            st.metric(
                label="Transactions",
                value=format_large_number(primary_data['total_transactions']),
                delta=f"{((primary_data['total_transactions'] / comparison_data['total_transactions']) - 1) * 100:.1f}%" 
                      if comparison_data['total_transactions'] > 0 else None
            )
        
        # Average Order Value KPI
        with kpi_cols[4]:
            avg_order_value = primary_data['total_revenue'] / primary_data['total_transactions'] if primary_data['total_transactions'] > 0 else 0
            comparison_avg_order = comparison_data['total_revenue'] / comparison_data['total_transactions'] if comparison_data['total_transactions'] > 0 else 0
            st.metric(
                label="Avg. Order Value",
                value=format_large_number(avg_order_value),
                delta=f"{((avg_order_value / comparison_avg_order) - 1) * 100:.1f}%" 
                      if comparison_avg_order > 0 else None
            )
        
        # Margin KPI
        with kpi_cols[5]:
            st.metric(
                label="Margin",
                value=f"{profit_margin:.1f}%",
                delta=f"{profit_margin - comparison_margin:.1f}pp" 
                      if comparison_margin > 0 else None
            )
        
        # Render Sales Trend Over Time
        st.subheader("Sales Trend Over Time")
        
        # Add custom CSS for pill-style buttons
        st.markdown("""
        <style>
        div.row-widget.stRadio > div {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label {
            background-color: #f0f2f6;
            border: 1px solid #ddd;
            padding: 6px 16px;
            border-radius: 20px;
            margin: 0 6px;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label:hover {
            background-color: #e0e2e6;
            border-color: #ccc;
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label[aria-checked="true"] {
            background-color: #0068C9;
            border-color: #0068C9;
            color: white;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label[data-baseweb="radio"] > div:first-child {
            display: none;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Time selector above the chart
        time_options = ["Daily", "Weekly", "Monthly"]
        time_option = st.radio(
            label="",
            options=time_options,
            index=2,  # Monthly (last option)
            horizontal=True,
            label_visibility="collapsed"
        )
        
        # Create the sales trend visualization
        try:
            # Check if we have any data to display
            if not primary_sales:
                st.info("No sales data available for the selected time period.")
                return
            
            # Prepare data for the chart
            current_df = pd.DataFrame(primary_sales, columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
            
            # Prepare comparison data if available
            if comparison_sales:
                comparison_df = pd.DataFrame(comparison_sales, columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
            else:
                comparison_df = pd.DataFrame(columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
            
            # Ensure date columns are datetime objects with error handling
            try:
                current_df['date'] = pd.to_datetime(current_df['date'])
                if not comparison_df.empty:
                    comparison_df['date'] = pd.to_datetime(comparison_df['date'])
            except Exception as e:
                st.warning(f"Date conversion issue: {str(e)}. Some dates may not display correctly.")
            
            # Aggregate data based on selected time period
            if time_option == "Weekly":
                # Create a week column
                current_df['week'] = current_df['date'].dt.isocalendar().week
                current_df['year'] = current_df['date'].dt.isocalendar().year
                
                # Create a string representation for week display: YYYY-WXX
                current_df['week_str'] = current_df['year'].astype(str) + "-W" + current_df['week'].astype(str).str.zfill(2)
                
                # Group by year and week
                current_df = current_df.groupby(['year', 'week', 'week_str']).agg({
                    'date': 'first',  # Keep first date in the week
                    'sales_value': 'sum',
                    'units_sold': 'sum',
                    'transaction_count': 'sum',
                    'profit': 'sum'
                }).reset_index()
                
                # Do the same for comparison data if available
                if not comparison_df.empty:
                    comparison_df['week'] = comparison_df['date'].dt.isocalendar().week
                    comparison_df['year'] = comparison_df['date'].dt.isocalendar().year
                    comparison_df['week_str'] = comparison_df['year'].astype(str) + "-W" + comparison_df['week'].astype(str).str.zfill(2)
                    
                    comparison_df = comparison_df.groupby(['year', 'week', 'week_str']).agg({
                        'date': 'first',  # Keep first date in the week
                        'sales_value': 'sum',
                        'units_sold': 'sum',
                        'transaction_count': 'sum',
                        'profit': 'sum'
                    }).reset_index()
            
            elif time_option == "Monthly":
                # Create month and year columns
                current_df['month'] = current_df['date'].dt.month
                current_df['year'] = current_df['date'].dt.year
                
                # Group by year and month
                current_df = current_df.groupby(['year', 'month']).agg({
                    'date': lambda x: x.iloc[0].replace(day=1),  # First day of the month
                    'sales_value': 'sum',
                    'units_sold': 'sum',
                    'transaction_count': 'sum',
                    'profit': 'sum'
                }).reset_index()
                
                # Do the same for comparison data if available
                if not comparison_df.empty:
                    comparison_df['month'] = comparison_df['date'].dt.month
                    comparison_df['year'] = comparison_df['date'].dt.year
                    
                    comparison_df = comparison_df.groupby(['year', 'month']).agg({
                        'date': lambda x: x.iloc[0].replace(day=1),  # First day of the month
                        'sales_value': 'sum',
                        'units_sold': 'sum',
                        'transaction_count': 'sum',
                        'profit': 'sum'
                    }).reset_index()
            
            # We need to align the comparison data to match the current period's date range
            if not comparison_df.empty:
                # For week and monthly mode, we're trying to compare same weeks/months across years
                if time_option in ["Weekly", "Monthly"]:
                    # For weekly mode, we want to match week numbers
                    if time_option == "Weekly":
                        # First, build a lookup of comparison_df week data
                        comp_week_lookup = {}
                        
                        for _, row in comparison_df.iterrows():
                            key = (row['week'], row['year'])
                            comp_week_lookup[key] = row
                        
                        # Then align based on week number from current data
                        aligned_data = []
                        
                        for _, row in current_df.iterrows():
                            week_num = row['week']
                            year_diff = row['year'] - comparison_df['year'].iloc[0]
                            
                            # Look for the corresponding week in the previous year
                            lookup_key = (week_num, row['year'] - year_diff)
                            
                            if lookup_key in comp_week_lookup:
                                matching_row = comp_week_lookup[lookup_key]
                                aligned_data.append({
                                    'date': matching_row['date'],
                                    'sales_value': matching_row['sales_value'],
                                    'units_sold': matching_row['units_sold'],
                                    'transaction_count': matching_row['transaction_count'],
                                    'profit': matching_row['profit'],
                                    'aligned_date': row['date']
                                })
                        
                        if aligned_data:
                            comparison_df = pd.DataFrame(aligned_data)
                        else:
                            comparison_df['aligned_date'] = comparison_df['date'].apply(
                                lambda x: safe_year_shift(x, year_diff)
                            )
                    else:
                        comparison_df['aligned_date'] = comparison_df['date'].copy()
                else:
                    # Regular date alignment for daily views
                    if current_df.shape[0] > 0 and comparison_df.shape[0] > 0:
                        year_diff = current_df['date'].iloc[0].year - comparison_df['date'].iloc[0].year
                        # Apply the safer year shifting function
                        comparison_df['aligned_date'] = comparison_df['date'].apply(
                            lambda x: safe_year_shift(x, year_diff)
                        )
                    else:
                        comparison_df['aligned_date'] = comparison_df['date'].copy()
            else:
                comparison_df['aligned_date'] = pd.Series(dtype='datetime64[ns]')
            
            # Call the chart function with the correct parameters
            sales_trend_fig = create_sales_trend_chart(
                primary_df=current_df, 
                comparison_df=comparison_df, 
                time_view=time_option.lower()
            )
            
            st.plotly_chart(sales_trend_fig, use_container_width=True)
            
            # Add a note about the aggregation
            if time_option == "Daily":
                st.caption("Showing daily sales values")
            elif time_option == "Weekly":
                st.caption("Data aggregated by week - each point represents a week's total")
            else:  # Monthly
                st.caption("Data aggregated by month - each point represents a month's total")
            
        except Exception as e:
            st.error(f"Error displaying sales trend chart: {str(e)}")
            st.info("We're having trouble displaying the sales trend chart. Please try adjusting your filters or upload sales data if none exists.")
    
    except Exception as e:
        st.error(f"Error rendering sales overview: {str(e)}")
        st.info("We're having trouble loading the sales overview. Please check your database connection and try again.")