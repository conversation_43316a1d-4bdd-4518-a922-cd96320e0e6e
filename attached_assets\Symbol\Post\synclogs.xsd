﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SyncLogs" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="SyncLogs" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="SyncLog">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EventType" type="xs:string" minOccurs="0" />
              <xs:element name="EventDate" type="xs:string" minOccurs="0" />
              <xs:element name="Employee" type="xs:string" minOccurs="0" />
              <xs:element name="Subject" type="xs:string" minOccurs="0" />
              <xs:element name="Description" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>