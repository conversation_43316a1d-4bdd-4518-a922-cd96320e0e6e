import streamlit as st
import pandas as pd
from database import get_session
from models import Stock, Product
from components.stock_analytics_functions import (
    get_current_inventory, process_inventory_data,
    get_stock_value_analysis, process_value_analysis_data,
    create_warehouse_distribution_chart, create_top_products_chart,
    create_margin_comparison_chart
)

def show():
    """Display the stock analytics page."""
    # Use custom styling to create header with no top margin
    st.markdown('<h1 style="margin:0;padding:0;line-height:1;">Stock Analytics</h1>', unsafe_allow_html=True)
    
    # Get database session
    session = get_session()
    if not session:
        st.error("Failed to connect to the database. Please check your connection settings.")
        return
    
    try:
        # Add filters in main area with expander (collapsed by default)
        with st.expander("Filters", expanded=False):
            # Create a grid layout for filters
            col1, col2 = st.columns(2)
            col3, col4 = st.columns(2)
            
            # Get all warehouse options
            warehouses = [w[0] for w in session.query(Stock.warehouse).distinct().all() if w[0]]
            warehouses = ["All"] + warehouses if warehouses else ["All"]
            
            # Add warehouse filter
            with col1:
                st.write("Warehouse")
                selected_warehouse = st.selectbox(
                    "Warehouse",
                    options=warehouses,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Get brands for filtering
            brands = [b[0] for b in session.query(Product.brand).distinct().all() if b[0]]
            brands = ["All"] + brands if brands else ["All"]
            
            # Add brand filter
            with col2:
                st.write("Brand")
                selected_brand = st.selectbox(
                    "Brand",
                    options=brands,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Get product categories for filtering
            product_groups = [pg[0] for pg in session.query(Product.product_group).distinct().all() if pg[0]]
            product_groups = ["All"] + product_groups if product_groups else ["All"]
            
            # Add product category filter
            with col3:
                st.write("Product Category")
                selected_product_category = st.selectbox(
                    "Product Category",
                    options=product_groups,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Add minimum stock value filter (hidden, set to 0)
            min_stock_value = 0
            
            # Convert "All" selections to empty lists for filtering
            selected_warehouses = [] if selected_warehouse == "All" else [selected_warehouse]
            selected_product_groups = [] if selected_product_category == "All" else [selected_product_category]
            selected_brands = [] if selected_brand == "All" else [selected_brand]
        
        # Tabs for different stock views
        tab1, tab2, tab3 = st.tabs(["Inventory Overview", "Stock Value Analysis", "Stock Movements"])
        
        with tab1:
            st.subheader("Current Inventory Status")
            
            # Get stock data using the modular function
            stock_data = get_current_inventory(
                session, 
                selected_warehouses=selected_warehouses,
                selected_product_groups=selected_product_groups,
                min_stock_value=min_stock_value
            )
            
            # Process the data
            stock_df = process_inventory_data(stock_data)
            
            if stock_df is not None:
                # Display summary metrics
                total_value = stock_df['Value'].sum()
                total_items = stock_df['Quantity'].sum()
                total_products = stock_df['Product ID'].nunique()
                avg_value = total_value / total_products if total_products > 0 else 0
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.write("Total Inventory Value")
                    st.write(f"### {total_value:,.0f} Ft")
                with col2:
                    st.write("Total Products")
                    st.write(f"### {total_products:,.0f}")
                with col3:
                    st.write("Total Items in Stock")
                    st.write(f"### {total_items:,.0f}")
                with col4:
                    st.write("Avg. Value Per Product")
                    st.write(f"### {avg_value:,.0f} Ft")
                
                # Display as interactive table
                st.dataframe(stock_df, use_container_width=True)
                
                # Visualization: Inventory value by warehouse
                st.subheader("Inventory Value by Warehouse")
                warehouse_chart = create_warehouse_distribution_chart(stock_df)
                st.plotly_chart(warehouse_chart, use_container_width=True)
                
                # Visualization: Top 10 products by value
                st.subheader("Top 10 Products by Inventory Value")
                top_products_chart = create_top_products_chart(stock_df)
                st.plotly_chart(top_products_chart, use_container_width=True)
            else:
                st.info("No stock data available. Please check your filters or upload stock data.")
        
        with tab2:
            st.subheader("Stock Value Analysis")
            
            # Get value analysis data
            value_data = get_stock_value_analysis(
                session,
                selected_warehouses=selected_warehouses,
                selected_product_groups=selected_product_groups,
                min_stock_value=min_stock_value
            )
            
            # Process the data
            value_df = process_value_analysis_data(value_data)
            
            if value_df is not None:
                # Display summary metrics
                col1, col2 = st.columns(2)
                with col1:
                    st.metric(
                        "Potential Store Revenue", 
                        f"{value_df['Potential Store Value'].sum():,.0f} Ft",
                        f"Profit: {value_df['Potential Store Profit'].sum():,.0f} Ft"
                    )
                with col2:
                    st.metric(
                        "Potential Online Revenue", 
                        f"{value_df['Potential Online Value'].sum():,.0f} Ft",
                        f"Profit: {value_df['Potential Online Profit'].sum():,.0f} Ft"
                    )
                
                # Display as interactive table
                st.dataframe(value_df, use_container_width=True)
                
                # Visualization: Margin comparison by product group
                st.subheader("Margin Comparison by Product Group")
                margin_chart = create_margin_comparison_chart(value_df)
                st.plotly_chart(margin_chart, use_container_width=True)
            else:
                st.info("No stock value data available. Please check your filters or upload stock and price data.")
        
        with tab3:
            st.subheader("Stock Movements")
            st.info("This feature will track stock movements over time once historical data is available.")
            st.warning("To enable stock movement tracking, upload multiple stock snapshots over time.")
            
    except Exception as e:
        st.error(f"Error retrieving stock data: {str(e)}")
    finally:
        # Close the session
        session.close()
