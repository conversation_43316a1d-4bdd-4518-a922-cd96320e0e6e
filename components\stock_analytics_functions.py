"""
Stock analytics helper functions for data querying, processing, and visualization.
These functions support the stock_analytics.py page.
"""
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from sqlalchemy import func
from models import Stock, Product, Price

def get_current_inventory(session, selected_warehouses=None, selected_product_groups=None, min_stock_value=0):
    """
    Query current stock levels with filters.
    
    Args:
        session: SQLAlchemy database session
        selected_warehouses: List of warehouses to filter by (empty list means no filter)
        selected_product_groups: List of product groups to filter by (empty list means no filter)
        min_stock_value: Minimum stock value to include (0 means no minimum)
        
    Returns:
        List of tuples with product and stock information
    """
    # Query current stock levels
    stock_query = session.query(
        Product.product_id,
        Product.product_name,
        Product.product_group,
        Stock.warehouse,
        func.sum(Stock.quantity).label('quantity'),
        func.sum(Stock.value).label('value')
    ).join(Stock, Stock.product_id == Product.product_id)
    
    # Apply filters
    if selected_warehouses:
        stock_query = stock_query.filter(Stock.warehouse.in_(selected_warehouses))
        
    if selected_product_groups:
        stock_query = stock_query.filter(Product.product_group.in_(selected_product_groups))
        
    if min_stock_value > 0:
        # This will be applied after group by
        having_clause = func.sum(Stock.value) >= min_stock_value
    else:
        having_clause = True
        
    # Get results
    return stock_query.group_by(
        Product.product_id, 
        Product.product_name,
        Product.product_group,
        Stock.warehouse
    ).having(having_clause).all()

def process_inventory_data(stock_data):
    """
    Process stock data into a pandas DataFrame with appropriate columns.
    
    Args:
        stock_data: List of tuples from get_current_inventory
    
    Returns:
        Pandas DataFrame with inventory data
    """
    if not stock_data:
        return None
        
    # Convert to dataframe
    stock_df = pd.DataFrame(stock_data, columns=[
        'Product ID', 'Product Name', 'Product Group', 'Warehouse', 'Quantity', 'Value'
    ])
    
    return stock_df

def get_stock_value_analysis(session, selected_warehouses=None, selected_product_groups=None, min_stock_value=0):
    """
    Query stock value data with price information for profit calculations.
    
    Args:
        session: SQLAlchemy database session
        selected_warehouses: List of warehouses to filter by (empty list means no filter)
        selected_product_groups: List of product groups to filter by (empty list means no filter)
        min_stock_value: Minimum stock value to include (0 means no minimum)
        
    Returns:
        List of tuples with product, stock and price information
    """
    # Get product with stock value and price data to calculate potential profits
    value_query = session.query(
        Product.product_id,
        Product.product_name,
        Product.product_group,
        func.sum(Stock.quantity).label('quantity'),
        func.sum(Stock.value).label('cost_value'),
        func.avg(Price.store_price).label('avg_store_price'),
        func.avg(Price.online_price).label('avg_online_price')
    ).join(Stock, Stock.product_id == Product.product_id) \
     .outerjoin(Price, Price.product_id == Product.product_id)
    
    # Apply filters
    if selected_warehouses:
        value_query = value_query.filter(Stock.warehouse.in_(selected_warehouses))
        
    if selected_product_groups:
        value_query = value_query.filter(Product.product_group.in_(selected_product_groups))
        
    if min_stock_value > 0:
        # This will be applied after group by
        having_clause = func.sum(Stock.value) >= min_stock_value
    else:
        having_clause = True
        
    # Get results
    return value_query.group_by(
        Product.product_id, 
        Product.product_name,
        Product.product_group
    ).having(having_clause).all()

def process_value_analysis_data(value_data):
    """
    Process stock value data into a pandas DataFrame with calculated profit metrics.
    
    Args:
        value_data: List of tuples from get_stock_value_analysis
    
    Returns:
        Pandas DataFrame with value analysis data including calculated profit metrics
    """
    if not value_data:
        return None
        
    # Convert to dataframe
    value_df = pd.DataFrame(value_data, columns=[
        'Product ID', 'Product Name', 'Product Group', 'Quantity', 
        'Cost Value', 'Avg Store Price', 'Avg Online Price'
    ])
    
    # Calculate potential sale values and profit margins
    value_df['Potential Store Value'] = value_df['Quantity'] * value_df['Avg Store Price']
    value_df['Potential Online Value'] = value_df['Quantity'] * value_df['Avg Online Price']
    value_df['Potential Store Profit'] = value_df['Potential Store Value'] - value_df['Cost Value']
    value_df['Potential Online Profit'] = value_df['Potential Online Value'] - value_df['Cost Value']
    value_df['Store Margin %'] = (value_df['Potential Store Profit'] / value_df['Potential Store Value'] * 100).round(2)
    value_df['Online Margin %'] = (value_df['Potential Online Profit'] / value_df['Potential Online Value'] * 100).round(2)
    
    # Replace NaN with 0
    value_df = value_df.fillna(0)
    
    return value_df

def create_warehouse_distribution_chart(stock_df):
    """
    Create a donut chart showing inventory value distribution by warehouse.
    
    Args:
        stock_df: Pandas DataFrame from process_inventory_data
    
    Returns:
        Plotly figure object
    """
    warehouse_summary = stock_df.groupby('Warehouse')['Value'].sum().reset_index()
    
    fig = px.pie(
        warehouse_summary, 
        values='Value', 
        names='Warehouse',
        title='Inventory Value Distribution by Warehouse',
        hole=0.4
    )
    
    return fig

def create_top_products_chart(stock_df, limit=10):
    """
    Create a bar chart showing top products by inventory value.
    
    Args:
        stock_df: Pandas DataFrame from process_inventory_data
        limit: Number of top products to show
    
    Returns:
        Plotly figure object
    """
    top_products = stock_df.groupby(['Product Name', 'Product ID'])['Value'].sum().reset_index().sort_values('Value', ascending=False).head(limit)
    
    fig = px.bar(
        top_products,
        x='Product Name',
        y='Value',
        hover_data=['Product ID'],
        title=f'Top {limit} Products by Inventory Value'
    )
    
    return fig

def create_margin_comparison_chart(value_df):
    """
    Create a grouped bar chart comparing margins by product group and sales channel.
    
    Args:
        value_df: Pandas DataFrame from process_value_analysis_data
    
    Returns:
        Plotly figure object
    """
    # Calculate margins by product group
    margin_df = value_df.groupby('Product Group').agg({
        'Cost Value': 'sum',
        'Potential Store Value': 'sum',
        'Potential Online Value': 'sum'
    }).reset_index()
    
    margin_df['Store Margin %'] = ((margin_df['Potential Store Value'] - margin_df['Cost Value']) / margin_df['Potential Store Value'] * 100).round(2)
    margin_df['Online Margin %'] = ((margin_df['Potential Online Value'] - margin_df['Cost Value']) / margin_df['Potential Online Value'] * 100).round(2)
    
    # Convert to long format for plotting
    margin_long = pd.melt(
        margin_df, 
        id_vars=['Product Group'], 
        value_vars=['Store Margin %', 'Online Margin %'],
        var_name='Channel',
        value_name='Margin %'
    )
    
    fig = px.bar(
        margin_long,
        x='Product Group',
        y='Margin %',
        color='Channel',
        title='Margin Comparison by Product Group and Sales Channel',
        barmode='group'
    )
    
    return fig