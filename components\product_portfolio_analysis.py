import streamlit as st
import pandas as pd
import numpy as np
from sqlalchemy import func, desc, and_
from models import Sale, Buyer, Product, ProductCategory, product_category_association

def get_brand_product_data(session, start_date, end_date, countries=None, product_groups=None, brands=None, 
                   buyer_categories=None, buyer_names=None, suppliers=None, product_filter=None, web_categories=None):
    """
    Get sales data grouped by brand and product group with revenue, profit, and units sold metrics.
    
    Args:
        session: SQLAlchemy database session
        start_date: Start date for data range
        end_date: End date for data range
        countries: Optional list of countries to filter
        product_groups: Optional list of product groups to filter
        brands: Optional list of brands to filter
        buyer_categories: Optional list of buyer categories to filter
        buyer_names: Optional list of specific buyer names to filter by
        suppliers: Optional list of suppliers to filter by
        product_filter: Optional product code filter (case-insensitive partial match)
        web_categories: Optional list of web categories to filter by
        
    Returns:
        brand_df: DataFrame with brand-level aggregated data
        product_group_df: DataFrame with product-group level data
    """
    # Base query for brand level data
    brand_query = session.query(
        Product.brand,
        func.sum(Sale.quantity * Sale.unit_price).label('revenue'),
        func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit'),
        func.sum(Sale.quantity).label('units_sold')
    ).join(
        Product, Sale.product_id == Product.product_id
    ).filter(
        Sale.sale_date.between(start_date, end_date),
        Product.brand != None,  # Exclude NULL brands
        Product.brand != ''     # Exclude empty brands
    )
    
    # Product group level query 
    product_group_query = session.query(
        Product.brand,
        Product.product_group,
        func.sum(Sale.quantity * Sale.unit_price).label('revenue'),
        func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit'),
        func.sum(Sale.quantity).label('units_sold')
    ).join(
        Product, Sale.product_id == Product.product_id
    ).filter(
        Sale.sale_date.between(start_date, end_date),
        Product.brand != None,  # Exclude NULL brands
        Product.brand != '',    # Exclude empty brands
        Product.product_group != None,  # Exclude NULL product groups
        Product.product_group != ''     # Exclude empty product groups
    )
    
    # Apply filters
    # Determine if we need to join Buyer table
    needs_buyer_join = countries or buyer_categories or buyer_names
    
    # Add Buyer join if needed for buyer-related filters
    if needs_buyer_join:
        brand_query = brand_query.join(Buyer, Sale.buyer_id == Buyer.buyer_id)
        product_group_query = product_group_query.join(Buyer, Sale.buyer_id == Buyer.buyer_id)
        
        # Apply country filter
        if countries:
            brand_query = brand_query.filter(Buyer.country.in_(countries))
            product_group_query = product_group_query.filter(Buyer.country.in_(countries))
        
        # Apply buyer category filter
        if buyer_categories:
            brand_query = brand_query.filter(Buyer.buyer_category.in_(buyer_categories))
            product_group_query = product_group_query.filter(Buyer.buyer_category.in_(buyer_categories))
        
        # Apply specific buyer filter (buyer_names parameter actually contains buyer IDs)
        if buyer_names:
            brand_query = brand_query.filter(Buyer.buyer_id.in_(buyer_names))
            product_group_query = product_group_query.filter(Buyer.buyer_id.in_(buyer_names))
    
    # Apply product-related filters
    if product_groups:
        brand_query = brand_query.filter(Product.product_group.in_(product_groups))
        product_group_query = product_group_query.filter(Product.product_group.in_(product_groups))
    
    if brands:
        brand_query = brand_query.filter(Product.brand.in_(brands))
        product_group_query = product_group_query.filter(Product.brand.in_(brands))
        
    # Apply supplier filter
    if suppliers:
        brand_query = brand_query.filter(Product.primary_supplier.in_(suppliers))
        product_group_query = product_group_query.filter(Product.primary_supplier.in_(suppliers))
    
    # Apply product filter
    if product_filter:
        brand_query = brand_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
        product_group_query = product_group_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
    
    # Apply web categories filter
    if web_categories:
        brand_query = brand_query.join(
            product_category_association, 
            Product.product_id == product_category_association.c.product_id
        ).join(
            ProductCategory, 
            product_category_association.c.category_id == ProductCategory.id
        ).filter(ProductCategory.category_name.in_(web_categories))
        
        product_group_query = product_group_query.join(
            product_category_association, 
            Product.product_id == product_category_association.c.product_id
        ).join(
            ProductCategory, 
            product_category_association.c.category_id == ProductCategory.id
        ).filter(ProductCategory.category_name.in_(web_categories))
    
    # Group and order results
    brand_results = brand_query.group_by(
        Product.brand
    ).order_by(desc('revenue')).all()
    
    product_group_results = product_group_query.group_by(
        Product.brand, 
        Product.product_group
    ).order_by(
        Product.brand, 
        desc('revenue')
    ).all()
    
    # Convert to dataframes
    if brand_results:
        brand_df = pd.DataFrame(brand_results, columns=['brand', 'revenue', 'profit', 'units_sold'])
        # Calculate margin
        brand_df['margin'] = (brand_df['profit'] / brand_df['revenue'] * 100).round(2)
        # Calculate market share (% of total revenue)
        total_revenue = brand_df['revenue'].sum()
        brand_df['market_share'] = (brand_df['revenue'] / total_revenue * 100).round(2)
    else:
        brand_df = pd.DataFrame(columns=['brand', 'revenue', 'profit', 'units_sold', 'margin', 'market_share'])
    
    if product_group_results:
        product_group_df = pd.DataFrame(product_group_results, 
                                       columns=['brand', 'product_group', 'revenue', 'profit', 'units_sold'])
        # Calculate margin
        product_group_df['margin'] = (product_group_df['profit'] / product_group_df['revenue'] * 100).round(2)
        # Calculate overall market share
        total_revenue = product_group_df['revenue'].sum()
        product_group_df['market_share'] = (product_group_df['revenue'] / total_revenue * 100).round(2)
        
        # Calculate share within brand safely
        # First create a copy to avoid index issues
        share_df = product_group_df.copy()
        
        # Initialize share_within_brand column with zeros
        product_group_df['share_within_brand'] = 0.0
        
        # Calculate share for each brand separately
        for brand_name in share_df['brand'].unique():
            brand_mask = share_df['brand'] == brand_name
            brand_total = share_df.loc[brand_mask, 'revenue'].sum()
            if brand_total > 0:  # Avoid division by zero
                # Calculate for each product in this brand
                for idx in share_df.loc[brand_mask].index:
                    product_revenue = share_df.loc[idx, 'revenue']
                    share_pct = (product_revenue / brand_total * 100).round(2)
                    # Update in the original dataframe
                    product_group_df.loc[idx, 'share_within_brand'] = share_pct
    else:
        product_group_df = pd.DataFrame(
            columns=['brand', 'product_group', 'revenue', 'profit', 'units_sold', 
                    'margin', 'share_within_brand', 'market_share']
        )
    
    return brand_df, product_group_df

def calculate_year_over_year_changes(current_df, comparison_df, level='brand'):
    """
    Calculate year-over-year changes between current and comparison periods.
    
    Args:
        current_df: DataFrame with current period data
        comparison_df: DataFrame with comparison period data
        level: 'brand' or 'product_group' to indicate which level of analysis
        
    Returns:
        DataFrame with the original current data plus change metrics
    """
    # Handle empty dataframes
    if current_df.empty or comparison_df.empty:
        # Create a copy of current dataframe to avoid modifying the original
        if not current_df.empty:
            result_df = current_df.copy()
        else:
            # If both dataframes are empty, return an empty dataframe with the correct columns
            columns = ['brand', 'revenue', 'profit', 'units_sold', 'margin', 'market_share']
            if level == 'product_group':
                columns.insert(1, 'product_group')
            result_df = pd.DataFrame(columns=columns)
            
        # Add empty change columns
        for col in ['revenue_change', 'revenue_change_pct', 'profit_change', 
                   'profit_change_pct', 'margin_change', 'units_change', 
                   'units_change_pct', 'market_share_change']:
            result_df[col] = np.nan
        return result_df
    
    # Determine the columns to use for merging based on level
    merge_cols = ['brand'] if level == 'brand' else ['brand', 'product_group']
    
    try:
        # Merge current and comparison data
        merged_df = current_df.merge(
            comparison_df,
            on=merge_cols,
            suffixes=('', '_prev')
        )
        
        # Calculate changes (both absolute and percentage)
        # Revenue changes
        merged_df['revenue_change'] = merged_df['revenue'] - merged_df['revenue_prev']
        merged_df['revenue_change_pct'] = np.where(
            merged_df['revenue_prev'] > 0,
            ((merged_df['revenue'] / merged_df['revenue_prev']) - 1) * 100,
            np.nan
        )
        
        # Profit changes
        merged_df['profit_change'] = merged_df['profit'] - merged_df['profit_prev']
        merged_df['profit_change_pct'] = np.where(
            merged_df['profit_prev'] > 0,
            ((merged_df['profit'] / merged_df['profit_prev']) - 1) * 100,
            np.nan
        )
        
        # Margin change (percentage points) with error handling
        try:
            prev_margin = np.where(
                merged_df['revenue_prev'] > 0,
                (merged_df['profit_prev'] / merged_df['revenue_prev']) * 100,
                0
            )
            merged_df['margin_change'] = merged_df['margin'] - prev_margin
        except Exception as e:
            # If there's an error calculating margin change, set to NaN
            merged_df['margin_change'] = np.nan
            st.warning(f"Error calculating margin change: {e}")
        
        # Units sold changes
        if 'units_sold' in merged_df.columns and 'units_sold_prev' in merged_df.columns:
            merged_df['units_change'] = merged_df['units_sold'] - merged_df['units_sold_prev']
            merged_df['units_change_pct'] = np.where(
                merged_df['units_sold_prev'] > 0,
                ((merged_df['units_sold'] / merged_df['units_sold_prev']) - 1) * 100,
                np.nan
            )
        else:
            merged_df['units_change'] = np.nan
            merged_df['units_change_pct'] = np.nan
        
        # Market share change (percentage points)
        if 'market_share' in merged_df.columns and 'market_share_prev' in merged_df.columns:
            merged_df['market_share_change'] = merged_df['market_share'] - merged_df['market_share_prev']
        else:
            merged_df['market_share_change'] = np.nan
        
        return merged_df
    
    except Exception as e:
        # If there's an error in merging or calculations, return the current data with empty change columns
        st.warning(f"Error calculating year-over-year changes: {e}")
        result_df = current_df.copy()
        for col in ['revenue_change', 'revenue_change_pct', 'profit_change', 
                   'profit_change_pct', 'margin_change', 'units_change', 
                   'units_change_pct', 'market_share_change']:
            result_df[col] = np.nan
        return result_df

def format_currency(value):
    """Format currency values with appropriate suffixes (B, M, K)."""
    if pd.isna(value) or value == 0:
        return "0 Ft"
    elif abs(value) >= 1_000_000_000:
        return f"{value/1_000_000_000:.1f}B Ft"
    elif abs(value) >= 1_000_000:
        return f"{value/1_000_000:.1f}M Ft"
    elif abs(value) >= 1_000:
        return f"{value/1_000:.1f}K Ft"
    else:
        return f"{value:.0f} Ft"

def format_percent(value, with_sign=False, decimal_places=1):
    """Format percentage values with optional sign."""
    if pd.isna(value):
        return ""
    
    if with_sign:
        return f"{value:+.{decimal_places}f}%"
    else:
        return f"{value:.{decimal_places}f}%"

def format_units(value):
    """Format unit values with appropriate suffixes (M, K)."""
    if pd.isna(value) or value == 0:
        return "0"
    elif abs(value) >= 1_000_000:
        return f"{value/1_000_000:.1f}M"
    elif abs(value) >= 1_000:
        return f"{value/1_000:.1f}K"
    else:
        return f"{value:.0f}"

def render_product_portfolio_table(brand_df, product_group_df, sort_col='revenue', ascending=False):
    """Renders a table showing brand and product group data with metrics."""
    if brand_df.empty:
        st.info("No brand data available for the selected filters.")
        return

    # Apply sorting
    if sort_col in brand_df.columns:
        brand_df = brand_df.sort_values(by=sort_col, ascending=ascending, na_position='last')

    # Initialize session state for expanded brands if not already done
    if 'expanded_brands' not in st.session_state:
        st.session_state['expanded_brands'] = set()
        
    # Add CSS for styling
    st.markdown("""
    <style>
    /* Table styling */
    .portfolio-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 10px;
        margin-top: 4px;
    }
    .portfolio-table th {
        background-color: #f5f5f5;
        padding: 2px 4px;
        text-align: left;
        font-weight: 600;
        border: 1px solid #ddd;
        font-size: 10px;
    }
    .portfolio-table td {
        padding: 1px 4px;
        border: 1px solid #ddd;
        font-size: 10px;
    }
    .portfolio-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .portfolio-table tr:hover {
        background-color: #f0f0f0;
    }
    .portfolio-table th:not(:first-child), 
    .portfolio-table td:not(:first-child) {
        text-align: right;
    }
    .product-group-row {
        background-color: #f9f9f9 !important;
    }
    
    /* Reduce vertical spacing between blocks */
    div[data-testid="stVerticalBlock"] > div {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    /* Shrink horizontal block height */
    div[data-testid="stHorizontalBlock"] {
        min-height: 20px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    /* Minimize padding inside buttons */
    div[data-testid="stHorizontalBlock"] button {
        padding: 0.1rem 0.2rem !important;
        font-size: 0.7rem !important;
        line-height: 1 !important;
        height: 22px !important;
        min-height: 0;
    }

    /* Shrink markdown (used in table cells) */
    .element-container > div > div > div > div > div {
        padding: 0 2px !important;
        font-size: 0.75rem !important;
        line-height: 1 !important;
    }

    /* Remove extra line spacing */
    .css-1y4p8pa {
        line-height: 1 !important;
        margin: 0 !important;
    }
    
    /* Reduce header sizes */
    h1, h2, h3, h4, h5, h6 {
        font-size: 0.85rem !important;
        margin-bottom: 0.2rem !important;
        margin-top: 0.2rem !important;
        padding: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Create sortable column headers
    cols = st.columns([3, 2, 2, 2, 2, 2, 2])
    
    # Helper function to create sort buttons
    def create_sort_button(col, label, column_name):
        is_current = sort_col == column_name
        button_type = "primary" if is_current else "secondary"
        direction_icon = "↓" if is_current and not ascending else "↑" if is_current else ""
        
        if col.button(f"{label} {direction_icon}", key=f"sort_{column_name}", 
                    type=button_type, use_container_width=True):
            if is_current:
                # Toggle direction
                st.session_state.portfolio_sort_direction = 'asc' if not ascending else 'desc'
            else:
                # New column
                st.session_state.portfolio_sort_column = column_name
                # Default direction based on column type
                is_numeric = column_name in ['revenue', 'profit', 'units_sold', 'market_share']
                st.session_state.portfolio_sort_direction = 'desc' if is_numeric else 'asc'
            st.rerun()
    
    # Create the sort buttons for each column
    create_sort_button(cols[0], "Brand/Product", "brand")
    create_sort_button(cols[1], "Revenue", "revenue")
    create_sort_button(cols[2], "Revenue Change", "revenue_change_pct")
    create_sort_button(cols[3], "Profit", "profit")
    create_sort_button(cols[4], "Profit Change", "profit_change_pct")
    create_sort_button(cols[5], "Units", "units_sold")
    create_sort_button(cols[6], "% Total", "market_share")
    
    # Helper functions for formatting
    def format_currency(val):
        if pd.isna(val): return 'N/A'
        if val >= 1_000_000:
            return f"{val/1_000_000:.1f}M"
        if val >= 1_000:
            return f"{val/1_000:.1f}K"
        return f"{val:.0f}"
    
    def format_percent(val, with_sign=False):
        if pd.isna(val): return "N/A"
        sign = "+" if val >= 0 and with_sign else ""
        color = "green" if val >= 0 else "red"
        return f"<span style='color:{color};font-weight:600;'>{sign}{val:.1f}%</span>"
    
    def format_units(val):
        if pd.isna(val): return 'N/A'
        if val >= 1_000_000:
            return f"{val/1_000_000:.1f}M"
        if val >= 1_000:
            return f"{val/1_000:.1f}K"
        return f"{val:.0f}"
    
    # Create a DataFrame for display
    display_rows = []
    
    # Process each brand and its product groups
    for _, row in brand_df.iterrows():
        brand = row['brand']
        is_expanded = brand in st.session_state['expanded_brands']
        
        # Add brand row
        brand_row = {
            'Brand/Product': brand,
            'Revenue': format_currency(row['revenue']),
            'Revenue Change': format_percent(row.get('revenue_change_pct', float('nan')), with_sign=True),
            'Profit': format_currency(row['profit']),
            'Profit Change': format_percent(row.get('profit_change_pct', float('nan')), with_sign=True),
            'Units': format_units(row['units_sold']),
            '% Total': format_percent(row.get('market_share', float('nan')))
        }
        display_rows.append(brand_row)
        
        # If brand is expanded, add its product groups
        if is_expanded:
            brand_products = product_group_df[product_group_df['brand'] == brand]
            brand_products = brand_products.sort_values(by='revenue', ascending=False)
            
            for _, prod_row in brand_products.iterrows():
                product_row = {
                    'Brand/Product': f"└─ {prod_row['product_group']}",
                    'Revenue': format_currency(prod_row['revenue']),
                    'Revenue Change': format_percent(prod_row.get('revenue_change_pct', float('nan')), with_sign=True),
                    'Profit': format_currency(prod_row['profit']),
                    'Profit Change': format_percent(prod_row.get('profit_change_pct', float('nan')), with_sign=True),
                    'Units': format_units(prod_row['units_sold']),
                    '% Total': format_percent(prod_row.get('share_within_brand', float('nan')))
                }
                display_rows.append(product_row)
    
    # Create DataFrame from rows
    display_df = pd.DataFrame(display_rows)
    
    # Apply additional styling to the table
    styled_html = display_df.to_html(escape=False, index=False)
    styled_html = styled_html.replace('<table', '<table class="portfolio-table" style="margin:0;padding:0;"')
    styled_html = styled_html.replace('<tr>', '<tr style="height:18px;line-height:1;">')
    styled_html = styled_html.replace('<td>', '<td style="padding:1px 3px;font-size:10px;">')
    styled_html = styled_html.replace('<th>', '<th style="padding:1px 3px;font-size:10px;font-weight:600;background:#f5f5f5;">')
    
    # Display the styled table
    st.write(styled_html, unsafe_allow_html=True)
    
    # Add brand expansion buttons in a more compact layout
    st.markdown("<div style='margin:0;padding:4px 0 0 0;font-size:11px;font-weight:600;'>Quick Brand Toggles:</div>", unsafe_allow_html=True)
    
    # Use more columns to fit more buttons in a row
    brand_cols = st.columns(5)  # Increased from 4 to 5
    
    for i, brand in enumerate(brand_df['brand']):
        col_idx = i % 5
        is_expanded = brand in st.session_state['expanded_brands']
        icon = "▼" if is_expanded else "▶"
        
        # Use smaller, more compact buttons
        if brand_cols[col_idx].button(f"{icon} {brand}", 
                                    key=f"toggle_{brand}", 
                                    type="secondary" if not is_expanded else "primary",
                                    use_container_width=True):
            if is_expanded:
                st.session_state['expanded_brands'].remove(brand)
            else:
                st.session_state['expanded_brands'].add(brand)
            st.rerun()

def render_portfolio_table_header(sort_column=None, sort_direction=None):
    """
    Render the header row for the portfolio table with sortable columns.
    
    Args:
        sort_column: Currently sorted column name
        sort_direction: 'asc' or 'desc' to indicate sort direction
    """
    # Add compact styling for the entire table
    st.markdown("""
    <style>
    /* Make rows more compact */
    div[data-testid="stHorizontalBlock"] {
        padding: 0 !important;
        margin: 0 !important;
        min-height: 28px !important;
    }
    
    /* Reduce button padding and size */
    div[data-testid="stHorizontalBlock"] button {
        padding: 0.1rem 0.3rem !important;
        font-size: 0.8rem !important;
        min-height: 0 !important;
        height: 24px !important;
        line-height: 1 !important;
    }
    
    /* Reduce spacing between rows */
    hr {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding: 0 !important;
        height: 1px !important;
    }
    
    /* Make text in cells more compact */
    div[data-testid="stHorizontalBlock"] > div {
        padding: 0 4px !important;
        font-size: 0.8rem !important;
        line-height: 1 !important;
        display: flex !important;
        align-items: center !important;
    }
    
    /* Reduce overall vertical spacing */
    div[data-testid="stVerticalBlock"] > div {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Create a unique prefix for all keys in this tab to avoid conflicts with other tabs
    key_prefix = "portfolio_"
    
    # Create the column headers with sort functionality and better styling
    col1, col2, col3, col4, col5, col6, col7 = st.columns([3, 1.5, 1, 1.5, 1, 1.5, 1])
    
    # Define common button styles
    btn_style = {
        "use_container_width": True,
        "type": "secondary"
    }
    
    # Helper function to create sort indicators
    def get_sort_indicator(col_name):
        if sort_column == col_name:
            return "▲" if sort_direction == 'asc' else "▼"
        return ""
    
    # Create buttons for each column header
    with col1:
        if st.button(f"Brand/Product {get_sort_indicator('brand')}", key=f"{key_prefix}sort_brand", **btn_style):
            if sort_column == 'brand':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'brand'
                st.session_state.portfolio_sort_direction = 'asc'
            st.rerun()
    
    with col2:
        if st.button(f"Revenue {get_sort_indicator('revenue')}", key=f"{key_prefix}sort_revenue", **btn_style):
            if sort_column == 'revenue':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'revenue'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    with col3:
        if st.button(f"Change {get_sort_indicator('revenue_change_pct')}", key=f"{key_prefix}sort_revenue_change", **btn_style):
            if sort_column == 'revenue_change_pct':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'revenue_change_pct'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    with col4:
        if st.button(f"Profit {get_sort_indicator('profit')}", key=f"{key_prefix}sort_profit", **btn_style):
            if sort_column == 'profit':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'profit'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    with col5:
        if st.button(f"Change {get_sort_indicator('profit_change_pct')}", key=f"{key_prefix}sort_profit_change", **btn_style):
            if sort_column == 'profit_change_pct':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'profit_change_pct'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    with col6:
        if st.button(f"Units {get_sort_indicator('units_sold')}", key=f"{key_prefix}sort_units", **btn_style):
            if sort_column == 'units_sold':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'units_sold'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    with col7:
        if st.button(f"% Total {get_sort_indicator('market_share')}", key=f"{key_prefix}sort_share", **btn_style):
            if sort_column == 'market_share':
                st.session_state.portfolio_sort_direction = 'desc' if sort_direction == 'asc' else 'asc'
            else:
                st.session_state.portfolio_sort_column = 'market_share'
                st.session_state.portfolio_sort_direction = 'desc'
            st.rerun()
    
    # Add a horizontal line below the header buttons
    st.markdown("<hr style='margin-top: 0; margin-bottom: 0; height: 1px; border: none; background-color: #ddd;'>", unsafe_allow_html=True)

def render_brand_row(row, is_expanded):
    """
    Render a brand row in the portfolio table.
    
    Args:
        row: DataFrame row with brand data
        is_expanded: Boolean indicating if this brand is currently expanded
    """
    brand_name = row['brand']
    
    # Create columns for metrics with the same proportions as the header
    col1, col2, col3, col4, col5, col6, col7 = st.columns([3, 1.5, 1, 1.5, 1, 1.5, 1])

    # Place the brand name using a button that looks like text
    with col1:
        if st.button(
            f"{'▼' if is_expanded else '▶'} {brand_name}",
            key=f"portfolio_brand_{brand_name}",
            type="secondary" if not is_expanded else "primary",
            use_container_width=True
        ):
            st.session_state.expanded_brand = None if st.session_state.expanded_brand == brand_name else brand_name
            st.rerun()

    # Helper function for consistent cell styling
    def metric_cell(value, color=None):
        styled = f'<div style="width:100%;text-align:right;padding:0;margin:0;">{value}</div>' if not color else f'<div style="color:{color};width:100%;text-align:right;padding:0;margin:0;font-weight:600;">{value}</div>'
        return styled

    # Display the metrics with right alignment and compact styling
    with col2:
        st.markdown(metric_cell(format_currency(row['revenue']) + " Ft"), unsafe_allow_html=True)

    with col3:
        pct = row.get('revenue_change_pct', np.nan)
        col = "green" if not pd.isna(pct) and pct >= 0 else "red"
        st.markdown(metric_cell(format_percent(pct, with_sign=True), col if not pd.isna(pct) else None), unsafe_allow_html=True)

    with col4:
        st.markdown(metric_cell(format_currency(row['profit']) + " Ft"), unsafe_allow_html=True)

    with col5:
        pct = row.get('profit_change_pct', np.nan)
        col = "green" if not pd.isna(pct) and pct >= 0 else "red"
        st.markdown(metric_cell(format_percent(pct, with_sign=True), col if not pd.isna(pct) else None), unsafe_allow_html=True)

    with col6:
        st.markdown(metric_cell(format_units(row['units_sold'])), unsafe_allow_html=True)

    with col7:
        st.markdown(metric_cell(format_percent(row['market_share'])), unsafe_allow_html=True)

    # Minimal separator
    st.markdown('<hr style="margin:0;padding:0;height:1px;border:none;background-color:#eee;">', unsafe_allow_html=True)

def render_product_group_row(row):
    """
    Render a product group row (child row) in the portfolio table.
    
    Args:
        row: DataFrame row with product group data
    """
    product_name = row['product_group']
    
    # Create columns for metrics with the same proportions as the header
    col1, col2, col3, col4, col5, col6, col7 = st.columns([3, 1.5, 1, 1.5, 1, 1.5, 1])

    # Helper function for consistent cell formatting
    def cell_html(value, color=None, indent=False):
        margin = "margin-left:20px;" if indent else ""
        text_align = "text-align:right;" if not indent else ""
        return f'<div style="width:100%;{margin}{text_align}padding:0;margin-top:0;margin-bottom:0;{"color:" + color + ";" if color else ""}">{value}</div>'

    # Display the metrics with right alignment and compact styling
    with col1:
        st.markdown(cell_html(f"└─ {product_name}", color="#505050", indent=True), unsafe_allow_html=True)

    with col2:
        st.markdown(cell_html(format_currency(row['revenue']) + " Ft"), unsafe_allow_html=True)

    with col3:
        pct = row.get('revenue_change_pct', np.nan)
        col = "green" if not pd.isna(pct) and pct >= 0 else "red"
        st.markdown(cell_html(format_percent(pct, with_sign=True), col if not pd.isna(pct) else None), unsafe_allow_html=True)

    with col4:
        st.markdown(cell_html(format_currency(row['profit']) + " Ft"), unsafe_allow_html=True)

    with col5:
        pct = row.get('profit_change_pct', np.nan)
        col = "green" if not pd.isna(pct) and pct >= 0 else "red"
        st.markdown(cell_html(format_percent(pct, with_sign=True), col if not pd.isna(pct) else None), unsafe_allow_html=True)

    with col6:
        st.markdown(cell_html(format_units(row['units_sold'])), unsafe_allow_html=True)

    with col7:
        st.markdown(cell_html(format_percent(row.get('share_within_brand', np.nan))), unsafe_allow_html=True)

    # Minimal separator with lighter color for product group rows
    st.markdown('<hr style="margin:0;padding:0;height:1px;border:none;background-color:#f0f0f0;">', unsafe_allow_html=True)

def render_portfolio_analysis(session, start_date, end_date, comparison_start_date=None, comparison_end_date=None,
                              countries=None, product_groups=None, brands=None, buyer_categories=None,
                              buyer_names=None, suppliers=None, max_brands=None, product_filter=None, web_categories=None):
    """
    Main function to render the product portfolio analysis visualization.
    """
    # Create session state variables if they don't exist
    if 'expanded_brand' not in st.session_state:
        st.session_state.expanded_brand = None
    
    if 'portfolio_sort_column' not in st.session_state:
        st.session_state.portfolio_sort_column = 'revenue'
    
    if 'portfolio_sort_direction' not in st.session_state:
        st.session_state.portfolio_sort_direction = 'desc'
    
    # Handle database session validation
    if session is None:
        from database import get_session
        st.warning("Database session is not initialized. Attempting to create a new session...")
        session = get_session()
        if session is None:
            st.error("Failed to create database session. Please check your database connection.")
            return
    
    # Get brand and product group data for the current period
    try:
        current_brand_df, current_product_group_df = get_brand_product_data(
            session, start_date, end_date, 
            countries, product_groups, brands, buyer_categories,
            buyer_names, suppliers, product_filter, web_categories
        )
    except Exception as e:
        st.error(f"Error retrieving product data: {str(e)}")
        return
    
    # If there's a comparison period, get that data too
    comparison_brand_df = pd.DataFrame()
    comparison_product_group_df = pd.DataFrame()
    
    if comparison_start_date and comparison_end_date:
        try:
            comparison_brand_df, comparison_product_group_df = get_brand_product_data(
                session, comparison_start_date, comparison_end_date,
                countries, product_groups, brands, buyer_categories,
                buyer_names, suppliers, product_filter, web_categories
            )
        except Exception as e:
            st.warning(f"Error retrieving comparison data: {str(e)}. Proceeding without comparison metrics.")
    
    # Calculate year-over-year changes
    if not comparison_brand_df.empty:
        brand_df_for_viz = calculate_year_over_year_changes(
            current_brand_df, comparison_brand_df, level='brand'
        )
        
        # Also calculate changes for product groups if needed
        if not comparison_product_group_df.empty:
            product_group_df_for_viz = calculate_year_over_year_changes(
                current_product_group_df, comparison_product_group_df, level='product_group'
            )
        else:
            product_group_df_for_viz = current_product_group_df
    else:
        brand_df_for_viz = current_brand_df
        product_group_df_for_viz = current_product_group_df
    
    # Limit to max brands if specified
    if max_brands and len(brand_df_for_viz) > max_brands:
        # Sort by revenue before limiting
        sort_col = st.session_state.get('portfolio_sort_column', 'revenue')
        ascending = st.session_state.get('portfolio_sort_direction', 'desc') == 'asc'
        
        # Get the top brands
        top_brands = brand_df_for_viz.sort_values(
            by=sort_col if sort_col in brand_df_for_viz.columns else 'revenue',
            ascending=ascending
        ).head(max_brands)['brand'].tolist()
        
        # Filter dataframes to include only top brands
        brand_df_for_viz = brand_df_for_viz[brand_df_for_viz['brand'].isin(top_brands)]
        product_group_df_for_viz = product_group_df_for_viz[product_group_df_for_viz['brand'].isin(top_brands)]
    
    # Get sorting parameters from session state
    sort_col = st.session_state.get('portfolio_sort_column', 'revenue')
    ascending = st.session_state.get('portfolio_sort_direction', 'desc') == 'asc'
    
    # Render the portfolio table header
    render_portfolio_table_header(sort_col, "asc" if ascending else "desc")
    
    # Render each brand row and its product groups if expanded
    for _, row in brand_df_for_viz.sort_values(by=sort_col, ascending=ascending).iterrows():
        is_expanded = st.session_state.expanded_brand == row['brand']
        render_brand_row(row, is_expanded)
        
        # If brand is expanded, show its product groups
        if is_expanded:
            brand_products = product_group_df_for_viz[product_group_df_for_viz['brand'] == row['brand']]
            brand_products = brand_products.sort_values(by='revenue', ascending=False)
            
            for _, prod_row in brand_products.iterrows():
                render_product_group_row(prod_row)
    
    # Show a message if brands were omitted due to max_brands limit
    if max_brands and len(brand_df_for_viz) >= max_brands:
        st.caption(f"Showing top {max_brands} brands. Use filters to narrow results.")
    
    # Calculate overall metrics
    total_revenue = brand_df_for_viz['revenue'].sum() if not brand_df_for_viz.empty else 0
    total_profit = brand_df_for_viz['profit'].sum() if not brand_df_for_viz.empty else 0
    total_units = brand_df_for_viz['units_sold'].sum() if not brand_df_for_viz.empty else 0
    overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
    
    # Calculate the total change percentages if we have comparison data
    total_revenue_change = None
    total_profit_change = None
    
    if comparison_start_date and comparison_end_date and not comparison_brand_df.empty:
        total_comparison_revenue = comparison_brand_df['revenue'].sum() if not comparison_brand_df.empty else 0
        total_comparison_profit = comparison_brand_df['profit'].sum() if not comparison_brand_df.empty else 0
        
        if total_comparison_revenue > 0:
            total_revenue_change = ((total_revenue - total_comparison_revenue) / total_comparison_revenue) * 100
        
        if total_comparison_profit > 0:
            total_profit_change = ((total_profit - total_comparison_profit) / total_comparison_profit) * 100
    
    if not brand_df_for_viz.empty:
        # Add separator before totals
        st.markdown('<hr style="margin:0;margin-top:5px;padding:0;height:2px;border:none;background-color:#ddd;">', unsafe_allow_html=True)
        
        # Create a summary row with totals
        col1, col2, col3, col4, col5, col6, col7 = st.columns([3, 1.5, 1, 1.5, 1, 1.5, 1])
        
        with col1:
            st.markdown('<div style="font-weight:600;width:100%;padding:0;margin-top:0;margin-bottom:0;">TOTAL</div>', unsafe_allow_html=True)
        
        with col2:
            st.markdown(f'<div style="font-weight:600;width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">{format_currency(total_revenue)} Ft</div>', unsafe_allow_html=True)
        
        with col3:
            # Show revenue change if available
            if total_revenue_change is not None:
                sign = "+" if total_revenue_change >= 0 else ""
                color = "green" if total_revenue_change >= 0 else "red"
                st.markdown(f'<div style="font-weight:600;color:{color};width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">{sign}{total_revenue_change:.1f}%</div>', unsafe_allow_html=True)
            else:
                st.markdown('<div style="width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">-</div>', unsafe_allow_html=True)
        
        with col4:
            st.markdown(f'<div style="font-weight:600;width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">{format_currency(total_profit)} Ft</div>', unsafe_allow_html=True)
        
        with col5:
            # Show profit change if available
            if total_profit_change is not None:
                sign = "+" if total_profit_change >= 0 else ""
                color = "green" if total_profit_change >= 0 else "red"
                st.markdown(f'<div style="font-weight:600;color:{color};width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">{sign}{total_profit_change:.1f}%</div>', unsafe_allow_html=True)
            else:
                st.markdown('<div style="width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">-</div>', unsafe_allow_html=True)
        
        with col6:
            st.markdown(f'<div style="font-weight:600;width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">{format_units(total_units)}</div>', unsafe_allow_html=True)
        
        with col7:
            st.markdown('<div style="font-weight:600;width:100%;text-align:right;padding:0;margin-top:0;margin-bottom:0;">100.0%</div>', unsafe_allow_html=True)
        
        # Display the overall profit margin
        st.markdown(f"""
        <div style='
            background-color: #f5f5f5;
            padding: 4px 10px 8px;
            margin-bottom: 10px;
            border-radius: 0 0 4px 4px;
            color: #666;
        '>
            <b>Overall Profit Margin: {format_percent(overall_margin)}</b>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.info("No brand data available for the selected filters. Please adjust your filters or upload product sales data.")









