"""
Overview page visualizations for the ERP Analytics Dashboard.
Contains functions for rendering overview metrics and charts.
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from utils import (
    get_sales_by_brand,
    get_sales_by_month,
    get_stock_by_warehouse,
    get_purchases_by_supplier,
    get_top_products,
    get_top_buyers
)

def format_currency(value, currency="HUF", precision=0):
    """Format currency value for display."""
    if currency == "HUF":
        return f"{value:,.{precision}f} Ft"
    elif currency == "EUR":
        return f"€{value:,.{precision}f}"
    elif currency == "USD":
        return f"${value:,.{precision}f}"
    elif currency == "RON":
        return f"{value:,.{precision}f} RON"
    else:
        return f"{value:,.{precision}f} {currency}"

def create_kpi_metrics(kpis, currency="HUF"):
    """Create KPI metrics display."""
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Sales Revenue", format_currency(kpis['sales_revenue'], currency))
        st.metric("Total Stock Value", format_currency(kpis['stock_value'], currency))
    
    with col2:
        st.metric("Profit Margin", f"{kpis['margin_percentage']:.2f}%")
        st.metric("Stock Turnover", f"{kpis['stock_turnover']:.2f}x")
    
    with col3:
        st.metric("Active Buyers", f"{kpis['buyer_count']}")
        st.metric("Total Products", f"{kpis['product_count']}")

def create_sales_trend_chart(start_date=None, end_date=None, brand=None):
    """Create a chart showing sales trends over time."""
    data = get_sales_by_month(start_date, end_date, brand)
    
    if not data:
        st.info("No sales data available for the selected period.")
        return
    
    df = pd.DataFrame(data)
    
    fig = go.Figure()
    
    # Add Revenue line
    fig.add_trace(
        go.Scatter(
            x=df['month'], 
            y=df['revenue'], 
            mode='lines+markers',
            name='Revenue',
            line=dict(color='#1E88E5', width=3),
            marker=dict(size=8)
        )
    )
    
    # Add Profit line
    fig.add_trace(
        go.Scatter(
            x=df['month'], 
            y=df['profit'], 
            mode='lines+markers',
            name='Profit',
            line=dict(color='#4CAF50', width=3),
            marker=dict(size=8)
        )
    )
    
    # Add Quantity on secondary y-axis
    fig.add_trace(
        go.Scatter(
            x=df['month'], 
            y=df['quantity'], 
            mode='lines+markers',
            name='Quantity',
            line=dict(color='#FFC107', width=3, dash='dot'),
            marker=dict(size=8),
            yaxis='y2'
        )
    )
    
    # Update layout
    fig.update_layout(
        title="Sales Trend Over Time",
        xaxis_title="Month",
        yaxis_title="Value (HUF)",
        yaxis2=dict(
            title="Quantity",
            overlaying="y",
            side="right"
        ),
        hovermode="x unified",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_sales_by_brand_chart(start_date=None, end_date=None, country=None):
    """Create a chart showing sales by brand."""
    data = get_sales_by_brand(start_date, end_date, country)
    
    if not data:
        st.info("No sales data available for the selected period.")
        return
    
    df = pd.DataFrame(data)
    
    # Limit to top 10 brands if more than 10
    if len(df) > 10:
        df = df.nlargest(10, 'revenue')
    
    fig = px.bar(
        df,
        y='brand',
        x='revenue',
        color='margin',
        color_continuous_scale='RdYlGn',
        labels={'revenue': 'Revenue (HUF)', 'brand': 'Brand', 'margin': 'Margin (%)'},
        text=df['revenue'].apply(lambda x: f'{x:,.0f}'),
        title="Sales by Brand",
        height=400
    )
    
    fig.update_layout(yaxis={'categoryorder': 'total ascending'})
    
    st.plotly_chart(fig, use_container_width=True)

def create_stock_by_warehouse_chart():
    """Create a chart showing stock by warehouse."""
    data = get_stock_by_warehouse()
    
    if not data:
        st.info("No stock data available.")
        return
    
    df = pd.DataFrame(data)
    
    fig = px.pie(
        df,
        values='value',
        names='warehouse',
        title="Stock Value by Warehouse",
        hole=0.4,
        height=400
    )
    
    fig.update_traces(textposition='inside', textinfo='percent+label')
    
    st.plotly_chart(fig, use_container_width=True)

def create_top_products_chart(metric='revenue', limit=10, start_date=None, end_date=None):
    """Create a chart showing top products."""
    data = get_top_products(metric, limit, start_date, end_date)
    
    if not data:
        st.info(f"No product data available for {metric}.")
        return
    
    df = pd.DataFrame(data)
    
    # Default values
    title = "Top Products"
    x_title = "Value"
    
    # Set chart title based on metric
    if metric == 'sales':
        title = "Top Products by Sales Quantity"
        x_title = "Quantity Sold"
    elif metric == 'revenue':
        title = "Top Products by Sales Revenue"
        x_title = "Revenue (HUF)"
    elif metric == 'profit':
        title = "Top Products by Profit"
        x_title = "Profit (HUF)"
    elif metric == 'stock_value':
        title = "Top Products by Stock Value"
        x_title = "Stock Value (HUF)"
    
    fig = px.bar(
        df,
        y='product_name',
        x='value',
        labels={'value': x_title, 'product_name': 'Product'},
        text=df['value'].apply(lambda x: f'{x:,.0f}'),
        title=title,
        height=400
    )
    
    fig.update_layout(yaxis={'categoryorder': 'total ascending'})
    
    st.plotly_chart(fig, use_container_width=True)

def create_top_buyers_chart(metric='revenue', limit=10, start_date=None, end_date=None):
    """Create a chart showing top buyers."""
    data = get_top_buyers(metric, limit, start_date, end_date)
    
    if not data:
        st.info(f"No buyer data available for {metric}.")
        return
    
    df = pd.DataFrame(data)
    
    # Default values
    title = "Top Buyers"
    x_title = "Value"
    
    # Set chart title based on metric
    if metric == 'quantity':
        title = "Top Buyers by Purchase Quantity"
        x_title = "Quantity Purchased"
    elif metric == 'revenue':
        title = "Top Buyers by Revenue"
        x_title = "Revenue (HUF)"
    
    fig = px.bar(
        df,
        y='buyer_name',
        x='value',
        labels={'value': x_title, 'buyer_name': 'Buyer'},
        text=df['value'].apply(lambda x: f'{x:,.0f}'),
        title=title,
        height=400
    )
    
    fig.update_layout(yaxis={'categoryorder': 'total ascending'})
    
    st.plotly_chart(fig, use_container_width=True)