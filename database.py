import os
import sqlalchemy as sa
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
import streamlit as st

# Import configuration
from config import config

# Create the SQLAlchemy base class
Base = declarative_base()

# Use the configuration to get DATABASE_URL
DATABASE_URL = config.DATABASE_URL

# Add SSL mode setting if not already in URL and if URL is not None
if DATABASE_URL:
    if '?' not in DATABASE_URL:
        DATABASE_URL += "?sslmode=disable"
    elif 'sslmode=' not in DATABASE_URL:
        DATABASE_URL += "&sslmode=disable"
else:
    # Fallback if DATABASE_URL is None
    DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/erpanalytics?sslmode=disable"
    st.warning("Database URL not found in configuration. Using default local database.")

# Database connection and session initialization
# Define engine and Session as global variables with initial None values
engine = None
Session = None

# Initialize with a function that will be cached by Streamlit
# Remove cache to ensure fresh connection each time
def init_db():
    """Initialize the database connection and create tables if they don't exist."""
    global engine, Session
    try:
        # Configure connection pool based on environment
        pool_size = 5
        max_overflow = 10
        
        # In production environments, use larger connection pools
        if config.IS_AWS:
            pool_size = 20
            max_overflow = 30
        
        # Create the engine with more robust error handling
        engine = sa.create_engine(
            DATABASE_URL,
            echo=False,                # Set to True for debugging
            pool_pre_ping=True,        # Test connection before using from pool
            pool_size=pool_size,       # Base pool size
            max_overflow=max_overflow, # Allow additional temporary connections
            pool_timeout=30,           # Wait up to 30 seconds for a connection
            pool_recycle=1800,         # Recycle connections after 30 minutes
            connect_args={
                'connect_timeout': 30,  # Longer timeout
                'application_name': config.APP_NAME  # Name for connection
            }
        )
        
        # Create session factory - force new connection
        session_factory = sessionmaker(bind=engine)
        Session = scoped_session(session_factory)
        
        # Import models here to avoid circular imports
        from models import Buyer, Product, ProductCategory, Price, Stock, Sale, Purchase, CustomSetting
        
        # Create tables
        Base.metadata.create_all(engine)
        
        # Verify connection
        conn = engine.connect()
        conn.close()
        
        return True
    except Exception as e:
        st.error(f"Database initialization error: {str(e)}")
        return False

def get_session():
    """Get a new database session."""
    global Session
    
    # Initialize if not already done
    if Session is None:
        success = init_db()
        if not success:
            st.error("Failed to initialize database. Check your connection settings.")
            return None
    
    # Double check that Session is not None after initialization
    if Session is None:
        st.error("Database session factory is not initialized.")
        return None
    
    try:
        session = Session()
        return session
    except Exception as e:
        st.error(f"Error creating database session: {str(e)}")
        
        # Attempt to re-initialize once in case of stale connections
        try:
            success = init_db()
            if success:
                session = Session()
                return session
        except Exception as re_init_error:
            st.error(f"Failed to re-initialize database: {str(re_init_error)}")
        
        return None

def close_session(session):
    """Close the database session."""
    if session:
        try:
            session.close()
        except Exception as e:
            st.error(f"Error closing database session: {str(e)}")

def check_db_connection():
    """
    Check if database connection is working.
    Returns a tuple of (bool, str) indicating success/failure and a message.
    """
    global engine, Session
    
    # First check if engine and Session are initialized
    if engine is None or Session is None:
        if DATABASE_URL:
            return False, "Database engine not initialized. URL is available but connection failed."
        else:
            return False, "Database URL not available. Check environment variables."
    
    # Then try to establish a connection
    try:
        # Try to connect with the engine directly first
        try:
            conn = engine.connect()
            conn.close()
        except Exception as engine_error:
            return False, f"Engine connection failed: {str(engine_error)}"
        
        # Now try with a session
        session = get_session()
        if session:
            # Try a simple query using SQLAlchemy text function
            from sqlalchemy import text
            result = session.execute(text("SELECT 1 AS connection_test")).scalar()
            close_session(session)
            
            if result == 1:
                return True, f"Connected to database in {config.ENVIRONMENT_NAME} environment"
            else:
                return False, "Database connection test failed: unexpected result"
        else:
            return False, "Failed to get database session"
    except Exception as e:
        return False, f"Database connection error: {str(e)}"
