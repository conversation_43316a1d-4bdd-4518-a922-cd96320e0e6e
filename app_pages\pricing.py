import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from database import get_session, close_session
from models import Product, Purchase, Sale, Stock, Price, CustomSetting, CompetitorScrape
from utils import (
    get_suppliers_for_pricing, get_brands_by_supplier, get_products_for_pricing,
    get_customs_shipping_rates, get_margin_default_category, get_all_margins_for_brand,
    get_discount_rates_by_brand, get_sales_history_data, get_latest_stock_data, 
    get_purchase_history_data, calculate_landing_cost, calculate_partner_pricing_cascade, 
    calculate_msrp_from_category_i, apply_currency_conversion, get_detailed_competitor_data, 
    get_suppliers, get_brands, get_product_categories, get_product_groups, get_default_shipping_method
)
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode, DataReturnMode, JsCode

def create_brand_info_table(brands, brand_currency_cache, brand_exchange_rate_cache):
    """Create a table showing brand-specific pricing settings."""
    from utils import get_exchange_rate_from_settings
    
    brand_data = []
    
    for brand in brands:
        # Get currency info from cache
        currency_info = brand_currency_cache.get(brand, {})
        purchase_currency = currency_info.get('purchase_currency', 'USD')
        sales_currency = currency_info.get('sales_currency', 'EUR')
        
        # Get exchange rates from settings instead of using hardcoded values
        try:
            if purchase_currency == 'HUF':
                purchase_rate = 1.0
            else:
                purchase_rate = get_exchange_rate_from_settings(purchase_currency)
        except Exception:
            purchase_rate = 1.0
            
        try:
            if sales_currency == 'HUF':
                sales_rate = 1.0
            else:
                sales_rate = get_exchange_rate_from_settings(sales_currency)
        except Exception:
            sales_rate = 1.0
        
        # Get default shipping method
        default_shipping = get_default_shipping_method(brand)
        
        # Get customs and shipping rates for the shipping method
        customs_rate, shipping_rate = get_customs_shipping_rates(brand, default_shipping)
        
        # Get margin data
        margin_data = get_all_margins_for_brand(brand)
        
        # Get discount data  
        discount_data = get_discount_rates_by_brand(brand)
        
        # Build row data with comprehensive information
        row_data = {
            'Brand': brand,
            'Shipping Method': default_shipping,
            'Shipping': f"{shipping_rate * 100:.1f}%",
            'Customs': f"{customs_rate * 100:.1f}%",
            'Purchase Currency': purchase_currency,
            'Purchase Rate': f"{purchase_rate:.1f}",
            'Sales Currency': sales_currency,
            'Sales Rate': f"{sales_rate:.1f}"
        }
        
        # Add margin categories
        margin_categories = ['A', 'B', 'C', 'D', 'EOL']
        for cat in margin_categories:
            margin_value = None
            for margin in margin_data:
                if margin['category'] == cat:
                    margin_value = margin['percentage']
                    break
            row_data[f'Margin Cat {cat}'] = f"{margin_value:.1f}%" if margin_value is not None else "N/A"
        
        # Add discount categories
        discount_categories = ['I', 'II', 'III', 'IV']
        for cat in discount_categories:
            discount_value = discount_data.get(cat, 0)
            row_data[f'Discount Cat {cat}'] = f"{discount_value:.1f}%"
        
        brand_data.append(row_data)
    
    return pd.DataFrame(brand_data)

def show():
    """Display the Pricing page with three tabs."""
    st.header("Pricing Management")
    
    # Create tabs
    tab1, tab2, tab3 = st.tabs(["Live Pricelists", "Create Pricelist", "Competitor Scrape"])
    
    with tab1:
        show_live_pricelists()
    
    with tab2:
        show_create_pricelist()
    
    with tab3:
        show_competitor_scrape()

def show_live_pricelists():
    """Display saved pricing models."""
    st.subheader("Live Pricelists")
    
    # Check if there are any published pricelists
    if 'published_pricelists' not in st.session_state or not st.session_state.published_pricelists:
        st.info("No pricelists have been published yet. Create a pricelist in the 'Create Pricelist' tab and publish it to see it here.")
        return
    
    # Display existing pricelists
    pricelists = st.session_state.published_pricelists
    
    st.write(f"**{len(pricelists)} Published Pricelists:**")
    
    for pricelist_name, pricelist_data in pricelists.items():
        with st.expander(f"📋 {pricelist_name}", expanded=False):
            # Pricelist information
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"**Created:** {pricelist_data['created_at'].strftime('%Y-%m-%d %H:%M')}")
                st.write(f"**Products:** {len(pricelist_data['data'])} items")
            
            with col2:
                st.write(f"**Currency:** {pricelist_data.get('currency', 'HUF')}")
                st.write(f"**Method:** {pricelist_data.get('method', 'N/A')}")
            
            with col3:
                suppliers = pricelist_data.get('suppliers', [])
                brands = pricelist_data.get('brands', [])
                st.write(f"**Suppliers:** {', '.join(suppliers) if suppliers else 'All'}")
                st.write(f"**Brands:** {', '.join(brands) if brands else 'All'}")
            
            # Action buttons
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                if st.button("👁️ View", key=f"view_{pricelist_name}"):
                    st.write("**Pricelist Data Preview:**")
                    # Show a preview of the data
                    preview_df = pricelist_data['data'].head(10)
                    st.dataframe(preview_df, use_container_width=True)
            
            with col2:
                if st.button("📝 Load for Editing", key=f"load_{pricelist_name}"):
                    # Load this pricelist data into the create tab
                    st.session_state.loaded_pricelist_data = pricelist_data['data'].copy()
                    st.session_state.loaded_suppliers = pricelist_data.get('suppliers', [])
                    st.session_state.loaded_brands = pricelist_data.get('brands', [])
                    st.session_state.loaded_currency = pricelist_data.get('currency', 'HUF')
                    st.session_state.loaded_method = pricelist_data.get('method', 'Lakossági Preset')
                    st.success(f"Loaded '{pricelist_name}' for editing. Switch to 'Create Pricelist' tab to continue.")
            
            with col3:
                # Export to Excel
                if st.button("📊 Export Excel", key=f"export_{pricelist_name}"):
                    try:
                        import io
                        from io import BytesIO
                        
                        # Create Excel file in memory
                        output = BytesIO()
                        with pd.ExcelWriter(output, engine='openpyxl') as writer:
                            pricelist_data['data'].to_excel(writer, sheet_name='Pricelist', index=False)
                        
                        # Prepare download
                        output.seek(0)
                        st.download_button(
                            label="💾 Download",
                            data=output.getvalue(),
                            file_name=f"{pricelist_name}.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key=f"download_{pricelist_name}"
                        )
                    except Exception as e:
                        st.error(f"Export failed: {str(e)}")
            
            with col4:
                if st.button("🗑️ Delete", key=f"delete_{pricelist_name}"):
                    if st.session_state.get(f"confirm_delete_{pricelist_name}", False):
                        # Actually delete
                        del st.session_state.published_pricelists[pricelist_name]
                        st.success(f"Deleted '{pricelist_name}'")
                        st.rerun()
                    else:
                        # Ask for confirmation
                        st.session_state[f"confirm_delete_{pricelist_name}"] = True
                        st.warning("Click delete again to confirm")
                        st.rerun()

def show_create_pricelist():
    """Main pricing calculator functionality."""
    
    # Configuration Section
    st.write("### Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Supplier Selection
        suppliers = get_suppliers_for_pricing()
        if not suppliers:
            st.warning("No suppliers found in the database. Please import purchase data first.")
            return
            
        selected_suppliers = st.multiselect(
            "Select Suppliers",
            options=suppliers,
            help="Choose one or more suppliers to filter products"
        )
        

        
        # Sales History Period
        sales_months = st.number_input(
            "Sales History (months)",
            min_value=1,
            max_value=36,
            value=12,
            help="Number of months of prior sales to display"
        )
    
    with col2:
        # Brand Selection (filtered by suppliers)
        if selected_suppliers:
            brands = get_brands_by_supplier(selected_suppliers)
            selected_brands = st.multiselect(
                "Select Brands",
                options=brands,
                help="Choose brands from selected suppliers"
            )
        else:
            selected_brands = []
            st.info("Select suppliers first to see available brands")
        
        # Purchase currencies are now managed per-brand in logistics settings
    
    # Load Data Section
    if selected_suppliers and selected_brands:
        st.write("### Data Loading")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("Load Prices", type="primary"):
                # Show loading indicator
                with st.spinner("Loading pricing data... This may take a few moments."):
                    # Pre-load and validate data - currency now determined per-brand
                    products_data = get_products_for_pricing(selected_suppliers, selected_brands)
                    
                    if not products_data:
                        st.error("No products found for selected criteria.")
                        return
                    
                    # Load brand currency and exchange rate data
                    with st.spinner("Loading brand settings and exchange rates..."):
                        from utils import get_all_logistics_settings_for_brands, get_exchange_rate_from_settings
                        
                        # Get logistics settings (includes purchase_currency and sales_currency)
                        logistics_data = get_all_logistics_settings_for_brands()
                        
                        # Build brand currency cache
                        brand_currency_cache = {}
                        brand_exchange_rate_cache = {}
                        
                        for brand in selected_brands:
                            if brand in logistics_data:
                                purchase_currency = logistics_data[brand].get('purchase_currency', 'EUR')
                                sales_currency = logistics_data[brand].get('sales_currency', 'HUF')
                            else:
                                # Default fallback values
                                purchase_currency = 'EUR'
                                sales_currency = 'HUF'
                            
                            brand_currency_cache[brand] = {
                                'purchase_currency': purchase_currency,
                                'sales_currency': sales_currency
                            }
                            
                            # Get exchange rate for purchase currency
                            if purchase_currency == 'HUF':
                                exchange_rate = 1.0
                            else:
                                try:
                                    exchange_rate = get_exchange_rate_from_settings(purchase_currency)
                                except Exception as e:
                                    st.warning(f"Could not get {purchase_currency} exchange rate for brand {brand}: {str(e)}")
                                    exchange_rate = 1.0  # Fallback
                            
                            brand_exchange_rate_cache[brand] = exchange_rate
                    
                    # Set session state to indicate prices are loaded
                    st.session_state.prices_loaded = True
                    st.session_state.loaded_suppliers = selected_suppliers
                    st.session_state.loaded_brands = selected_brands
                    st.session_state.loaded_months = sales_months
                    st.session_state.pricing_data = products_data  # Cache the data
                    st.session_state.brand_currency_cache = brand_currency_cache
                    st.session_state.brand_exchange_rate_cache = brand_exchange_rate_cache
                    st.session_state.logistics_data = logistics_data
                    st.session_state.cache_version = "v10_no_fallback"  # Force recalculation with no fallback logic
                    
                st.success(f"Loaded {len(products_data)} products successfully!")
                st.rerun()
        
        with col2:
            pass
        
        # Only show pricing table if prices have been loaded and configuration matches
        if (hasattr(st.session_state, 'prices_loaded') and st.session_state.prices_loaded and
            hasattr(st.session_state, 'loaded_suppliers') and 
            st.session_state.loaded_suppliers == selected_suppliers and
            st.session_state.loaded_brands == selected_brands and
            hasattr(st.session_state, 'cache_version') and st.session_state.cache_version == "v10_no_fallback"):
            
            # Generate Pricing Table using cached data with per-brand currency
            show_pricing_table_with_data(st.session_state.pricing_data, 
                                       st.session_state.brand_currency_cache, 
                                       st.session_state.brand_exchange_rate_cache, 
                                       sales_months)
    else:
        st.info("Please select suppliers and brands to generate pricing table.")

def format_price_value(x):
    """Format price value with thousand separators."""
    import pandas as pd
    if pd.notna(x) and x != 0:
        return f"{int(x):,}".replace(',', ' ')
    return ""

def format_percentage_change(x):
    """Format percentage change with % sign."""
    import pandas as pd
    if pd.notna(x) and x != 0:
        # If it's already a string with %, return as-is
        if isinstance(x, str) and x.endswith('%'):
            return x
        # Otherwise format as percentage
        return f"{x:.1f}%"
    return ""

def format_ratio_value(x):
    """Format ratio value as decimal or return as-is if already formatted."""
    import pandas as pd
    if pd.notna(x) and x != 0:
        # If it's already a string (like "98.0%"), return as-is
        if isinstance(x, str):
            return x
        # Otherwise format as decimal
        return f"{x:.2f}"
    return ""

def format_percentage_value(x):
    """Format percentage value with center alignment."""
    import pandas as pd
    if pd.isna(x) or x == 0:
        return ""
    try:
        return f"{float(x):.0f}%"
    except:
        return str(x)

def format_number_value(x):
    """Format number value with center alignment."""
    import pandas as pd
    if pd.isna(x) or x == 0:
        return ""
    try:
        return f"{int(x)}"
    except:
        return str(x)

def format_stock_value(x):
    """Format stock value with center alignment."""
    import pandas as pd
    if pd.isna(x) or x == "":
        return ""
    return str(x)

def format_date_value(x):
    """Format date value."""
    import pandas as pd
    if pd.isna(x) or x is None:
        return ""
    try:
        if isinstance(x, str):
            from datetime import datetime
            date_obj = datetime.fromisoformat(x)
            return date_obj.strftime("%Y-%m-%d")
        else:
            return x.strftime("%Y-%m-%d")
    except:
        return str(x)

def show_pricing_table_with_data(products_data, brand_currency_cache, brand_exchange_rate_cache, sales_months):
    """Generate and display the pricing table using cached data with per-brand currency support."""
    st.write("### Pricing Table (Lakossági Preset)")
    
    if not products_data:
        st.warning("No products found for selected criteria.")
        return
    
    # Get unique brands from products data
    unique_brands = list(set([p.get('brand', '') for p in products_data if p.get('brand')]))
    
    # Initialize local data management
    import json
    import pandas as pd
    

    
    if 'row_margin_overrides' not in st.session_state:
        st.session_state.row_margin_overrides = {}
    
    # Initialize margin cache for performance
    if 'margin_cache' not in st.session_state:
        st.session_state.margin_cache = {}
    
    # Initialize shipping cache for performance
    if 'shipping_cache' not in st.session_state:
        st.session_state.shipping_cache = {}
    
    # Initialize shipping method overrides
    if 'row_shipping_overrides' not in st.session_state:
        st.session_state.row_shipping_overrides = {}
    
    # Pre-cache margin and shipping data for all brands to avoid repeated database calls
    for brand in unique_brands:
        if brand not in st.session_state.margin_cache:
            st.session_state.margin_cache[brand] = {
                'margins': get_all_margins_for_brand(brand),
                'default': get_margin_default_category(brand)
            }
        if brand not in st.session_state.shipping_cache:
            st.session_state.shipping_cache[brand] = get_default_shipping_method(brand)
    
    # Clear cached pricing data to ensure fresh calculations with no fallback logic
    cache_key = f"pricing_df_v10_no_fallback_{len(products_data)}_{len(brand_exchange_rate_cache)}_{sales_months}"
    if cache_key in st.session_state:
        del st.session_state[cache_key]
    
    # Create the pricing dataframe with per-brand currency support
    pricing_df = create_pricing_dataframe(products_data, brand_currency_cache, brand_exchange_rate_cache, sales_months)
    

    
    if pricing_df is not None and not pricing_df.empty:
        # Prepare editable dataframe
        display_df = pricing_df.copy()
        
        # Add margin category and shipping columns using cached data
        margin_cats = []
        margin_percentages = []
        shipping_methods = []
        
        for idx, row in display_df.iterrows():
            product_id = row['Termékkód']
            brand = row['Gyártó']
            row_key = f"{product_id}_{brand}"
            
            # Get cached margin data for this brand
            brand_cache = st.session_state.margin_cache.get(brand, {})
            available_margins = brand_cache.get('margins', [])
            default_margin = brand_cache.get('default', 20.0)
            
            # Check for user override
            current_selection = st.session_state.row_margin_overrides.get(row_key, default_margin)
            
            # Find the margin category letter and percentage using cached data
            margin_cat = "C"  # Default fallback
            margin_percent = 20.0  # Default fallback
            
            if available_margins:
                for margin in available_margins:
                    if abs(margin['value'] - current_selection) < 0.001:
                        margin_cat = margin['category']
                        margin_percent = margin['percentage']
                        break
            
            margin_cats.append(margin_cat)
            margin_percentages.append(margin_percent)
            
            # Get shipping method (default from brand settings or user override)
            default_shipping = st.session_state.shipping_cache.get(brand, "EXW")
            current_shipping = st.session_state.row_shipping_overrides.get(row_key, default_shipping)
            shipping_methods.append(current_shipping)
        
        # Margin columns are now added during dataframe creation, no need to insert here
        
        # Debug: Check what's actually in the dataframe before display
        if not display_df.empty:
            print(f"DEBUG: Dataframe columns: {display_df.columns.tolist()}")
            if 'Customs Rate' in display_df.columns:
                print(f"DEBUG: First few Customs Rate values: {display_df['Customs Rate'].head(3).tolist()}")
            if 'Shipping Rate' in display_df.columns:
                print(f"DEBUG: First few Shipping Rate values: {display_df['Shipping Rate'].head(3).tolist()}")
        
        # Store original dataframe for comparison
        if 'original_display_df' not in st.session_state or st.session_state.original_display_df is None:
            st.session_state.original_display_df = display_df.copy()
        
        # Format price columns with thousand separators and no decimals
        price_columns = [
            'Beszerzési ár', 'Net Man MSRP', 'Vámköltség', 'Szállítási költség', 'Landing cost', 'Landing DEV', 
            'Landing HUF net', 'Landing HUF br.', 'Export Price', 'Category IV', 'Category III', 
            'Category II', 'Category I', 'MSRP HUF net', 'MSRP HUF bruttó', 'Net HUF NEW MSRP', 
            'Br NEW HUF MSRP', 'Br HUF Man MSRP', 'Jelenlegi MSRP (net)', 'Jelenlegi MSRP (br.)',
            'Thomann', 'Muziker', 'R55', 'Kytary', 'Mezzo', 'Allegro', 'Pako', 'Mango', 
            'Pláza', 'Diszkont', 'Hitspace', 'Average Price (Main)', 'Average Price (All)',
            'Készletérték', 'Értékesítés érték'
        ]
        
        # Format percentage columns (center aligned, no decimals)
        percentage_columns = ['Suggested Margin', 'Margó (%)', 'Export Margin', 'Customs Rate', 
                            'Shipping Rate', 'MSRP változás (%)', 'Partner / Thomann', 'MSRP / Thomann', 
                            'Partner / Muziker', 'MSRP / Muziker']
        
        # Format number columns (center aligned, middle alignment)
        number_columns = ['Sales Count', 'Purchase Count']
        
        # Format stock columns (center aligned)
        stock_columns = ['R55 Stock', 'Kytary Stock', 'Mezzo Stock', 'Allegro Stock', 'Pako Stock', 
                        'Mango Stock', 'Pláza Stock', 'Diszkont Stock', 'Hitspace Stock']
        
        # Format date columns
        date_columns = ['Last Sale Date', 'Last Purchase Date']
        
        # Format price columns
        for col in price_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(format_price_value)
        
        # Format percentage columns
        for col in percentage_columns:
            if col in display_df.columns:
                if 'változás' not in col and '/' not in col:
                    # Format as percentage with % sign
                    display_df[col] = display_df[col].apply(format_percentage_value)
                else:
                    # Ratios as decimal numbers or percentages (but not MSRP change)
                    display_df[col] = display_df[col].apply(format_ratio_value)
        
        # Format number columns
        for col in number_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(format_number_value)
        
        # Format stock columns
        for col in stock_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(format_stock_value)
        
        # Format date columns
        for col in date_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].apply(format_date_value)
        
        # Configure AgGrid for Excel-like editing
        gb = GridOptionsBuilder.from_dataframe(display_df)
        
        # Configure margin category column with dropdown
        gb.configure_column(
            "Margin Cat",
            editable=True,
            cellEditor="agSelectCellEditor",
            cellEditorParams={"values": ["A", "B", "C", "D", "EOL"]},
            width=100,
            cellStyle={"textAlign": "center"}
        )
        
        # Configure shipping method column with dropdown
        gb.configure_column(
            "Shipping",
            editable=True,
            cellEditor="agSelectCellEditor",
            cellEditorParams={"values": ["EXW", "FOB"]},
            width=100,
            cellStyle={"textAlign": "center"}
        )
        
        # Configure margin percentage column (read-only, calculated)
        gb.configure_column(
            "Margin %",
            editable=False,
            type="numericColumn",
            precision=1,
            width=100,
            cellStyle={"textAlign": "center"},
            valueFormatter="value + '%'"
        )
        
        # Configure percentage columns with center alignment
        for col in percentage_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    cellStyle={"textAlign": "center"},
                    width=120
                )
        
        # Configure ratio/comparison columns (percentage, no decimals, center aligned)
        ratio_columns = ['MSRP / Thomann', 'MSRP / Muziker', 'Partner / Thomann', 'Partner / Muziker']
        for col in ratio_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    cellStyle={"textAlign": "center"},
                    valueFormatter="Math.round(value) + '%'",
                    width=120
                )
        
        # Configure number columns with center alignment
        for col in number_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    cellStyle={"textAlign": "center"},
                    width=100
                )
        
        # Configure stock columns with center alignment
        for col in stock_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    cellStyle={"textAlign": "center"},
                    width=100
                )
        
        # Configure date columns
        for col in date_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    cellStyle={"textAlign": "center"},
                    width=120
                )
        
        # Apply currency formatting and preserve sorting
        if 'Beszerzési ár' in display_df.columns:
            # Create numeric column for sorting
            numeric_values = []
            for idx, row in display_df.iterrows():
                brand = row.get('Gyártó', '')
                currency = brand_currency_cache.get(brand, {}).get('purchase_currency', 'EUR')
                if pd.notna(row['Beszerzési ár']) and row['Beszerzési ár'] != '':
                    try:
                        # Handle both comma and space thousand separators
                        value_str = str(row['Beszerzési ár']).replace(',', '').replace(' ', '')
                        value = float(value_str)
                        numeric_values.append(value)
                        # Format with thousand separators and currency
                        formatted_value = f"{int(value):,}".replace(',', ' ')
                        display_df.at[idx, 'Beszerzési ár'] = f"{formatted_value} {currency}"
                    except (ValueError, TypeError):
                        numeric_values.append(0)
                else:
                    numeric_values.append(0)
            
            # Store numeric values for potential sorting
            display_df['_beszerzesi_ar_sort'] = numeric_values
            
            # Sort DataFrame by numeric values (descending order)
            display_df = display_df.sort_values('_beszerzesi_ar_sort', ascending=False)
        
        # Apply currency formatting to Net Man MSRP with actual MSRP currency
        if 'Net Man MSRP' in display_df.columns:
            # Create numeric column for sorting
            numeric_values = []
            for idx, row in display_df.iterrows():
                # Use the actual MSRP currency instead of brand's preferred sales currency
                currency = row.get('_msrp_currency', 'EUR')
                if pd.notna(row['Net Man MSRP']) and row['Net Man MSRP'] != '':
                    try:
                        # Handle both comma and space thousand separators
                        value_str = str(row['Net Man MSRP']).replace(',', '').replace(' ', '')
                        value = float(value_str)
                        numeric_values.append(value)
                        # Format with thousand separators and currency
                        formatted_value = f"{int(value):,}".replace(',', ' ')
                        display_df.at[idx, 'Net Man MSRP'] = f"{formatted_value} {currency}"
                    except (ValueError, TypeError):
                        numeric_values.append(0)
                else:
                    numeric_values.append(0)
            
            # Store numeric values for potential sorting
            display_df['_net_man_msrp_sort'] = numeric_values
        
        # Configure Beszerzési ár column with simple configuration
        if 'Beszerzési ár' in display_df.columns:
            gb.configure_column(
                'Beszerzési ár',
                editable=False,
                width=120,
                sortable=True
            )
        
        # Configure Net Man MSRP column with simple configuration  
        if 'Net Man MSRP' in display_df.columns:
            gb.configure_column(
                'Net Man MSRP',
                editable=False,
                width=120,
                sortable=True
            )
        
        # Configure other currency columns with simple text formatting
        other_currency_columns = ['Landing DEV', 'Export Price']
        for col in other_currency_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    width=120
                )
        
        # Hide the sorting helper columns
        if '_beszerzesi_ar_sort' in display_df.columns:
            gb.configure_column(
                '_beszerzesi_ar_sort',
                hide=True
            )
        if '_net_man_msrp_sort' in display_df.columns:
            gb.configure_column(
                '_net_man_msrp_sort',
                hide=True
            )
        if '_msrp_currency' in display_df.columns:
            gb.configure_column(
                '_msrp_currency',
                hide=True
            )
        
        # Define column categories for later use
        purchase_currency_columns = ['Beszerzési ár', 'Net Man MSRP', 'Landing DEV', 'Export Price']
        
        # Configure HUF currency columns (no currency display)
        huf_currency_columns = ['Br HUF Man MSRP', 'Br HUF NEW MSRP']
        for col in huf_currency_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    type="numericColumn",
                    precision=0,
                    width=120,
                    cellStyle={"textAlign": "center"}
                )
        
        # Configure HUF columns with thousand separators
        huf_columns = ['Landing HUF br.', 'Br HUF NEW MSRP']
        for col in huf_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    type="numericColumn",
                    precision=0,
                    width=120,
                    valueFormatter="value.toLocaleString('hu-HU')"
                )
        
        # Configure percentage change columns only
        change_percentage_columns = ['MSRP változás (%)']
        for col in change_percentage_columns:
            if col in display_df.columns:
                gb.configure_column(
                    col,
                    editable=False,
                    width=120,
                    cellStyle={"textAlign": "center"},
                    valueFormatter="value + '%'"
                )
        
        # Configure other price columns
        other_price_columns = [
            'Vámköltség', 'Szállítási költség', 'Landing cost', 
            'Landing HUF net', 'Category I', 'Category II', 
            'Category III', 'Category IV', 'Net HUF NEW MSRP', 'Jelenlegi MSRP (net)', 'Jelenlegi MSRP (br.)',
            'Készlet érték', 'Eladás 3 hó', 'Eladás 6 hó', 'Eladás 12 hó'
        ]
        
        for col in display_df.columns:
            if col in other_price_columns:
                gb.configure_column(
                    col,
                    editable=False,
                    type="numericColumn",
                    precision=0,
                    width=120
                )
            elif col not in ["Margin Cat", "Margin %"] + purchase_currency_columns + huf_currency_columns + percentage_columns + ratio_columns + change_percentage_columns + number_columns + stock_columns + date_columns:
                gb.configure_column(col, editable=False, width=100)
        
        # Grid options for Excel-like behavior
        gb.configure_default_column(resizable=True, sortable=True, filterable=True)
        gb.configure_selection(selection_mode="multiple", use_checkbox=False)
        gb.configure_grid_options(
            enableRangeSelection=True,
            enableFillHandle=True,
            suppressRowClickSelection=True,
            rowSelection="multiple"
        )
        
        grid_options = gb.build()
        
        # Inject brand currency cache into browser's global scope for dynamic formatting
        brand_currency_script = f"""
        <script>
        window.brandCurrencyCache = {json.dumps(brand_currency_cache)};
        console.log('Brand currency cache loaded:', window.brandCurrencyCache);
        </script>
        """
        st.components.v1.html(brand_currency_script, height=0)
        
        # Get all unique brands from the pricing data
        all_brands = list(set(row['Gyártó'] for row in products_data if row.get('Gyártó')))
        
        # Prepare brand-specific data for JavaScript
        brand_margin_data = {}
        brand_discount_data = {}
        
        for brand in all_brands:
            # Get margin data for this brand
            margin_data = get_all_margins_for_brand(brand)
            brand_margins = {}
            brand_default_margin = None
            
            for margin in margin_data:
                brand_margins[margin['category']] = margin['percentage']
                if margin.get('is_default', False):
                    brand_default_margin = margin['percentage']
            
            # If no default found, use the first margin or fallback
            if brand_default_margin is None and brand_margins:
                brand_default_margin = list(brand_margins.values())[0]
            elif brand_default_margin is None:
                brand_default_margin = 20.0  # Final fallback
            
            brand_margin_data[brand] = {
                'margins': brand_margins,
                'default': brand_default_margin
            }
            
            # Get discount data for this brand
            discount_data = get_discount_rates_by_brand(brand)
            brand_discount_data[brand] = discount_data
        
        # Convert dictionaries to JSON strings for JavaScript
        import json
        brand_margin_json = json.dumps(brand_margin_data)
        brand_discount_json = json.dumps(brand_discount_data)
        
        # Build customs and shipping cache for JavaScript
        customs_shipping_cache = {}
        for brand in all_brands:
            customs_shipping_cache[brand] = {
                "EXW": get_customs_shipping_rates(brand, "EXW"),
                "FOB": get_customs_shipping_rates(brand, "FOB")
            }
        
        # Prepare shipping cache data for JavaScript
        brand_shipping_data = {}
        for brand in all_brands:
            brand_shipping_data[brand] = {
                'default': st.session_state.shipping_cache.get(brand, "EXW"),
                'exw_customs': customs_shipping_cache[brand]["EXW"][0],
                'exw_shipping': customs_shipping_cache[brand]["EXW"][1],
                'fob_customs': customs_shipping_cache[brand]["FOB"][0],
                'fob_shipping': customs_shipping_cache[brand]["FOB"][1]
            }
        
        # Convert to JSON for JavaScript
        brand_shipping_json = json.dumps(brand_shipping_data)
        
        # Add brand currency cache to JavaScript global scope
        brand_currency_json = json.dumps(brand_currency_cache)
        
        # Create JavaScript code with proper string interpolation
        js_code = """
        function(params) {
            const field = params.colDef.field;
            
            if (field === 'Margin Cat' || field === 'Shipping') {
                const rowData = params.data;
                const brand = rowData['Gyártó'];
                
                // Brand-specific margin, discount, shipping, and currency data
                const brandMarginData = """ + brand_margin_json + """;
                const brandDiscountData = """ + brand_discount_json + """;
                const brandShippingData = """ + brand_shipping_json + """;
                const brandCurrencyData = """ + brand_currency_json + """;
                
                // Make currency data globally available for currency formatters
                if (typeof window !== 'undefined') {
                    window.brandCurrencyCache = brandCurrencyData;
                }
                
                // Handle margin category changes
                if (field === 'Margin Cat') {
                    const marginCat = params.newValue;
                    
                    // Get margin percentage for this brand and category
                    let marginPercent = null;
                    if (brandMarginData[brand] && brandMarginData[brand].margins[marginCat]) {
                        marginPercent = brandMarginData[brand].margins[marginCat];
                    } else if (brandMarginData[brand]) {
                        marginPercent = brandMarginData[brand].default;
                    }
                    
                    if (marginPercent === null) {
                        console.error('No margin percentage found for brand:', brand, 'category:', marginCat);
                        return false;
                    }
                    
                    // Update margin percentage
                    rowData['Margin %'] = marginPercent;
                }
                
                // Get current values for calculation
                const currentShipping = field === 'Shipping' ? params.newValue : rowData['Shipping'];
                const currentMarginPercent = field === 'Margin Cat' ? 
                    (brandMarginData[brand] && brandMarginData[brand].margins[params.newValue] ? 
                     brandMarginData[brand].margins[params.newValue] : brandMarginData[brand].default) :
                    parseFloat(rowData['Margin %']) || 0;
                
                // Get shipping rates for current method
                const shippingData = brandShippingData[brand];
                if (!shippingData) {
                    console.error('No shipping data found for brand:', brand);
                    return false;
                }
                
                let customsRate = 0;
                let shippingRate = 0;
                
                if (currentShipping === 'FOB') {
                    customsRate = shippingData.fob_customs;
                    shippingRate = shippingData.fob_shipping;
                } else {
                    customsRate = shippingData.exw_customs;
                    shippingRate = shippingData.exw_shipping;
                }
                
                // Recalculate landing costs with new shipping method
                const purchasePrice = parseFloat(rowData['Beszerzési ár']) || 0;
                
                // Get brand-specific exchange rate
                const brandExchangeRates = """ + json.dumps(brand_exchange_rate_cache) + """;
                const exchangeRate = brandExchangeRates[brand] || 1.0;
                
                // Calculate new landing cost
                const landingCostDEV = purchasePrice * (1 + customsRate + shippingRate);
                const landingHUFNet = landingCostDEV * exchangeRate;
                const landingHUFBr = landingHUFNet * 1.27;
                
                // Update customs and shipping rate display columns
                rowData['Customs Rate'] = (customsRate * 100).toFixed(0) + '%';
                rowData['Shipping Rate'] = (shippingRate * 100).toFixed(0) + '%';
                rowData['Shipping Method'] = shippingMethod;
                
                // Update landing cost columns
                rowData['Landing DEV'] = parseFloat(landingCostDEV.toFixed(2));
                rowData['Landing HUF net'] = parseFloat(landingHUFNet.toFixed(0));
                rowData['Landing HUF br.'] = parseFloat(landingHUFBr.toFixed(0));
                
                // Get discount rates for this brand
                const discountData = brandDiscountData[brand] || {};
                const discountI = discountData.discount_i || 0;
                const discountII = discountData.discount_ii || 0;
                const discountIII = discountData.discount_iii || 0;
                const discountIV = discountData.discount_iv || 0;
                
                // Calculate partner pricing cascade
                const categoryI = landingHUFBr * (1 + currentMarginPercent / 100);
                const categoryII = categoryI * (1 - discountI / 100);
                const categoryIII = categoryII * (1 - discountII / 100);
                const categoryIV = categoryIII * (1 - discountIII / 100);
                const partnerNet = categoryIV * (1 - discountIV / 100);
                
                // Update partner pricing columns
                rowData['I. kat. HUF br.'] = Math.round(categoryI);
                rowData['II. kat. HUF br.'] = Math.round(categoryII);
                rowData['III. kat. HUF br.'] = Math.round(categoryIII);
                rowData['IV. kat. HUF br.'] = Math.round(categoryIV);
                rowData['Partner nettó'] = Math.round(partnerNet);
                
                // Calculate MSRP
                const msrpGross = categoryI * 1.6;
                const msrpNet = msrpGross / 1.27;
                
                rowData['MSRP bruttó'] = Math.round(msrpGross);
                rowData['MSRP nettó'] = Math.round(msrpNet);
                
                // Calculate MSRP change percentage
                const currentMSRP = parseFloat(rowData['Jelenlegi MSRP']) || 0;
                if (currentMSRP > 0) {
                    const changePercent = ((msrpNet - currentMSRP) / currentMSRP * 100);
                    rowData['MSRP változás (%)'] = parseFloat(changePercent.toFixed(1));
                }
                
                // Calculate competitor ratios
                const thomannPrice = parseFloat(rowData['Thomann']) || 0;
                const muzikerPrice = parseFloat(rowData['Muziker']) || 0;
                
                if (thomannPrice > 0) {
                    const partnerThommann = (categoryIV / thomannPrice).toFixed(2);
                    const msrpThommannRatio = (msrpGross / thomannPrice);
                    rowData['Partner / Thomann'] = partnerThommann;
                    rowData['MSRP / Thomann'] = msrpThommannRatio ? (msrpThommannRatio * 100).toFixed(1) + '%' : '0%';
                }
                
                if (muzikerPrice > 0) {
                    const partnerMuziker = (categoryIV / muzikerPrice).toFixed(2);
                    const msrpMuzikerRatio = (msrpGross / muzikerPrice);
                    rowData['Partner / Muziker'] = partnerMuziker;
                    rowData['MSRP / Muziker'] = msrpMuzikerRatio ? (msrpMuzikerRatio * 100).toFixed(1) + '%' : '0%';
                }
                
                // Refresh all cells in the row
                params.api.refreshCells({
                    rowNodes: [params.node],
                    force: true
                });
            }
        }
        """
        
        margin_update_js = JsCode(js_code)
        
        # Configure cell value changed callback
        grid_options["onCellValueChanged"] = margin_update_js
        
        # Display AgGrid
        grid_response = AgGrid(
            display_df,
            gridOptions=grid_options,
            update_mode=GridUpdateMode.VALUE_CHANGED,
            data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
            height=600,
            width='100%',
            allow_unsafe_jscode=True,
            key="pricing_aggrid"
        )
        
        # Brand information table
        st.write("---")
        st.subheader("Brand Pricing Settings")
        
        if (hasattr(st.session_state, 'loaded_brands') and st.session_state.loaded_brands):
            brand_info_df = create_brand_info_table(st.session_state.loaded_brands, brand_currency_cache, brand_exchange_rate_cache)
            if not brand_info_df.empty:
                st.dataframe(
                    brand_info_df,
                    use_container_width=True,
                    height=300,
                    hide_index=True
                )
            else:
                st.info("No brand settings found for selected brands")
        else:
            st.info("Load pricing data to see brand settings")
        
        # Simple publish functionality
        st.write("---")
        
        col1, col2 = st.columns([3, 1])
        
        with col1:
            pricelist_name = st.text_input(
                "Pricelist Name",
                value=f"Pricelist_{datetime.now().strftime('%Y%m%d_%H%M')}",
                help="Enter a name for this pricelist"
            )
        
        with col2:
            st.write("")  # Spacing
            if st.button("📋 Publish Pricelist", type="primary", help="Save current pricing as live pricelist"):
                if pricelist_name:
                    # Get current grid data
                    current_data = grid_response['data']
                    if current_data is not None and not current_data.empty:
                        # Store pricelist in session state (could be extended to database)
                        if 'published_pricelists' not in st.session_state:
                            st.session_state.published_pricelists = {}
                        
                        st.session_state.published_pricelists[pricelist_name] = {
                            'data': current_data.copy(),
                            'created_at': datetime.now(),
                            'suppliers': st.session_state.get('loaded_suppliers', []),
                            'brands': st.session_state.get('loaded_brands', []),
                            'currency': st.session_state.get('loaded_currency', 'HUF'),
                            'method': st.session_state.get('loaded_method', 'Lakossági Preset')
                        }
                        
                        st.success(f"Pricelist '{pricelist_name}' published successfully!")
                    else:
                        st.error("No data to publish")
                else:
                    st.error("Please enter a pricelist name")
        
        # Export options
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📊 Export to Excel", key="export_excel"):
                # Convert dataframe to Excel
                from io import BytesIO
                import pandas as pd
                
                output = BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    pricing_df.to_excel(writer, sheet_name='Pricing', index=False)
                
                output.seek(0)
                st.download_button(
                    label="Download Excel File",
                    data=output.getvalue(),
                    file_name=f"pricing_table_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
        
        with col2:
            st.info(f"Showing {len(pricing_df)} products")
    
    else:
        st.error("Unable to generate pricing table. Please check your data and try again.")

def show_pricing_table(suppliers, brands, pricing_method, currency, exchange_rate, sales_months):
    """Generate and display the pricing table."""
    st.write("### Pricing Table (Lakossági Preset)")
    
    # Get products data
    products_data = get_products_for_pricing(suppliers, brands, currency)
    
    if not products_data:
        st.warning("No products found for selected criteria.")
        return
    
    # Create the pricing dataframe
    pricing_df = create_pricing_dataframe(products_data, pricing_method, exchange_rate, sales_months)
    
    if pricing_df is not None and not pricing_df.empty:
        # Format price columns with thousand separators and no decimals
        price_columns = [
            'Beszerzési ár', 'Vámköltség', 'Szállítási költség', 'Landing cost', 'Landing DEV', 'Landing HUF net',
            'Category IV', 'Category III', 'Category II', 'Category I',
            'MSRP HUF net', 'MSRP HUF bruttó', 'Net HUF NEW MSRP', 'Br NEW HUF MSRP',
            'Jelenlegi MSRP (net)', 'Jelenlegi MSRP (br.)',
            'Thomann', 'Muziker', 'Készletérték', 'Értékesítés érték'
        ]
        
        # Format percentage columns
        percentage_columns = ['MSRP változás (%)', 'Partner / Thomann', 'MSRP / Thomann', 
                            'Partner / Muziker', 'MSRP / Muziker']
        
        # Create a copy for formatting
        df_display = pricing_df.copy()
        
        # Format price columns
        for col in price_columns:
            if col in df_display.columns:
                df_display[col] = df_display[col].apply(format_price_value)
        
        # Format percentage columns
        for col in percentage_columns:
            if col in df_display.columns:
                if 'változás' in col:
                    # MSRP change as percentage with % sign
                    df_display[col] = df_display[col].apply(format_percentage_change)
                else:
                    # Ratios as decimal numbers
                    df_display[col] = df_display[col].apply(format_ratio_value)
        
        # Display the formatted table
        st.dataframe(
            df_display,
            use_container_width=True,
            height=600
        )
        
        # Export options
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("Save Pricelist"):
                st.success("Pricelist saved successfully!")
        
        with col2:
            csv = pricing_df.to_csv(index=False)
            st.download_button(
                "Export CSV",
                csv,
                "pricelist.csv",
                "text/csv"
            )
        
        with col3:
            # Excel export would go here
            st.button("Export Excel")
    else:
        st.error("Failed to generate pricing table.")

def create_pricing_dataframe(products_data, brand_currency_cache, brand_exchange_rate_cache, sales_months):
    """Create the comprehensive pricing dataframe with per-brand currency support."""
    try:
        df_list = []
        
        # Pre-load all settings data to avoid repeated database queries
        all_brands = list(set([p.get('brand', '') for p in products_data]))
        
        # Pre-load customs/shipping rates for all brands and methods
        customs_shipping_cache = {}
        for brand in all_brands:
            # Cache rates for both EXW and FOB methods
            exw_customs, exw_shipping = get_customs_shipping_rates(brand, "EXW")
            fob_customs, fob_shipping = get_customs_shipping_rates(brand, "FOB")
            
            customs_shipping_cache[brand] = {
                "EXW": (exw_customs, exw_shipping),
                "FOB": (fob_customs, fob_shipping)
            }
        
        # Pre-load margin data for all brands (matching the JavaScript structure)
        brand_margin_data = {}
        for brand in all_brands:
            # Get margin data for this brand (same as in the JavaScript section)
            margin_data = get_all_margins_for_brand(brand)
            brand_margins = {}
            brand_default_margin = None
            
            for margin in margin_data:
                brand_margins[margin['category']] = margin['percentage']
                # Check if this is the default margin category for this brand
                default_multiplier = get_margin_default_category(brand)
                if abs(margin['value'] - default_multiplier) < 0.001:
                    brand_default_margin = margin['percentage']
            
            # If no default found, use first available margin
            if brand_default_margin is None and brand_margins:
                brand_default_margin = list(brand_margins.values())[0]
            
            brand_margin_data[brand] = {
                'margins': brand_margins,
                'default': brand_default_margin
            }
        
        # Pre-load discount rates for all brands (matching JavaScript structure)
        brand_discount_data = {}
        for brand in all_brands:
            brand_discount_data[brand] = get_discount_rates_by_brand(brand)
        
        # Pre-load stock, sales, and purchase data in bulk for better performance
        all_product_ids = [p.get('product_id', '') for p in products_data]
        bulk_stock_data = get_latest_stock_data(all_product_ids)
        bulk_sales_data = get_sales_history_data(all_product_ids, sales_months)
        bulk_purchase_data = get_purchase_history_data(all_product_ids, sales_months)
        
        # Pre-load competitor pricing data in bulk
        competitor_cache = {}
        try:
            from models import CompetitorScrape
            session = get_session()
            if session:
                competitor_results = session.query(CompetitorScrape).filter(
                    CompetitorScrape.product_code.in_(all_product_ids)
                ).all()
                
                for comp in competitor_results:
                    competitor_cache[comp.product_code] = {
                        'thomann_price': comp.thomann_price or 0,
                        'muziker_price': comp.muziker_price or 0,
                        'thomann_url': comp.thomann_url or '',
                        'muziker_url': comp.muziker_url or '',
                        'r55_price': comp.r55_price or 0,
                        'r55_stock': comp.r55_stock or '',
                        'kytary_price': comp.kytary_price or 0,
                        'kytary_stock': comp.kytary_stock or '',
                        'mezzo_price': comp.mezzo_price or 0,
                        'mezzo_stock': comp.mezzo_stock or '',
                        'allegro_price': comp.allegro_price or 0,
                        'allegro_stock': comp.allegro_stock or '',
                        'pako_price': comp.pako_price or 0,
                        'pako_stock': comp.pako_stock or '',
                        'mango_price': comp.mango_price or 0,
                        'mango_stock': comp.mango_stock or '',
                        'plaza_price': comp.plaza_price or 0,
                        'plaza_stock': comp.plaza_stock or '',
                        'diszkont_price': comp.diszkont_price or 0,
                        'diszkont_stock': comp.diszkont_stock or '',
                        'hitspace_price': comp.hitspace_price or 0,
                        'hitspace_stock': comp.hitspace_stock or ''
                    }
                close_session(session)
        except Exception as e:
            print(f"Error loading competitor data: {e}")
        
        for product in products_data:
            row_data = {}
            
            # Basic product info
            brand = product.get('brand', '')
            product_id = product.get('product_id', '')
            row_data['Gyártó'] = brand
            row_data['Termékkód'] = product_id
            row_data['Terméknév'] = product.get('product_name', '')
            
            # Get brand-specific currency and exchange rate
            brand_currency_info = brand_currency_cache.get(brand, {})
            purchase_currency = brand_currency_info.get('purchase_currency', 'EUR')
            brand_exchange_rate = brand_exchange_rate_cache.get(brand, 1.0)
            
            # Purchase price and current MSRP (always in HUF from prices table)
            purchase_price = product.get('purchase_price', 0) or 0
            current_huf_msrp = product.get('current_huf_msrp', 0) or 0  # HUF listaár from prices table
            net_man_msrp = product.get('msrp', 0) or 0  # Net manufacturer MSRP from prices table
            msrp_currency = product.get('msrp_currency', 'EUR')  # Actual currency of the MSRP value
            
            row_data['Beszerzési ár'] = purchase_price
            row_data['Net Man MSRP'] = net_man_msrp
            row_data['_msrp_currency'] = msrp_currency  # Store actual MSRP currency for formatting
            row_data['Jelenlegi MSRP (net)'] = current_huf_msrp
            row_data['Jelenlegi MSRP (br.)'] = current_huf_msrp * 1.27 if current_huf_msrp else 0
            
            # Calculate suggested margin percentage
            if purchase_price > 0 and net_man_msrp > 0:
                suggested_margin = ((net_man_msrp / purchase_price) - 1) * 100
                row_data['Suggested Margin'] = round(suggested_margin, 0)
            else:
                row_data['Suggested Margin'] = 0
            
            # Determine shipping method for this product row
            row_key = f"{product_id}_{brand}"
            
            # Check for per-row shipping override first
            if hasattr(st.session_state, 'row_shipping_overrides') and row_key in st.session_state.row_shipping_overrides:
                shipping_method = st.session_state.row_shipping_overrides[row_key]
            else:
                # Get brand's default shipping method
                shipping_method = get_default_shipping_method(brand)
            
            # Get cached customs and shipping rates for this specific shipping method
            brand_rates = customs_shipping_cache.get(brand, {})
            customs_rate, shipping_rate = brand_rates.get(shipping_method, (0.0, 0.0))
            
            # Add customs and shipping rate columns to display
            row_data['Customs Rate'] = f"{customs_rate * 100:.0f}%"
            row_data['Shipping Rate'] = f"{shipping_rate * 100:.0f}%"
            row_data['Shipping Method'] = shipping_method
            
            # Add margin category and percentage columns directly here
            # Check for per-row margin overrides first
            row_key = f"{product_id}_{brand}"
            if hasattr(st.session_state, 'row_margin_overrides') and row_key in st.session_state.row_margin_overrides:
                selected_margin_percentage = st.session_state.row_margin_overrides[row_key]
            else:
                # Get brand's default margin percentage from brand_margin_data
                brand_margin_info = brand_margin_data.get(brand, {})
                selected_margin_percentage = brand_margin_info.get('default')
            
            # Find the margin category name for this percentage
            margin_cat = "Unknown"
            brand_margin_info = brand_margin_data.get(brand, {})
            available_margins = brand_margin_info.get('margins', {})
            for category, percentage in available_margins.items():
                if abs(percentage - (selected_margin_percentage or 0)) < 0.001:
                    margin_cat = category
                    break
            
            row_data['Margin Cat'] = margin_cat
            row_data['Margin %'] = selected_margin_percentage or 0
            
            # Debug logging for first product
            if len(df_list) == 0:  # Only log for first product
                print(f"DEBUG: Brand={brand}, Shipping={shipping_method}")
                print(f"DEBUG: Customs Rate = {customs_rate} -> {row_data['Customs Rate']}")
                print(f"DEBUG: Shipping Rate = {shipping_rate} -> {row_data['Shipping Rate']}")
                print(f"DEBUG: Row data types: Customs={type(row_data['Customs Rate'])}, Shipping={type(row_data['Shipping Rate'])}")
            
            # Calculate landing cost using the row-specific shipping method
            landing_cost = calculate_landing_cost(purchase_price, customs_rate, shipping_rate)
            row_data['Landing DEV'] = landing_cost
            row_data['Landing HUF net'] = landing_cost * brand_exchange_rate
            row_data['Landing HUF br.'] = row_data['Landing HUF net'] * 1.27
            
            # Use the margin percentage we already calculated above
            margin_percentage = selected_margin_percentage or 0
            if margin_percentage == 0:
                st.error(f"No default margin found for brand {brand}")
                continue
            
            # Note: margin_percentage is used for calculations but not displayed as separate column
            # since we already have "Margin %" column showing the selected margin category
            
            # Get export margin for the brand
            from utils import get_export_margin_for_brand
            export_margin = get_export_margin_for_brand(brand)
            row_data['Export Margin'] = export_margin
            
            # Calculate export price
            if purchase_price and purchase_price > 0 and export_margin and export_margin > 0:
                export_price = purchase_price * (1 + export_margin / 100)
                row_data['Export Price'] = round(export_price, 0)
            else:
                row_data['Export Price'] = 0
            
            # Get brand-specific discount rates (same as JavaScript)
            brand_discounts = brand_discount_data.get(brand, {})
            
            # Calculate pricing using the same logic as JavaScript dynamic calculation
            landing_huf_net = row_data['Landing HUF net']
            # Use rounded value to match what JavaScript sees in the display
            landing_huf_br = round(row_data['Landing HUF br.'])
            
            # Calculate Category IV (minimum reseller price) from Landing Br + Margin
            margin_multiplier = (100 + margin_percentage) / 100
            category_iv = round(landing_huf_br * margin_multiplier)
            
            # Get brand-specific discount rates (check if all required keys exist)
            required_keys = ['IV', 'III', 'II', 'I']
            missing_keys = [key for key in required_keys if key not in brand_discounts]
            
            if missing_keys:
                st.error(f"⚠️ Brand '{brand}' is missing discount settings for categories: {', '.join(missing_keys)}")
                st.info(f"Please configure discount rates for {brand} in the Settings page under 'Discount Settings'")
                continue
                
            discount_iv = brand_discounts['IV'] / 100  # Convert to decimal
            discount_iii = brand_discounts['III'] / 100
            discount_ii = brand_discounts['II'] / 100
            discount_i = brand_discounts['I'] / 100
            
            # Calculate pricing tiers going up from Category IV
            category_iii = round(category_iv / (1 - discount_iv))
            category_ii = round(category_iii / (1 - discount_iii))
            category_i = round(category_ii / (1 - discount_ii))
            new_msrp_gross = round(category_i / (1 - discount_i))
            new_msrp_net = round(new_msrp_gross / 1.27)
            
            # Update pricing columns with proper formatting
            row_data['Category IV'] = category_iv
            row_data['Category III'] = category_iii
            row_data['Category II'] = category_ii
            row_data['Category I'] = category_i
            row_data['Net HUF NEW MSRP'] = new_msrp_net
            row_data['Br HUF NEW MSRP'] = new_msrp_gross
            
            # Calculate Br HUF Man MSRP from Net Man MSRP
            if net_man_msrp and net_man_msrp > 0:
                br_huf_man_msrp = round(net_man_msrp * brand_exchange_rate * 1.27)
                row_data['Br HUF Man MSRP'] = br_huf_man_msrp
            else:
                row_data['Br HUF Man MSRP'] = 0
            
            # Calculate MSRP change (percentage only) - comparing HUF prices
            if current_huf_msrp and new_msrp_net:
                msrp_change_percent = ((new_msrp_net - current_huf_msrp) / current_huf_msrp * 100) if current_huf_msrp else 0
                row_data['MSRP változás (%)'] = round(msrp_change_percent, 0)
            else:
                row_data['MSRP változás (%)'] = 0
            
            # Add competitor pricing data
            competitor_data = competitor_cache.get(product_id, {})
            thomann_price = competitor_data.get('thomann_price', 0)
            muziker_price = competitor_data.get('muziker_price', 0)
            
            # Thomann pricing columns
            row_data['Thomann'] = thomann_price
            if thomann_price and category_iv:
                ratio = (category_iv / thomann_price) * 100 if thomann_price else 0
                row_data['Partner / Thomann'] = round(ratio, 0)
            else:
                row_data['Partner / Thomann'] = 0
                
            if thomann_price and new_msrp_gross:
                ratio = (new_msrp_gross / thomann_price) * 100 if thomann_price else 0
                row_data['MSRP / Thomann'] = round(ratio, 0)
            else:
                row_data['MSRP / Thomann'] = 0
            
            # Muziker pricing columns
            row_data['Muziker'] = muziker_price
            if muziker_price and category_iv:
                ratio = (category_iv / muziker_price) * 100 if muziker_price else 0
                row_data['Partner / Muziker'] = round(ratio, 0)
            else:
                row_data['Partner / Muziker'] = 0
                
            if muziker_price and new_msrp_gross:
                ratio = (new_msrp_gross / muziker_price) * 100 if muziker_price else 0
                row_data['MSRP / Muziker'] = round(ratio, 0)
            else:
                row_data['MSRP / Muziker'] = 0
            
            # Add all additional competitor prices and stock info
            row_data['R55'] = competitor_data.get('r55_price', 0) or 0
            row_data['R55 Stock'] = competitor_data.get('r55_stock', '') or ''
            row_data['Kytary'] = competitor_data.get('kytary_price', 0) or 0
            row_data['Kytary Stock'] = competitor_data.get('kytary_stock', '') or ''
            row_data['Mezzo'] = competitor_data.get('mezzo_price', 0) or 0
            row_data['Mezzo Stock'] = competitor_data.get('mezzo_stock', '') or ''
            row_data['Allegro'] = competitor_data.get('allegro_price', 0) or 0
            row_data['Allegro Stock'] = competitor_data.get('allegro_stock', '') or ''
            row_data['Pako'] = competitor_data.get('pako_price', 0) or 0
            row_data['Pako Stock'] = competitor_data.get('pako_stock', '') or ''
            row_data['Mango'] = competitor_data.get('mango_price', 0) or 0
            row_data['Mango Stock'] = competitor_data.get('mango_stock', '') or ''
            row_data['Pláza'] = competitor_data.get('plaza_price', 0) or 0
            row_data['Pláza Stock'] = competitor_data.get('plaza_stock', '') or ''
            row_data['Diszkont'] = competitor_data.get('diszkont_price', 0) or 0
            row_data['Diszkont Stock'] = competitor_data.get('diszkont_stock', '') or ''
            row_data['Hitspace'] = competitor_data.get('hitspace_price', 0) or 0
            row_data['Hitspace Stock'] = competitor_data.get('hitspace_stock', '') or ''
            
            # Calculate average prices
            main_prices = [p for p in [thomann_price, muziker_price] if p > 0]
            all_prices = [p for p in [
                thomann_price, muziker_price, row_data['R55'], row_data['Kytary'],
                row_data['Mezzo'], row_data['Allegro'], row_data['Pako'], row_data['Mango'],
                row_data['Pláza'], row_data['Diszkont'], row_data['Hitspace']
            ] if p > 0]
            
            row_data['Average Price (Main)'] = round(sum(main_prices) / len(main_prices), 0) if main_prices else 0
            row_data['Average Price (All)'] = round(sum(all_prices) / len(all_prices), 0) if all_prices else 0
            
            # Add sales history data
            product_sales = bulk_sales_data.get(product_id, {})
            row_data['Sales Count'] = product_sales.get('sales_count', 0)
            row_data['Last Sale Date'] = product_sales.get('last_sale_date', None)
            
            # Add purchase history data  
            product_purchases = bulk_purchase_data.get(product_id, {})
            row_data['Purchase Count'] = product_purchases.get('purchase_count', 0)
            row_data['Last Purchase Date'] = product_purchases.get('last_purchase_date', None)
            
            # Customs and shipping rates already added above as formatted percentages
            
            # Add stock data from bulk queries
            product_stock = bulk_stock_data.get(product_id, {})
            row_data.update(product_stock)
            
            df_list.append(row_data)
        
        # Create dataframe and explicitly set data types for rate columns to preserve string formatting
        df = pd.DataFrame(df_list)
        
        # Force rate columns to remain as strings to preserve percentage formatting
        if 'Customs Rate' in df.columns:
            df['Customs Rate'] = df['Customs Rate'].astype(str)
        if 'Shipping Rate' in df.columns:
            df['Shipping Rate'] = df['Shipping Rate'].astype(str)
            
        return df
        
    except Exception as e:
        st.error(f"Error creating pricing table: {str(e)}")
        return None

def get_competitor_prices(product_id):
    """Get competitor pricing data for a product."""
    session = get_session()
    competitor_data = {
        'Thomann': 0,
        'Partner / Thomann': 0,
        'MSRP / Thomann': 0,
        'Muziker': 0,
        'Partner / Muziker': 0,
        'MSRP / Muziker': 0
    }
    
    if session:
        try:
            competitor = session.query(CompetitorScrape).filter(
                CompetitorScrape.product_code == product_id
            ).first()
            
            if competitor:
                competitor_data['Thomann'] = competitor.thomann_price or 0
                competitor_data['Muziker'] = competitor.muziker_price or 0
            
        except Exception as e:
            st.error(f"Error fetching competitor data: {str(e)}")
        finally:
            close_session(session)
    
    return competitor_data

def show_competitor_scrape():
    """Display enhanced competitor scrape management with detailed product information and scraping capabilities."""
    import pandas as pd
    st.subheader("Competitor Scrape Management")
    
    # Filters section
    st.write("### Filters")
    
    # First row of filters
    col1, col2, col3, col4 = st.columns(4)
    
    # Get filter options
    all_suppliers = get_suppliers()
    all_brands = get_brands()
    all_product_groups = [group for group in get_product_groups() if group]
    all_web_categories = [category for category in get_product_categories() if category]
    
    with col1:
        selected_suppliers = st.multiselect(
            "Suppliers",
            options=all_suppliers,
            default=[]
        )
    
    with col2:
        selected_brands = st.multiselect(
            "Brands", 
            options=all_brands,
            default=[]
        )
    
    with col3:
        selected_product_groups = st.multiselect(
            "Product Groups",
            options=all_product_groups,
            default=[]
        )
    
    with col4:
        selected_web_categories = st.multiselect(
            "Web Categories",
            options=all_web_categories,
            default=[]
        )
    
    # Second row for Product filter and Records Limit
    col5, col6, col7, col8 = st.columns(4)
    
    with col5:
        product_filter = st.text_input(
            "Product Filter (Termékkód)",
            placeholder="Enter product code (e.g., TS4)",
            help="Enter text to filter products by product code"
        )
    
    with col6:
        records_limit = st.selectbox(
            "Records Limit",
            options=[100, 250, 500, 1000, "All"],
            index=0
        )
        limit = None if records_limit == "All" else records_limit
    
    # Get detailed competitor data with filters
    detailed_data = get_detailed_competitor_data(
        suppliers=selected_suppliers if selected_suppliers else None,
        brands=selected_brands if selected_brands else None,
        product_groups=selected_product_groups if selected_product_groups else None,
        web_categories=selected_web_categories if selected_web_categories else None,
        product_filter=product_filter.strip() if product_filter else None,
        limit=limit
    )
    
    if not detailed_data:
        st.info("No competitor data found. Use the Data page to import competitor pricing data or adjust your filters.")
        return
    
    # Convert to DataFrame for AgGrid display
    df = pd.DataFrame(detailed_data)
    
    # Display data count
    st.info(f"Found {len(df)} competitor records")
    
    # Selection control buttons section
    st.markdown("### Product Selection Controls")
    
    # Button layout
    col_select, col_apply, col_deselect, col_status = st.columns([1, 1, 1, 1])
    
    with col_select:
        select_all_clicked = st.button("🔲 Select All", key="btn_select_all_comp")
    
    with col_apply:
        apply_selection_clicked = st.button("Apply", key="btn_apply_selection_comp")
    
    with col_deselect:
        deselect_all_clicked = st.button("❌ Clear All", key="btn_deselect_all_comp")
    
    with col_status:
        current_selection = getattr(st.session_state, 'selected_competitor_products', pd.DataFrame())
        selected_count = len(current_selection) if not current_selection.empty else 0
        st.info(f"Selected: {selected_count}")
    
    # Process button clicks - store selected rows for AgGrid
    if select_all_clicked:
        st.session_state['selected_competitor_products'] = df.copy()
        # Store indices for AgGrid pre-selection
        st.session_state['preselected_rows'] = list(range(len(df)))
        st.success(f"Selected all {len(df)} products")
        st.rerun()
    
    if deselect_all_clicked:
        if 'selected_competitor_products' in st.session_state:
            del st.session_state['selected_competitor_products']
        if 'preselected_rows' in st.session_state:
            del st.session_state['preselected_rows']
        st.success("Cleared all selections")
        st.rerun()
    
    if apply_selection_clicked:
        # This will be processed after the AgGrid is rendered and we have the current selection
        st.session_state['apply_manual_selection'] = True
    
    # Prepare dataframe for AgGrid display
    display_df = df.copy()
    
    # Format URLs column
    def format_urls(row):
        urls = []
        if row.get('thomann_url') and str(row.get('thomann_url')).lower() not in ['nan', 'none', '']:
            urls.append('Thomann')
        if row.get('muziker_url') and str(row.get('muziker_url')).lower() not in ['nan', 'none', '']:
            urls.append('Muziker')
        if row.get('arukereso_url') and str(row.get('arukereso_url')).lower() not in ['nan', 'none', '']:
            urls.append('Árukereső')
        return ', '.join(urls) if urls else 'No URLs'
    
    # Format last scraped date
    def format_last_scraped(date_val):
        if date_val is None or str(date_val).lower() in ['nan', 'none', '']:
            return '-'
        try:
            from datetime import datetime
            if isinstance(date_val, str):
                return datetime.fromisoformat(date_val).strftime('%Y-%m-%d %H:%M')
            else:
                return date_val.strftime('%Y-%m-%d %H:%M')
        except (ValueError, TypeError):
            return '-'
    
    # Apply formatting
    display_df['URLs'] = display_df.apply(format_urls, axis=1)
    display_df['Last Scraped'] = display_df['last_scraped'].apply(format_last_scraped)
    
    # Select columns for display
    columns_to_display = [
        'primary_supplier', 'brand', 'product_code', 'product_name', 
        'URLs', 'scrape_status', 'Last Scraped'
    ]
    
    # Rename columns for better display
    column_mapping = {
        'primary_supplier': 'Supplier',
        'brand': 'Brand', 
        'product_code': 'Product Code',
        'product_name': 'Product Name',
        'scrape_status': 'Status'
    }
    
    display_df = display_df[columns_to_display].rename(columns=column_mapping)
    
    # Configure AgGrid for competitor scrape table
    from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode, DataReturnMode
    
    gb = GridOptionsBuilder.from_dataframe(display_df)
    
    # Configure columns
    gb.configure_column("Supplier", width=120, pinned='left')
    gb.configure_column("Brand", width=100, pinned='left') 
    gb.configure_column("Product Code", width=120, pinned='left')
    gb.configure_column("Product Name", width=300)
    gb.configure_column("URLs", width=150, cellStyle={"textAlign": "center"})
    gb.configure_column("Status", width=100, cellStyle={"textAlign": "center"})
    gb.configure_column("Last Scraped", width=140, cellStyle={"textAlign": "center"})
    
    # Configure grid options for selection
    gb.configure_default_column(resizable=True, sortable=True, filterable=True)
    gb.configure_selection(selection_mode="multiple", use_checkbox=True)
    gb.configure_grid_options(
        enableRangeSelection=True,
        suppressRowClickSelection=True,  # Only allow selection via checkboxes
        rowSelection="multiple",
        suppressRowDeselection=False,  # Allow deselection for proper clearing
        headerCheckboxSelection=True,  # Add select all checkbox in header
        headerCheckboxSelectionFilteredOnly=True  # Only select filtered rows
    )
    
    grid_options = gb.build()
    
    # Get pre-selected rows if any
    preselected_rows = getattr(st.session_state, 'preselected_rows', [])
    
    # Display AgGrid
    grid_response = AgGrid(
        display_df,
        gridOptions=grid_options,
        update_mode=GridUpdateMode.SELECTION_CHANGED,
        data_return_mode=DataReturnMode.FILTERED_AND_SORTED,
        height=600,
        width='100%',
        allow_unsafe_jscode=False,
        key="competitor_scrape_grid",
        pre_selected_rows=preselected_rows
    )
    
    # Handle selection
    selected_rows = grid_response['selected_rows']
    
    # Process manual selection application
    apply_manual = getattr(st.session_state, 'apply_manual_selection', False)
    if apply_manual:
        if selected_rows is not None and len(selected_rows) > 0:
            # Map back to original dataframe to get full data
            selected_product_codes = selected_rows['Product Code'].tolist()
            selected_df = df[df['product_code'].isin(selected_product_codes)].copy()
            
            # Update session state
            st.session_state['selected_competitor_products'] = selected_df
            st.success(f"Applied selection: {len(selected_df)} products selected")
        else:
            st.warning("No rows selected to apply. Please check some rows first.")
        
        # Clear the apply flag
        del st.session_state['apply_manual_selection']
        st.rerun()
    
    # Process automatic selection changes (when not manually applying)
    elif selected_rows is not None and not apply_manual:
        if len(selected_rows) > 0:
            # Map back to original dataframe to get full data
            selected_product_codes = selected_rows['Product Code'].tolist()
            selected_df = df[df['product_code'].isin(selected_product_codes)].copy()
            
            # Update session state
            st.session_state['selected_competitor_products'] = selected_df
        else:
            # Clear selection when no rows are selected
            if 'selected_competitor_products' in st.session_state:
                del st.session_state['selected_competitor_products']
        
        # Clear preselected rows after processing to prevent conflicts
        if 'preselected_rows' in st.session_state:
            del st.session_state['preselected_rows']
    
    # Get current selection for scraping actions
    selected_products = getattr(st.session_state, 'selected_competitor_products', pd.DataFrame())
    
    # Scraping controls
    st.write("### Scraping Actions")
    
    if not selected_products.empty:
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔄 Scrape Selected Products", type="primary"):
                from scrape import scrape_competitor_products
                
                # Get product codes from selected products
                product_codes = selected_products['product_code'].tolist()
                
                # Create progress container
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # Create persistent log container
                log_container = st.container()
                scraping_logs = []
                
                def progress_callback(current, total, product_code):
                    progress = current / total
                    progress_bar.progress(progress)
                    status_text.text(f"Scraping {current}/{total}: {product_code}")
                
                # Initialize session state for logs if not exists
                if 'scraping_logs' not in st.session_state:
                    st.session_state.scraping_logs = []
                
                # Clear previous logs
                st.session_state.scraping_logs = []
                
                # Perform scraping
                results = scrape_competitor_products(product_codes, progress_callback)
                
                # Store results in session state for persistence
                from datetime import datetime
                st.session_state.last_scraping_results = results
                st.session_state.last_scraping_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                # Display final results summary
                if results['success']:
                    st.success(f"Scraping completed! {results['successful_scrapes']} products scraped successfully.")
                    if results['failed_scrapes'] > 0:
                        st.warning(f"{results['failed_scrapes']} products failed to scrape.")
                    
                    st.rerun()
                else:
                    st.error(f"Scraping failed: {results.get('error', 'Unknown error')}")
                
                progress_bar.empty()
                status_text.empty()
        
        with col2:
            if st.button("📊 Export Selected"):
                # Create export data
                export_df = selected_products[[
                    'product_code', 'product_name', 'brand', 'product_group',
                    'thomann_url', 'muziker_url', 'arukereso_url'
                ]]
                
                # Convert to CSV
                csv = export_df.to_csv(index=False)
                st.download_button(
                    label="Download CSV",
                    data=csv,
                    file_name=f"competitor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        
        with col3:
            if st.button("📋 View URLs"):
                st.write("**Selected Product URLs:**")
                for _, row in selected_products.iterrows():
                    st.write(f"**{row['product_code']}** - {row['product_name']}")
                    if row['thomann_url']:
                        st.write(f"🎵 Thomann: {row['thomann_url']}")
                    if row['muziker_url']:
                        st.write(f"🎼 Muziker: {row['muziker_url']}")
                    st.write("---")
    
    else:
        st.info("Select products from the table above to enable scraping actions")
    
    # Test scraping functionality
    st.write("### Test Scraping")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # Test direct scraping
        st.write("**Direct Scraping Test**")
        test_product_code = st.text_input(
            "Product Code",
            placeholder="e.g., SM58-LCE",
            help="Enter a product code to test direct scraping"
        )
        
        if st.button("🔍 Test Direct Scrape") and test_product_code:
            from scrape import test_scraping_single_product
            with st.spinner(f"Testing scraping for {test_product_code}..."):
                results = test_scraping_single_product(test_product_code)
                if results['success']:
                    st.success("Direct scraping test successful!")
                    if results['results']['thomann']:
                        st.write(f"Thomann: {results['results']['thomann']}")
                    if results['results']['muziker']:
                        st.write(f"Muziker: {results['results']['muziker']}")
                else:
                    st.error(f"Test failed: {results.get('error', 'Unknown error')}")
    
    with col2:
        # Test Árukereső scraping
        st.write("**Árukereső Test**")
        arukereso_test_code = st.text_input(
            "Product Code for Árukereső",
            placeholder="e.g., SM58-LCE",
            help="Enter a product code that has an Árukereső URL"
        )
        
        if st.button("🛒 Test Árukereső Scrape") and arukereso_test_code:
            from scrape import scrape_arukereso_product
            from database import get_session
            from models import CompetitorScrape
            
            # Get the Árukereső URL for this product
            session = get_session()
            if session:
                try:
                    competitor = session.query(CompetitorScrape).filter_by(
                        product_code=arukereso_test_code
                    ).first()
                    
                    if competitor and competitor.arukereso_url:
                        st.info(f"Testing Árukereső URL: {competitor.arukereso_url}")
                        with st.spinner(f"Scraping Árukereső for {arukereso_test_code}..."):
                            results = scrape_arukereso_product(competitor.arukereso_url)
                            
                            if results['success']:
                                st.success(f"Árukereső scraping successful! Found {len(results['competitors_found'])} competitors")
                                
                                # Display results in organized format
                                for competitor_name in results['competitors_found']:
                                    store_mapping = {
                                        'muziker': ('muziker_price', 'muziker_stock'),
                                        'r55': ('r55_price', 'r55_stock'),
                                        'kytary': ('kytary_price', 'kytary_stock'),
                                        'mezzo': ('mezzo_price', 'mezzo_stock'),
                                        'allegro': ('allegro_price', 'allegro_stock'),
                                        'pako': ('pako_price', 'pako_stock'),
                                        'mango': ('mango_price', 'mango_stock'),
                                        'plaza': ('plaza_price', 'plaza_stock'),
                                        'diszkont': ('diszkont_price', 'diszkont_stock'),
                                        'hitspace': ('hitspace_price', 'hitspace_stock')
                                    }
                                    
                                    if competitor_name in store_mapping:
                                        price_field, stock_field = store_mapping[competitor_name]
                                        price = results.get(price_field, 0)
                                        stock = results.get(stock_field, 'Unknown')
                                        st.write(f"**{competitor_name.upper()}**: {price} Ft - {stock}")
                            else:
                                st.error(f"Árukereső scraping failed: {results.get('error', 'Unknown error')}")
                    else:
                        st.warning(f"No Árukereső URL found for product {arukereso_test_code}")
                
                except Exception as e:
                    st.error(f"Database error: {str(e)}")
                finally:
                    session.close()
    
    with col3:
        # Database URL test
        st.write("**Database Test**")
        db_test_code = st.text_input(
            "Product Code for DB Check",
            placeholder="e.g., SM58-LCE",
            help="Check if product has scraping URLs in database"
        )
        
        if st.button("🔍 Check Database URLs") and db_test_code:
            from database import get_session
            from models import CompetitorScrape
            
            session = get_session()
            if session:
                try:
                    competitor = session.query(CompetitorScrape).filter_by(
                        product_code=db_test_code
                    ).first()
                    
                    if competitor:
                        st.success(f"Found product {db_test_code} in database")
                        
                        # Display available URLs
                        urls_found = []
                        if competitor.thomann_url:
                            urls_found.append("Thomann")
                            st.write(f"🎵 **Thomann**: {competitor.thomann_url}")
                        if competitor.muziker_url:
                            urls_found.append("Muziker")
                            st.write(f"🎼 **Muziker**: {competitor.muziker_url}")
                        if competitor.arukereso_url:
                            urls_found.append("Árukereső")
                            st.write(f"🛒 **Árukereső**: {competitor.arukereso_url}")
                        if competitor.argep_url:
                            urls_found.append("Árgép")
                            st.write(f"💰 **Árgép**: {competitor.argep_url}")
                        
                        if not urls_found:
                            st.warning("No scraping URLs found for this product")
                        else:
                            st.info(f"Available sources: {', '.join(urls_found)}")
                    else:
                        st.warning(f"Product {db_test_code} not found in competitor database")
                
                except Exception as e:
                    st.error(f"Database error: {str(e)}")
                finally:
                    session.close()
    
    # Display persistent scraping results if available
    if hasattr(st.session_state, 'last_scraping_results') and st.session_state.last_scraping_results:
        results = st.session_state.last_scraping_results
        scraping_time = getattr(st.session_state, 'last_scraping_time', 'Unknown time')
        
        st.markdown("---")
        st.subheader(f"📊 Last Scraping Results - {scraping_time}")
        
        # Summary metrics
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Products", len(results.get('products', {})))
        with col2:
            st.metric("Successful", results.get('successful_scrapes', 0))
        with col3:
            st.metric("Failed", results.get('failed_scrapes', 0))
        
        # Create comprehensive table format for all competitor data
        st.write("**Detailed Results:**")
        
        # Prepare data for the table
        table_data = []
        for product_code, result in results.get('products', {}).items():
            row = {'Product Code': product_code}
            
            # Direct competitors (Thomann, Muziker)
            if 'thomann' in result:
                row['Thomann Price'] = f"{result['thomann']['price']} Ft"
                row['Thomann Stock'] = result['thomann']['stock']
            else:
                row['Thomann Price'] = result.get('thomann_error', 'No data')
                row['Thomann Stock'] = '-'
            
            if 'muziker' in result:
                muziker_price = f"{result['muziker']['price']} Ft"
                if result['muziker'].get('source') == 'arukereso':
                    muziker_price += " (Árukereső)"
                row['Muziker Price'] = muziker_price
                row['Muziker Stock'] = result['muziker']['stock']
            else:
                row['Muziker Price'] = result.get('muziker_error', 'No data')
                row['Muziker Stock'] = '-'
            
            # Árukereső competitors
            if 'arukereso' in result:
                arukereso_data = result['arukereso']
                
                # R55
                if 'r55_price' in arukereso_data and arukereso_data['r55_price']:
                    row['R55 Price'] = f"{arukereso_data['r55_price']} Ft"
                    row['R55 Stock'] = arukereso_data.get('r55_stock', '-')
                else:
                    row['R55 Price'] = '-'
                    row['R55 Stock'] = '-'
                
                # Kytary
                if 'kytary_price' in arukereso_data and arukereso_data['kytary_price']:
                    row['Kytary Price'] = f"{arukereso_data['kytary_price']} Ft"
                    row['Kytary Stock'] = arukereso_data.get('kytary_stock', '-')
                else:
                    row['Kytary Price'] = '-'
                    row['Kytary Stock'] = '-'
                
                # Mezzo
                if 'mezzo_price' in arukereso_data and arukereso_data['mezzo_price']:
                    row['Mezzo Price'] = f"{arukereso_data['mezzo_price']} Ft"
                    row['Mezzo Stock'] = arukereso_data.get('mezzo_stock', '-')
                else:
                    row['Mezzo Price'] = '-'
                    row['Mezzo Stock'] = '-'
                
                # Allegro
                if 'allegro_price' in arukereso_data and arukereso_data['allegro_price']:
                    row['Allegro Price'] = f"{arukereso_data['allegro_price']} Ft"
                    row['Allegro Stock'] = arukereso_data.get('allegro_stock', '-')
                else:
                    row['Allegro Price'] = '-'
                    row['Allegro Stock'] = '-'
                
                # Pako
                if 'pako_price' in arukereso_data and arukereso_data['pako_price']:
                    row['Pako Price'] = f"{arukereso_data['pako_price']} Ft"
                    row['Pako Stock'] = arukereso_data.get('pako_stock', '-')
                else:
                    row['Pako Price'] = '-'
                    row['Pako Stock'] = '-'
                
                # Mango
                if 'mango_price' in arukereso_data and arukereso_data['mango_price']:
                    row['Mango Price'] = f"{arukereso_data['mango_price']} Ft"
                    row['Mango Stock'] = arukereso_data.get('mango_stock', '-')
                else:
                    row['Mango Price'] = '-'
                    row['Mango Stock'] = '-'
                
                # Plaza
                if 'plaza_price' in arukereso_data and arukereso_data['plaza_price']:
                    row['Plaza Price'] = f"{arukereso_data['plaza_price']} Ft"
                    row['Plaza Stock'] = arukereso_data.get('plaza_stock', '-')
                else:
                    row['Plaza Price'] = '-'
                    row['Plaza Stock'] = '-'
                
                # Diszkont
                if 'diszkont_price' in arukereso_data and arukereso_data['diszkont_price']:
                    row['Diszkont Price'] = f"{arukereso_data['diszkont_price']} Ft"
                    row['Diszkont Stock'] = arukereso_data.get('diszkont_stock', '-')
                else:
                    row['Diszkont Price'] = '-'
                    row['Diszkont Stock'] = '-'
                
                # Hitspace
                if 'hitspace_price' in arukereso_data and arukereso_data['hitspace_price']:
                    row['Hitspace Price'] = f"{arukereso_data['hitspace_price']} Ft"
                    row['Hitspace Stock'] = arukereso_data.get('hitspace_stock', '-')
                else:
                    row['Hitspace Price'] = '-'
                    row['Hitspace Stock'] = '-'
                
                # Summary info
                competitors_found = arukereso_data.get('competitors_found', [])
                row['Árukereső Competitors'] = f"{len(competitors_found)} found: {', '.join(competitors_found)}" if competitors_found else 'None found'
            else:
                # No Árukereső data available
                for competitor in ['R55', 'Kytary', 'Mezzo', 'Allegro', 'Pako', 'Mango', 'Plaza', 'Diszkont', 'Hitspace']:
                    row[f'{competitor} Price'] = '-'
                    row[f'{competitor} Stock'] = '-'
                row['Árukereső Competitors'] = result.get('arukereso_error', 'Not scraped')
            
            table_data.append(row)
        
        # Display the comprehensive table
        if table_data:
            df_results = pd.DataFrame(table_data)
            
            # Reorder columns for better display
            ordered_columns = ['Product Code', 'Thomann Price', 'Thomann Stock', 'Muziker Price', 'Muziker Stock']
            competitor_columns = [col for col in df_results.columns if col not in ordered_columns and col != 'Árukereső Competitors']
            ordered_columns.extend(sorted(competitor_columns))
            ordered_columns.append('Árukereső Competitors')
            
            df_results = df_results[ordered_columns]
            
            st.dataframe(
                df_results,
                use_container_width=True,
                height=400,
                hide_index=True
            )
        else:
            st.info("No scraping results to display")

    # Data management
    st.write("### Data Management")
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔄 Refresh Data"):
            if hasattr(st.session_state, 'selected_competitor_products'):
                del st.session_state['selected_competitor_products']
            st.rerun()
    
    with col2:
        st.info("Import new competitor data via Data page → Competitor Scrape")