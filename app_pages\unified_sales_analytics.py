"""
Unified Sales Analytics Page

This page combines all sales analytics visualizations into a single
flowing layout rather than using tabs. It includes:
- Performance Overview (KPIs)
- Sales Trend Over Time
- Customer Categories Performance
- Top 10 Buyers
- Product Portfolio Analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from sqlalchemy import func, desc, and_, extract, text
import database as db
from models import Sale, Buyer, Product
from components.customer_categories_rendering import render_customer_performance
from components.sales_overview import display_metric_with_growth, create_sales_trend_chart
from components.product_portfolio_analysis import render_portfolio_analysis

def format_large_number(value):
    """Format large numbers to #,##M and #,##K format for better readability."""
    if value >= 1_000_000:
        return f"{value/1_000_000:.2f}M"
    elif value >= 1_000:
        return f"{value/1_000:.2f}K"
    else:
        return f"{value:.0f}"

def show():
    """Display the unified sales analytics page with all sections in a flowing layout."""
    st.title("Unified Sales Analytics")
    
    # Initialize database session
    session = None
    
    try:
        # Make sure the database is initialized first - force reinitialization 
        # to ensure we have a fresh connection
        success = db.init_db()
        if not success:
            st.error("Failed to initialize database. Please check your database configuration.")
            st.stop()
            
        # Get a database session
        session = db.get_session()
        
        # Check if we got a valid session
        if not session:
            st.error("Could not connect to the database. Please check your database configuration.")
            st.stop()
        
        # Create a Streamlit session state object to persist values
        if 'sales_filter_defaults' not in st.session_state:
            st.session_state.sales_filter_defaults = {
                'date_range': None,
                'countries': None,
                'product_groups': None,
                'brands': None
            }
        
        # Calculate the default date range (last month)
        today = datetime.now().date()
        default_end_date = today
        default_start_date = datetime(today.year, today.month, 1).date()
        if default_start_date.month == today.month:
            # If we're still in the same month, use previous month
            if default_start_date.month > 1:
                default_start_date = datetime(today.year, today.month - 1, 1).date()
            else:
                default_start_date = datetime(today.year - 1, 12, 1).date()
        
        # Calculate default comparison period (same period last year)
        default_comparison_start_date = datetime(default_start_date.year - 1, default_start_date.month, default_start_date.day).date()
        default_comparison_end_date = datetime(default_end_date.year - 1, default_end_date.month, default_end_date.day).date()
        
        # Filter section (collapsible) - placed at the top, default expanded
        with st.expander("Filters", expanded=True):
            # Apply consistent styling to all filter selectors using CSS
            st.markdown("""
            <style>
            /* Make all multiselect filters have consistent width */
            div[data-testid="stMultiSelect"] {
                min-width: 200px;
                width: 100%;
            }
            
            /* Ensure proper alignment of filter columns */
            div[data-testid="column"] {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            }
            
            /* Make date input full width to align with other elements */
            div[data-testid="stDateInput"] {
                width: 100%;
            }
            
            /* Force all filters to have the same height */
            div.stMultiSelect, div.stDateInput {
                margin-bottom: 0.5rem;
            }
            </style>
            """, unsafe_allow_html=True)
            
            # Create three columns with equal width for filters
            filter_cols = st.columns(3)
            
            # Time-related filters (left column)
            with filter_cols[0]:
                st.markdown("<h5 style='margin: 0; padding: 0;'>Time Filters</h5>", unsafe_allow_html=True)
                
                # Year selection with the label as placeholder text
                current_year = datetime.now().year
                year_options = list(range(current_year - 5, current_year + 1))
                selected_years = st.multiselect(
                    "Years",
                    options=year_options,
                    default=[current_year],
                    key="sales_years",
                    label_visibility="collapsed",
                    placeholder="Years"
                )
                
                # Quarter selection with the label as placeholder text
                quarter_options = ["Q1", "Q2", "Q3", "Q4", "YTD", "Custom"]
                selected_quarters = st.multiselect(
                    "Periods",
                    options=quarter_options,
                    default=["YTD"],
                    key="sales_quarters",
                    label_visibility="collapsed",
                    placeholder="Periods"
                )
                
                # Show custom date selector only if "Custom" is selected in periods
                show_custom_selectors = "Custom" in selected_quarters
                
                # Initialize all custom date related session state variables
                if 'custom_date_range' not in st.session_state:
                    year = datetime.now().year
                    st.session_state.custom_date_range = (
                        datetime(year, 1, 1).date(),
                        datetime.now().date()
                    )
                
                # Initialize month and day variables
                if 'custom_start_month' not in st.session_state:
                    st.session_state.custom_start_month = 1
                if 'custom_start_day' not in st.session_state:
                    st.session_state.custom_start_day = 1
                if 'custom_end_month' not in st.session_state:
                    st.session_state.custom_end_month = datetime.now().month
                if 'custom_end_day' not in st.session_state:
                    st.session_state.custom_end_day = datetime.now().day
                
                # Display date selector for custom period
                if show_custom_selectors:
                    # Get current year from selection or use current year
                    year = selected_years[0] if selected_years else current_year
                    
                    # Date input with minimal styling
                    date_range = st.date_input(
                        "Custom date range",
                        value=st.session_state.custom_date_range,
                        key="custom_date_selector",
                        label_visibility="collapsed"
                    )
                    
                    # Store the selected dates
                    if isinstance(date_range, tuple) and len(date_range) == 2:
                        st.session_state.custom_date_range = date_range
                    
                    # Style the date selector
                    st.markdown("""
                    <style>
                    div[data-testid="stDateInput"] > div:first-child {
                        border: 2px solid #0068C9 !important;
                        border-radius: 5px;
                    }
                    </style>
                    """, unsafe_allow_html=True)
            
            # Buyer-related filters (middle column)
            with filter_cols[1]:
                st.markdown("<h5 style='margin: 0; padding: 0;'>Buyer Filters</h5>", unsafe_allow_html=True)
                
                # Get all countries from the database
                try:
                    countries_query = session.query(Buyer.country).distinct()
                    countries = [c[0] for c in countries_query if c[0]]  # Filter out None values
                    countries.sort()
                except Exception as e:
                    st.warning(f"Could not load countries: {str(e)}")
                    countries = ["Hungary", "Germany", "Austria"]  # Fallback
                
                # Country selection
                selected_countries = st.multiselect(
                    "Countries",
                    options=countries,
                    default=st.session_state.sales_filter_defaults['countries'],
                    key="sales_countries",
                    label_visibility="collapsed",
                    placeholder="Countries"
                )
                
                # Get buyer categories from database
                try:
                    categories_query = session.query(Buyer.buyer_category).distinct()
                    buyer_categories = [c[0] for c in categories_query if c[0]]  # Filter out None values
                    buyer_categories.sort()
                except Exception as e:
                    st.warning(f"Could not load buyer categories: {str(e)}")
                    buyer_categories = ["Retail", "Wholesale", "Direct"]  # Fallback
                
                # Buyer category selection
                selected_buyer_categories = st.multiselect(
                    "Buyer Categories",
                    options=buyer_categories,
                    key="sales_buyer_categories",
                    label_visibility="collapsed",
                    placeholder="Buyer Categories"
                )
                
                # Get buyers filtered by selected countries and categories if any
                buyers = []
                try:
                    buyers_query = session.query(Buyer.buyer_id, Buyer.buyer_name)
                    
                    # Apply country filter if selected
                    if selected_countries:
                        buyers_query = buyers_query.filter(Buyer.country.in_(selected_countries))
                    
                    # Apply category filter if selected
                    if selected_buyer_categories:
                        buyers_query = buyers_query.filter(Buyer.buyer_category.in_(selected_buyer_categories))
                    
                    # Get results and format for display
                    buyers = [(b.buyer_id, b.buyer_name) for b in buyers_query.all()]
                    buyers.sort(key=lambda x: x[1])  # Sort by name
                except Exception as e:
                    st.warning(f"Could not load buyers: {str(e)}")
                
                # Buyer selection (filtered by country and category if selected)
                selected_buyers = st.multiselect(
                    "Buyers",
                    options=[b[0] for b in buyers],
                    format_func=lambda x: next((b[1] for b in buyers if b[0] == x), x),
                    key="sales_buyers",
                    label_visibility="collapsed",
                    placeholder="Buyers"
                )
            
            # Product-related filters (right column)
            with filter_cols[2]:
                st.markdown("<h5 style='margin: 0; padding: 0;'>Product Filters</h5>", unsafe_allow_html=True)
                
                # Get suppliers from database
                try:
                    suppliers_query = session.query(Product.primary_supplier).distinct().filter(Product.primary_supplier != None)
                    suppliers = [s[0] for s in suppliers_query if s[0]]  # Filter out None values
                    suppliers.sort()
                except Exception as e:
                    st.warning(f"Could not load suppliers: {str(e)}")
                    suppliers = []  # Fallback
                
                # Supplier selection
                selected_suppliers = st.multiselect(
                    "Suppliers",  # This becomes placeholder text
                    options=suppliers,
                    default=None,
                    key="sales_suppliers",
                    label_visibility="collapsed",
                    placeholder="Suppliers"  # Placeholder text when no options are selected
                )
                
                # Get list of brands
                try:
                    brands_query = session.query(Product.brand).distinct().order_by(Product.brand)
                    brands = [b[0] for b in brands_query.all() if b[0]]
                except Exception as e:
                    st.warning(f"Could not load brands: {str(e)}")
                    brands = []
                
                # Brand selection (multi-select)
                selected_brands = st.multiselect(
                    "Brands",  # This becomes placeholder text
                    options=brands,
                    default=st.session_state.sales_filter_defaults['brands'],
                    key="sales_brands",
                    label_visibility="collapsed",
                    placeholder="Brands"  # Placeholder text when no options are selected
                )
                
                # Get list of product groups
                try:
                    product_groups_query = session.query(Product.product_group).distinct().order_by(Product.product_group)
                    product_groups = [pg[0] for pg in product_groups_query.all() if pg[0]]
                except Exception as e:
                    st.warning(f"Could not load product groups: {str(e)}")
                    product_groups = []
                
                # Product group selection (multi-select)
                selected_product_groups = st.multiselect(
                    "Product Groups",  # This becomes placeholder text
                    options=product_groups,
                    default=st.session_state.sales_filter_defaults['product_groups'],
                    key="sales_product_groups",
                    label_visibility="collapsed",
                    placeholder="Product Groups"  # Placeholder text when no options are selected
                )
                
                # Get list of web categories
                try:
                    from models import ProductCategory, product_category_association
                    web_categories_query = session.query(ProductCategory.category_name).distinct().order_by(ProductCategory.category_name)
                    web_categories = [wc[0] for wc in web_categories_query.all() if wc[0]]
                except Exception as e:
                    st.warning(f"Could not load web categories: {str(e)}")
                    web_categories = []
                
                # Web categories selection (multi-select)
                selected_web_categories = st.multiselect(
                    "Web Categories",
                    options=web_categories,
                    key="sales_web_categories",
                    label_visibility="collapsed",
                    placeholder="Web Categories"
                )
                
                # Product filter (text input for product code search)
                product_filter = st.text_input(
                    "Product Filter",
                    value="",
                    key="sales_product_filter",
                    label_visibility="collapsed",
                    placeholder="Search by product code..."
                )
                
                # Filter actions buttons
                st.write("")  # Add some spacing

        
        # Determine date range based on selected filters
        start_date = None
        end_date = None
        comparison_start_date = None
        comparison_end_date = None
        
        # Process selected years and quarters to get date ranges
        if selected_years and selected_quarters:
            # Process each year and quarter combination
            date_ranges = []
            for year in selected_years:
                for quarter in selected_quarters:
                    # Initialize period start and end dates
                    q_start = None
                    q_end = None
                    
                    if quarter == "Q1":
                        q_start = datetime(year, 1, 1).date()
                        q_end = datetime(year, 3, 31).date()
                    elif quarter == "Q2":
                        q_start = datetime(year, 4, 1).date()
                        q_end = datetime(year, 6, 30).date()
                    elif quarter == "Q3":
                        q_start = datetime(year, 7, 1).date()
                        q_end = datetime(year, 9, 30).date()
                    elif quarter == "Q4":
                        q_start = datetime(year, 10, 1).date()
                        q_end = datetime(year, 12, 31).date()
                    elif quarter == "YTD":
                        # Year-to-date means from Jan 1 to the current date
                        # For non-current years, use the same day/month but in that year
                        q_start = datetime(year, 1, 1).date()
                        if year == current_year:
                            # For current year, use today's date
                            q_end = today
                        else:
                            # For past years, use today's month/day in that year (or Dec 31 if that date doesn't exist)
                            try:
                                # Try to use the same month/day in the selected year
                                q_end = datetime(year, today.month, today.day).date()
                            except ValueError:
                                # Handle February 29 edge case
                                if today.month == 2 and today.day == 29:
                                    q_end = datetime(year, 2, 28).date()
                                else:
                                    # For other edge cases, use the last day of the year
                                    q_end = datetime(year, 12, 31).date()
                    elif quarter == "Full Year":
                        # If no quarter is selected, use Jan 1 to Dec 31 for the full year
                        q_start = datetime(year, 1, 1).date()
                        q_end = datetime(year, 12, 31).date()
                    elif quarter == "Custom":
                        # Custom period uses the date range selector
                        if isinstance(st.session_state.custom_date_range, tuple) and len(st.session_state.custom_date_range) == 2:
                            q_start, q_end = st.session_state.custom_date_range
                    
                    # Add to date ranges if both start and end are valid
                    if q_start and q_end:
                        date_ranges.append((q_start, q_end))
            
            # If we have date ranges, find the overall min and max
            if date_ranges:
                # Get the earliest start date and latest end date across all selections
                start_date = min([dr[0] for dr in date_ranges])
                end_date = max([dr[1] for dr in date_ranges])
                
                # Set comparison period to be exactly 1 year before
                comparison_start_date = datetime(start_date.year - 1, start_date.month, start_date.day).date()
                comparison_end_date = datetime(end_date.year - 1, end_date.month, end_date.day).date()
        
        # If no valid dates were determined, use defaults
        if not start_date or not end_date:
            start_date = default_start_date
            end_date = default_end_date
            comparison_start_date = default_comparison_start_date
            comparison_end_date = default_comparison_end_date
        
        # Store the date range for future reference
        st.session_state.sales_filter_defaults['date_range'] = (start_date, end_date)
        

        
        # Common function to query sales data with filters
        def get_sales_data(start_date, end_date, countries=None, product_groups=None, brands=None, 
                            buyer_categories=None, buyers=None, suppliers=None, product_filter=None, web_categories=None):
            sales_query = session.query(
                Sale.sale_date,
                func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                func.sum(Sale.quantity).label('units_sold'),
                func.count(Sale.id).label('transaction_count')
            ).filter(
                Sale.sale_date.between(start_date, end_date)
            ).group_by(
                Sale.sale_date
            ).order_by(
                Sale.sale_date
            )
            
            # Apply buyer filters if needed
            if countries or buyer_categories or buyers:
                sales_query = sales_query.join(Buyer, Sale.buyer_id == Buyer.buyer_id)
                
                if countries:
                    sales_query = sales_query.filter(Buyer.country.in_(countries))
                
                if buyer_categories:
                    sales_query = sales_query.filter(Buyer.buyer_category.in_(buyer_categories))
                    
                if buyers:
                    sales_query = sales_query.filter(Buyer.buyer_id.in_(buyers))
            
            # Apply product filters if needed
            if product_groups or brands or suppliers or product_filter or web_categories:
                sales_query = sales_query.join(Product, Sale.product_id == Product.product_id)
                
                if product_groups:
                    sales_query = sales_query.filter(Product.product_group.in_(product_groups))
                
                if brands:
                    sales_query = sales_query.filter(Product.brand.in_(brands))
                    
                if suppliers:
                    sales_query = sales_query.filter(Product.primary_supplier.in_(suppliers))
                    
                if product_filter:
                    sales_query = sales_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                
                # Apply web categories filter using many-to-many relationship
                if web_categories:
                    from models import ProductCategory, product_category_association
                    sales_query = sales_query.join(
                        product_category_association, 
                        Product.product_id == product_category_association.c.product_id
                    ).join(
                        ProductCategory, 
                        product_category_association.c.category_id == ProductCategory.id
                    ).filter(ProductCategory.category_name.in_(web_categories))
            
            return sales_query.all()
        
        # Common function to calculate aggregate sales metrics for a period
        def calculate_sales_metrics(results):
            # Check if we have results
            if not results:
                return {
                    'total_sales_value': 0,
                    'total_units_sold': 0,
                    'transaction_count': 0,
                    'average_order_value': 0
                }
            
            # Calculate total sales
            total_sales_value = sum(r.sales_value for r in results)
            total_units_sold = sum(r.units_sold for r in results)
            transaction_count = sum(r.transaction_count for r in results)
            
            # Calculate average order value
            average_order_value = total_sales_value / transaction_count if transaction_count > 0 else 0
            
            # Format numbers for display using Hungarian formatting (space as thousand separator)
            def format_number(value, format_str="{:,.0f} Ft"):
                """Format a number with thousands separator and optional currency."""
                formatted = format_str.format(value)
                # Replace commas with spaces for Hungarian formatting
                return formatted.replace(",", " ")
            
            # Calculate the growth percentage
            def calculate_growth(current, previous):
                """Calculate the growth percentage between two values."""
                if previous and previous > 0:
                    return (current / previous - 1) * 100
                return 0
            
            # Display a metric with growth indicator
            def display_metric_with_growth(label, value, comparison_value=None, format_str="{:,.0f} Ft", 
                                        percentage=False):
                """Display a metric with growth indicator."""
                # Format the value
                if percentage:
                    formatted_value = f"{value:.1f}%"
                else:
                    formatted_value = format_number(value, format_str)
                
                # Calculate and format the growth
                if comparison_value is not None and comparison_value > 0:
                    growth = calculate_growth(value, comparison_value)
                    growth_color = "green" if growth >= 0 else "red"
                    delta = f"{growth:+.1f}%"
                else:
                    delta = None
                
                # Display the metric
                st.metric(
                    label=label,
                    value=formatted_value,
                    delta=delta
                )
            
            return {
                'total_sales_value': total_sales_value,
                'total_units_sold': total_units_sold,
                'transaction_count': transaction_count,
                'average_order_value': average_order_value
            }
        
        # SECTION 1: Sales Performance Overview - KPIs
        st.markdown("## Sales Performance Overview", unsafe_allow_html=True)
        
        # Common function to get profit data
        def get_profit_data(start_date, end_date, countries=None, product_groups=None, brands=None, 
                           buyer_categories=None, buyers=None, suppliers=None, product_filter=None, web_categories=None):
            """Get profit data for a period."""
            profit_query = session.query(
                func.sum((Sale.unit_price - Sale.purchase_unit_price) * Sale.quantity).label('total_profit')
            ).filter(
                Sale.sale_date.between(start_date, end_date)
            )
            
            # Apply buyer filters if needed
            if countries or buyer_categories or buyers:
                profit_query = profit_query.join(Buyer, Sale.buyer_id == Buyer.buyer_id)
                
                if countries:
                    profit_query = profit_query.filter(Buyer.country.in_(countries))
                
                if buyer_categories:
                    profit_query = profit_query.filter(Buyer.buyer_category.in_(buyer_categories))
                    
                if buyers:
                    profit_query = profit_query.filter(Buyer.buyer_id.in_(buyers))
            
            # Apply product filters if needed
            if product_groups or brands or suppliers or product_filter or web_categories:
                profit_query = profit_query.join(Product, Sale.product_id == Product.product_id)
                
                if product_groups:
                    profit_query = profit_query.filter(Product.product_group.in_(product_groups))
                
                if brands:
                    profit_query = profit_query.filter(Product.brand.in_(brands))
                    
                if suppliers:
                    profit_query = profit_query.filter(Product.primary_supplier.in_(suppliers))
                    
                if product_filter:
                    profit_query = profit_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                
                # Apply web categories filter using many-to-many relationship
                if web_categories:
                    from models import ProductCategory, product_category_association
                    profit_query = profit_query.join(
                        product_category_association, 
                        Product.product_id == product_category_association.c.product_id
                    ).join(
                        ProductCategory, 
                        product_category_association.c.category_id == ProductCategory.id
                    ).filter(ProductCategory.category_name.in_(web_categories))
            
            result = profit_query.first()
            return result.total_profit if result and result.total_profit else 0
        
        # Get sales data for the current period
        current_period_results = get_sales_data(
            start_date, 
            end_date, 
            selected_countries, 
            selected_product_groups, 
            selected_brands, 
            selected_buyer_categories, 
            selected_buyers,
            selected_suppliers,
            product_filter,
            selected_web_categories
        )
        
        # Get sales data for the comparison period
        comparison_period_results = get_sales_data(
            comparison_start_date, 
            comparison_end_date, 
            selected_countries, 
            selected_product_groups, 
            selected_brands, 
            selected_buyer_categories, 
            selected_buyers,
            selected_suppliers,
            product_filter,
            selected_web_categories
        )
        
        # Calculate metrics for both periods
        current_metrics = calculate_sales_metrics(current_period_results)
        comparison_metrics = calculate_sales_metrics(comparison_period_results)
        
        # Get profit data for both periods
        current_profit = get_profit_data(
            start_date, 
            end_date, 
            selected_countries, 
            selected_product_groups, 
            selected_brands, 
            selected_buyer_categories, 
            selected_buyers,
            selected_suppliers,
            product_filter,
            selected_web_categories
        )
        
        comparison_profit = get_profit_data(
            comparison_start_date, 
            comparison_end_date, 
            selected_countries, 
            selected_product_groups, 
            selected_brands, 
            selected_buyer_categories, 
            selected_buyers,
            selected_suppliers,
            product_filter,
            selected_web_categories
        )
        
        # Display KPIs in a row
        kpi_cols = st.columns(6)
        
        # Revenue KPI
        with kpi_cols[0]:
            st.metric(
                label="Revenue",
                value=format_large_number(current_metrics['total_sales_value']),
                delta=f"{((current_metrics['total_sales_value'] / comparison_metrics['total_sales_value']) - 1) * 100:.1f}%" 
                      if comparison_metrics['total_sales_value'] > 0 else None
            )
        
        # Profit KPI
        with kpi_cols[1]:
            st.metric(
                label="Profit",
                value=format_large_number(current_profit),
                delta=f"{((current_profit / comparison_profit) - 1) * 100:.1f}%" 
                      if comparison_profit > 0 else None
            )
        
        # Units Sold KPI
        with kpi_cols[2]:
            st.metric(
                label="Units Sold",
                value=format_large_number(current_metrics['total_units_sold']),
                delta=f"{((current_metrics['total_units_sold'] / comparison_metrics['total_units_sold']) - 1) * 100:.1f}%" 
                      if comparison_metrics['total_units_sold'] > 0 else None
            )
        
        # Transaction KPI
        with kpi_cols[3]:
            st.metric(
                label="Transactions",
                value=format_large_number(current_metrics['transaction_count']),
                delta=f"{((current_metrics['transaction_count'] / comparison_metrics['transaction_count']) - 1) * 100:.1f}%" 
                      if comparison_metrics['transaction_count'] > 0 else None
            )
        
        # Average Order Value KPI
        with kpi_cols[4]:
            st.metric(
                label="Avg. Order Value",
                value=format_large_number(current_metrics['average_order_value']),
                delta=f"{((current_metrics['average_order_value'] / comparison_metrics['average_order_value']) - 1) * 100:.1f}%" 
                      if comparison_metrics['average_order_value'] > 0 else None
            )
        
        # Margin KPI
        with kpi_cols[5]:
            current_margin = (current_profit / current_metrics['total_sales_value'] * 100 
                             if current_metrics['total_sales_value'] > 0 else 0)
            comparison_margin = (comparison_profit / comparison_metrics['total_sales_value'] * 100 
                                if comparison_metrics['total_sales_value'] > 0 else 0)
            st.metric(
                label="Margin",
                value=f"{current_margin:.1f}%",
                delta=f"{current_margin - comparison_margin:.1f}pp" 
                      if comparison_margin > 0 else None
            )
        
        # SECTION 2: Sales Trend Over Time
        st.markdown("## Sales Trend Over Time", unsafe_allow_html=True)
        
        # Add custom CSS for pill-style buttons
        st.markdown("""
        <style>
        div.row-widget.stRadio > div {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label {
            background-color: #f0f2f6;
            border: 1px solid #ddd;
            padding: 6px 16px;
            border-radius: 20px;
            margin: 0 6px;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label:hover {
            background-color: #e0e2e6;
            border-color: #ccc;
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label[aria-checked="true"] {
            background-color: #0068C9;
            border-color: #0068C9;
            color: white;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        div.row-widget.stRadio > div[role="radiogroup"] > label[data-baseweb="radio"] > div:first-child {
            display: none;
        }
        </style>
        """, unsafe_allow_html=True)
        
        # Time selector above the chart
        time_options = ["Daily", "Weekly", "Monthly"]
        time_option = st.radio(
            label="",
            options=time_options,
            index=2,  # Monthly (last option)
            horizontal=True,
            label_visibility="collapsed"
        )
        
        # Use the existing working sales overview function but just for the chart part
        try:
            
            # Base query for primary period - exactly like the working version
            base_query = session.query(
                func.date(Sale.sale_date).label('date'),
                func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                func.sum(Sale.quantity).label('units_sold'),
                func.count(Sale.id.distinct()).label('transaction_count'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
            ).join(Buyer).join(Product)
            
            # Apply date filter for primary period
            base_query = base_query.filter(and_(
                Sale.sale_date >= start_date,
                Sale.sale_date <= end_date
            ))
            
            # Apply other filters exactly like the working version
            if selected_countries:
                base_query = base_query.filter(Buyer.country.in_(selected_countries))
            if selected_product_groups:
                base_query = base_query.filter(Product.product_group.in_(selected_product_groups))
            if selected_brands:
                base_query = base_query.filter(Product.brand.in_(selected_brands))
            if selected_buyer_categories:
                base_query = base_query.filter(Buyer.buyer_category.in_(selected_buyer_categories))
            if selected_buyers:
                base_query = base_query.filter(Buyer.buyer_name.in_(selected_buyers))
            if selected_suppliers:
                base_query = base_query.filter(Product.primary_supplier.in_(selected_suppliers))
            if product_filter:
                base_query = base_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
            
            # Group by date and execute query
            primary_sales = base_query.group_by(func.date(Sale.sale_date)).all()
            
            # Similar queries for comparison period if dates are provided
            comparison_sales = []
            if comparison_start_date and comparison_end_date:
                comp_query = session.query(
                    func.date(Sale.sale_date).label('date'),
                    func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                    func.sum(Sale.quantity).label('units_sold'),
                    func.count(Sale.id.distinct()).label('transaction_count'),
                    func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
                ).join(Buyer).join(Product)
                
                # Apply date filter for comparison period
                comp_query = comp_query.filter(and_(
                    Sale.sale_date >= comparison_start_date,
                    Sale.sale_date <= comparison_end_date
                ))
                
                # Apply other filters
                if selected_countries:
                    comp_query = comp_query.filter(Buyer.country.in_(selected_countries))
                if selected_product_groups:
                    comp_query = comp_query.filter(Product.product_group.in_(selected_product_groups))
                if selected_brands:
                    comp_query = comp_query.filter(Product.brand.in_(selected_brands))
                if selected_buyer_categories:
                    comp_query = comp_query.filter(Buyer.buyer_category.in_(selected_buyer_categories))
                if selected_buyers:
                    comp_query = comp_query.filter(Buyer.buyer_name.in_(selected_buyers))
                if selected_suppliers:
                    comp_query = comp_query.filter(Product.primary_supplier.in_(selected_suppliers))
                if product_filter:
                    comp_query = comp_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                
                # Group by date and execute query
                comparison_sales = comp_query.group_by(func.date(Sale.sale_date)).all()
            
            # Check if we have any data to display
            if not primary_sales:
                st.info("No sales data available for the selected time period.")
            else:
                # Prepare data for the chart exactly like the working version
                current_df = pd.DataFrame(primary_sales, columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
                
                # Prepare comparison data if available
                if comparison_sales:
                    comparison_df = pd.DataFrame(comparison_sales, columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
                else:
                    comparison_df = pd.DataFrame(columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
                
                # Ensure date columns are datetime objects with error handling (exactly like working version)
                try:
                    current_df['date'] = pd.to_datetime(current_df['date'])
                    if not comparison_df.empty:
                        comparison_df['date'] = pd.to_datetime(comparison_df['date'])
                except Exception as e:
                    st.warning(f"Date conversion issue: {str(e)}. Some dates may not display correctly.")
                
                # Aggregate data based on selected time period BEFORE creating the chart
                if time_option.lower() == "weekly":
                    # Group by week
                    current_df['week'] = current_df['date'].dt.isocalendar().week
                    current_df['year'] = current_df['date'].dt.isocalendar().year
                    current_df['week_start'] = current_df['date'] - pd.to_timedelta(current_df['date'].dt.dayofweek, unit='D')
                    
                    aggregated_current = current_df.groupby('week_start').agg({
                        'sales_value': 'sum',
                        'units_sold': 'sum',
                        'transaction_count': 'sum',
                        'profit': 'sum'
                    }).reset_index()
                    aggregated_current['date'] = aggregated_current['week_start']
                    
                    if not comparison_df.empty:
                        comparison_df['week'] = comparison_df['date'].dt.isocalendar().week
                        comparison_df['year'] = comparison_df['date'].dt.isocalendar().year
                        comparison_df['week_start'] = comparison_df['date'] - pd.to_timedelta(comparison_df['date'].dt.dayofweek, unit='D')
                        
                        aggregated_comparison = comparison_df.groupby('week_start').agg({
                            'sales_value': 'sum',
                            'units_sold': 'sum',
                            'transaction_count': 'sum',
                            'profit': 'sum'
                        }).reset_index()
                        aggregated_comparison['date'] = aggregated_comparison['week_start']
                    else:
                        aggregated_comparison = pd.DataFrame(columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
                    
                elif time_option.lower() == "monthly":
                    # Group by month
                    current_df['month_start'] = current_df['date'].dt.to_period('M').dt.start_time
                    
                    aggregated_current = current_df.groupby('month_start').agg({
                        'sales_value': 'sum',
                        'units_sold': 'sum',
                        'transaction_count': 'sum',
                        'profit': 'sum'
                    }).reset_index()
                    aggregated_current['date'] = aggregated_current['month_start']
                    
                    if not comparison_df.empty:
                        comparison_df['month_start'] = comparison_df['date'].dt.to_period('M').dt.start_time
                        
                        aggregated_comparison = comparison_df.groupby('month_start').agg({
                            'sales_value': 'sum',
                            'units_sold': 'sum',
                            'transaction_count': 'sum',
                            'profit': 'sum'
                        }).reset_index()
                        aggregated_comparison['date'] = aggregated_comparison['month_start']
                    else:
                        aggregated_comparison = pd.DataFrame(columns=['date', 'sales_value', 'units_sold', 'transaction_count', 'profit'])
                        
                else:  # daily - use original data
                    aggregated_current = current_df.copy()
                    aggregated_comparison = comparison_df.copy()
                
                # Create the chart using the aggregated data
                sales_trend_fig = create_sales_trend_chart(
                    primary_df=aggregated_current, 
                    comparison_df=aggregated_comparison, 
                    time_view=time_option.lower()
                )
                
                st.plotly_chart(sales_trend_fig, use_container_width=True)
                
                # Add a note about the aggregation - exactly like the working version
                if time_option == "Daily":
                    st.caption("Showing daily sales values")
                elif time_option == "Weekly":
                    st.caption("Data aggregated by week - each point represents a week's total")
                else:  # Monthly
                    st.caption("Data aggregated by month - each point represents a month's total")
            
        except Exception as e:
            # Rollback the transaction to recover from the error
            if session:
                session.rollback()
            st.error(f"Error generating sales trend chart: {str(e)}")
            st.info("Please check your data and filter selections.")
        
        # SECTION 3: Customer Categories Performance
        
        # Create function to get category data
        def get_sales_by_subcategory(start_date, end_date, countries=None, product_groups=None, 
                                    brands=None, buyer_categories=None, buyers=None, suppliers=None, product_filter=None, web_categories=None):
            """Get sales data grouped by buyer category and subcategory."""
            # Check if session is valid
            if session is None:
                st.error("Database session is not initialized. Please check your database connection.")
                return None, None
            
            try:
                # Main Category Query - Get sales by main buyer category
                main_query = session.query(
                    Buyer.buyer_category.label('category'),
                    func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                    func.sum((Sale.unit_price - Sale.purchase_unit_price) * Sale.quantity).label('profit')
                ).join(
                    Buyer, Sale.buyer_id == Buyer.buyer_id
                ).filter(
                    Sale.sale_date.between(start_date, end_date)
                ).group_by(
                    Buyer.buyer_category
                ).order_by(
                    desc('sales_value')
                )
                
                # Apply filters to main query
                if countries:
                    main_query = main_query.filter(Buyer.country.in_(countries))
                
                if buyer_categories:
                    main_query = main_query.filter(Buyer.buyer_category.in_(buyer_categories))
                
                if buyers:
                    main_query = main_query.filter(Buyer.buyer_id.in_(buyers))
                
                # Apply product filters if provided
                if product_groups or brands or suppliers or product_filter or web_categories:
                    main_query = main_query.join(Product, Sale.product_id == Product.product_id)
                    
                    if product_groups:
                        main_query = main_query.filter(Product.product_group.in_(product_groups))
                    
                    if brands:
                        main_query = main_query.filter(Product.brand.in_(brands))
                        
                    if suppliers:
                        main_query = main_query.filter(Product.primary_supplier.in_(suppliers))
                        
                    if product_filter:
                        main_query = main_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                    
                    # Apply web categories filter using many-to-many relationship
                    if web_categories:
                        from models import ProductCategory, product_category_association
                        main_query = main_query.join(
                            product_category_association, 
                            Product.product_id == product_category_association.c.product_id
                        ).join(
                            ProductCategory, 
                            product_category_association.c.category_id == ProductCategory.id
                        ).filter(ProductCategory.category_name.in_(web_categories))
                
                # Execute main query
                main_results = main_query.all()
                
                # Create main DataFrame
                main_df = pd.DataFrame([
                    {
                        'category': r.category,
                        'sales_value': float(r.sales_value) if r.sales_value else 0,
                        'profit': float(r.profit) if r.profit else 0
                    }
                    for r in main_results if r.category  # Filter out None categories
                ])
                
                # Get subcategory data (buyer category level, not individual buyer names)
                # This should return the actual buyer categories that make up each main category
                sub_query = session.query(
                    Buyer.buyer_category.label('subcategory'),
                    func.sum(Sale.quantity * Sale.unit_price).label('sales_value')
                ).join(
                    Buyer, Sale.buyer_id == Buyer.buyer_id
                ).filter(
                    Sale.sale_date.between(start_date, end_date)
                ).group_by(
                    Buyer.buyer_category
                ).order_by(
                    desc('sales_value')
                )
                
                # Apply filters to subcategory query (same as main query)
                if countries:
                    sub_query = sub_query.filter(Buyer.country.in_(countries))
                
                if buyer_categories:
                    sub_query = sub_query.filter(Buyer.buyer_category.in_(buyer_categories))
                
                if buyers:
                    sub_query = sub_query.filter(Buyer.buyer_id.in_(buyers))
                
                # Apply product filters if provided
                if product_groups or brands or suppliers or product_filter or web_categories:
                    sub_query = sub_query.join(Product, Sale.product_id == Product.product_id)
                    
                    if product_groups:
                        sub_query = sub_query.filter(Product.product_group.in_(product_groups))
                    
                    if brands:
                        sub_query = sub_query.filter(Product.brand.in_(brands))
                        
                    if suppliers:
                        sub_query = sub_query.filter(Product.primary_supplier.in_(suppliers))
                        
                    if product_filter:
                        sub_query = sub_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                    
                    # Apply web categories filter using many-to-many relationship
                    if web_categories:
                        from models import ProductCategory, product_category_association
                        sub_query = sub_query.join(
                            product_category_association, 
                            Product.product_id == product_category_association.c.product_id
                        ).join(
                            ProductCategory, 
                            product_category_association.c.category_id == ProductCategory.id
                        ).filter(ProductCategory.category_name.in_(web_categories))
                
                # Execute subcategory query
                sub_results = sub_query.all()
                
                # Create subcategory DataFrame
                sub_df = pd.DataFrame([
                    {
                        'subcategory': r.subcategory,
                        'sales_value': float(r.sales_value) if r.sales_value else 0
                    }
                    for r in sub_results if r.subcategory  # Filter out None values
                ])
                
                return main_df, sub_df
                
            except Exception as e:
                st.error(f"Error getting sales by subcategory: {str(e)}")
                return None, None
        
        # Get current period data (main and subcategory)
        current_main_df, current_sub_df = get_sales_by_subcategory(
            start_date, end_date, 
            selected_countries, selected_product_groups, 
            selected_brands, selected_buyer_categories,
            selected_buyers, selected_suppliers, product_filter,
            selected_web_categories
        )
        
        # Get comparison period data (main and subcategory)
        comparison_main_df, comparison_sub_df = None, None
        if comparison_start_date and comparison_end_date:
            comparison_main_df, comparison_sub_df = get_sales_by_subcategory(
                comparison_start_date, comparison_end_date,
                selected_countries, selected_product_groups,
                selected_brands, selected_buyer_categories,
                selected_buyers, selected_suppliers, product_filter,
                selected_web_categories
            )
        
        # Prepare data for customer categories component
        if current_main_df is not None and not current_main_df.empty:
            # Define the main categories we want to show
            main_categories = ['Kisker', 'Nagyker', 'Install', 'Rental', 'Export', 'Egyéb']
            
            # Define mapping of sub-categories to main categories
            buyer_to_main_map = {
                'Együttműködő partner': 'Egyéb',
                'Zenetanár': 'Egyéb',
                'Webes vásárló': 'Kisker',
                'Magánszemély': 'Kisker',
                'Rendszer-Viszonteladó': 'Install',
                'Dolgozók': 'Egyéb',
                'Kiskereskedelmi egység': 'Nagyker',
                'Export': 'Export',
                'Szerződés nélküli partner': 'Install',
                'Vegyes kereskedelem': 'Install',
                'Wholesale': 'Nagyker',
                'Rental partner': 'Rental',
                'Beszállító': 'Egyéb',
                'Installációs partner': 'Install',
                'Bolti vásárló': 'Kisker'
            }
            
            # Initialize dictionaries for main categories
            main_category_totals = {}
            main_category_profits = {}
            main_category_comparison = {}
            main_category_profit_comparison = {}
            
            # Initialize with zeros
            for main_cat in main_categories:
                main_category_totals[main_cat] = 0
                main_category_profits[main_cat] = 0
                main_category_comparison[main_cat] = 0
                main_category_profit_comparison[main_cat] = 0
            
            # Populate main category totals from our data
            for _, row in current_main_df.iterrows():
                category = row['category']
                # Map to main category if needed
                main_cat = buyer_to_main_map.get(category, category)
                if main_cat in main_category_totals:
                    main_category_totals[main_cat] += row['sales_value']
                    main_category_profits[main_cat] += row['profit']
            
            # Populate comparison data if available
            if comparison_main_df is not None and not comparison_main_df.empty:
                for _, row in comparison_main_df.iterrows():
                    category = row['category']
                    # Map to main category if needed
                    main_cat = buyer_to_main_map.get(category, category)
                    if main_cat in main_category_comparison:
                        main_category_comparison[main_cat] += row['sales_value']
                        main_category_profit_comparison[main_cat] += row['profit']
            
            # Create subcategory lookups with proper nested structure
            current_sub_lookup = {}
            comparison_sub_lookup = {}
            
            # Populate subcategory data with nested structure: main_category -> subcategory -> value
            # Now subcategory is the actual buyer category (like 'Webes vásárló', 'Wholesale', etc.)
            if current_sub_df is not None and not current_sub_df.empty:
                for _, row in current_sub_df.iterrows():
                    buyer_category = row['subcategory']  # This is the actual buyer category from database
                    
                    # Map buyer category to its main category
                    main_cat = buyer_to_main_map.get(buyer_category, 'Egyéb')
                    
                    if main_cat not in current_sub_lookup:
                        current_sub_lookup[main_cat] = {}
                    current_sub_lookup[main_cat][buyer_category] = row['sales_value']
            
            # Populate comparison subcategory data with nested structure
            if comparison_sub_df is not None and not comparison_sub_df.empty:
                for _, row in comparison_sub_df.iterrows():
                    buyer_category = row['subcategory']  # This is the actual buyer category from database
                    
                    # Map buyer category to its main category
                    main_cat = buyer_to_main_map.get(buyer_category, 'Egyéb')
                    
                    if main_cat not in comparison_sub_lookup:
                        comparison_sub_lookup[main_cat] = {}
                    comparison_sub_lookup[main_cat][buyer_category] = row['sales_value']
            
            # Get top buyers data for both current and comparison periods
            current_buyer_data = pd.DataFrame(columns=['buyer_name', 'sales_value', 'profit'])
            comparison_buyer_data = pd.DataFrame(columns=['buyer_name', 'sales_value', 'profit'])
            
            # Query current period buyer data
            try:
                current_buyer_query = session.query(
                    Buyer.buyer_name,
                    func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                    func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
                ).join(Sale).join(Product)
                
                # Apply date filter for current period
                current_buyer_query = current_buyer_query.filter(and_(
                    Sale.sale_date >= start_date,
                    Sale.sale_date <= end_date
                ))
                
                # Apply the same filters as the main analysis
                if selected_countries:
                    current_buyer_query = current_buyer_query.filter(Buyer.country.in_(selected_countries))
                if selected_product_groups:
                    current_buyer_query = current_buyer_query.filter(Product.product_group.in_(selected_product_groups))
                if selected_brands:
                    current_buyer_query = current_buyer_query.filter(Product.brand.in_(selected_brands))
                if selected_buyer_categories:
                    current_buyer_query = current_buyer_query.filter(Buyer.buyer_category.in_(selected_buyer_categories))
                if selected_buyers:
                    current_buyer_query = current_buyer_query.filter(Buyer.buyer_name.in_(selected_buyers))
                if selected_suppliers:
                    current_buyer_query = current_buyer_query.filter(Product.primary_supplier.in_(selected_suppliers))
                if product_filter:
                    current_buyer_query = current_buyer_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                
                # Apply web categories filter using many-to-many relationship
                if selected_web_categories:
                    from models import ProductCategory, product_category_association
                    current_buyer_query = current_buyer_query.join(
                        product_category_association, 
                        Product.product_id == product_category_association.c.product_id
                    ).join(
                        ProductCategory, 
                        product_category_association.c.category_id == ProductCategory.id
                    ).filter(ProductCategory.category_name.in_(selected_web_categories))
                
                # Group by buyer and execute
                current_buyer_results = current_buyer_query.group_by(Buyer.buyer_name).all()
                
                # Convert to DataFrame format expected by the component
                current_buyer_data = pd.DataFrame([
                    {
                        'buyer_name': row.buyer_name,
                        'sales_value': float(row.sales_value or 0),
                        'profit': float(row.profit or 0)
                    }
                    for row in current_buyer_results
                ])
                    
            except Exception as e:
                st.warning(f"Error loading current buyer data: {str(e)}")
            
            # Query comparison period buyer data if comparison dates exist
            if comparison_start_date and comparison_end_date:
                try:
                    comparison_buyer_query = session.query(
                        Buyer.buyer_name,
                        func.sum(Sale.quantity * Sale.unit_price).label('sales_value'),
                        func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('profit')
                    ).join(Sale).join(Product)
                    
                    # Apply date filter for comparison period
                    comparison_buyer_query = comparison_buyer_query.filter(and_(
                        Sale.sale_date >= comparison_start_date,
                        Sale.sale_date <= comparison_end_date
                    ))
                    
                    # Apply the same filters
                    if selected_countries:
                        comparison_buyer_query = comparison_buyer_query.filter(Buyer.country.in_(selected_countries))
                    if selected_product_groups:
                        comparison_buyer_query = comparison_buyer_query.filter(Product.product_group.in_(selected_product_groups))
                    if selected_brands:
                        comparison_buyer_query = comparison_buyer_query.filter(Product.brand.in_(selected_brands))
                    if selected_buyer_categories:
                        comparison_buyer_query = comparison_buyer_query.filter(Buyer.buyer_category.in_(selected_buyer_categories))
                    if selected_buyers:
                        comparison_buyer_query = comparison_buyer_query.filter(Buyer.buyer_name.in_(selected_buyers))
                    if selected_suppliers:
                        comparison_buyer_query = comparison_buyer_query.filter(Product.primary_supplier.in_(selected_suppliers))
                    if product_filter:
                        comparison_buyer_query = comparison_buyer_query.filter(Product.product_id.ilike(f'%{product_filter}%'))
                    
                    # Apply web categories filter using many-to-many relationship
                    if selected_web_categories:
                        from models import ProductCategory, product_category_association
                        comparison_buyer_query = comparison_buyer_query.join(
                            product_category_association, 
                            Product.product_id == product_category_association.c.product_id
                        ).join(
                            ProductCategory, 
                            product_category_association.c.category_id == ProductCategory.id
                        ).filter(ProductCategory.category_name.in_(selected_web_categories))
                    
                    # Group by buyer and execute
                    comparison_buyer_results = comparison_buyer_query.group_by(Buyer.buyer_name).all()
                    
                    # Convert to DataFrame format expected by the component
                    comparison_buyer_data = pd.DataFrame([
                        {
                            'buyer_name': row.buyer_name,
                            'sales_value': float(row.sales_value or 0),
                            'profit': float(row.profit or 0)
                        }
                        for row in comparison_buyer_results
                    ])
                        
                except Exception as e:
                    st.warning(f"Error loading comparison buyer data: {str(e)}")
            
            # The display_order parameter needs to be a list of tuples (subcategory, value) for each entry
            # Create a list of tuples with (main_category, revenue) for proper sorting
            display_order = [(cat, main_category_totals.get(cat, 0)) 
                             for cat in sorted(main_categories, 
                                              key=lambda x: main_category_totals.get(x, 0), 
                                              reverse=True)]
            

            
            # Render customer categories using the component
            render_customer_performance(
                main_category_totals, 
                main_category_profits,
                main_category_comparison, 
                main_category_profit_comparison,
                current_sub_lookup, 
                comparison_sub_lookup,
                buyer_to_main_map, 
                display_order,
                current_buyer_data,
                comparison_buyer_data
            )
        else:
            st.info("No customer category data available for the selected filters.")
        

        
        # SECTION 5: Product Portfolio Analysis
        st.markdown("## Product Portfolio Analysis", unsafe_allow_html=True)
        
        # Import and use the product portfolio analysis module (exactly like Sales Analytics page)
        from components.product_portfolio_analysis import render_portfolio_analysis
        
        # Call the portfolio analysis function with all the filter parameters
        render_portfolio_analysis(
            session=session,
            start_date=start_date,
            end_date=end_date,
            comparison_start_date=comparison_start_date,
            comparison_end_date=comparison_end_date,
            countries=selected_countries,
            product_groups=selected_product_groups,
            brands=selected_brands,
            buyer_categories=selected_buyer_categories,
            buyer_names=selected_buyers,
            suppliers=selected_suppliers,
            product_filter=product_filter,
            web_categories=selected_web_categories
        )
    
    except Exception as e:
        st.error(f"An error occurred in the unified sales analytics: {str(e)}")
        import traceback
        st.exception(e)
    finally:
        # Close the session if it was opened
        if session:
            db.close_session(session)