# ERP Analytics Dashboard

## Overview

The ERP Analytics Dashboard is a comprehensive web-based business intelligence tool built with Streamlit. It provides interactive visualizations and deep insights into enterprise resource planning data, helping businesses monitor sales performance, inventory management, purchase analytics, and customer behavior.

![ERP Analytics Dashboard](assets/placeholder_image.svg)

## Key Features

### Sales Analytics
- Interactive time period filtering with year, period, and date range selection
- Multi-dimensional filtering by country, buyer category, product groups, and suppliers
- Sales performance metrics with period-over-period comparisons
- Visualizations for sales trends, customer segments, and product performance
- Top buyers analysis with detailed performance metrics

### Inventory Management
- Stock value by warehouse and product category
- Inventory aging analysis
- Stock turnover metrics
- Low stock alerts and recommendations

### Purchase Analytics
- Supplier performance metrics
- Purchase order tracking
- Cost analysis by product and supplier
- Lead time monitoring

### Data Import
- Flexible Excel file import with column mapping
- Support for various ERP data exports
- Data validation and error reporting
- Incremental data updates

## Technology Stack

- **Frontend**: Streamlit for interactive web interface
- **Backend**: Python with Pandas for data processing
- **Database**: PostgreSQL for data storage
- **Visualization**: Plotly for interactive charts
- **Deployment**: Supports Replit, AWS, and Docker environments

## Installation and Setup

### Prerequisites
- Python 3.9+
- PostgreSQL database

### Local Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/erp-analytics-dashboard.git
   cd erp-analytics-dashboard
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the database connection:
   - Create a PostgreSQL database
   - Set the `DATABASE_URL` environment variable with your connection string:
     ```bash
     export DATABASE_URL="postgresql://username:password@localhost:5432/erp_analytics"
     ```

4. Run the application:
   ```bash
   streamlit run app.py
   ```

### Docker Deployment

1. Build and run using Docker Compose:
   ```bash
   docker-compose up -d
   ```

2. Access the application at http://localhost:5000

### AWS Deployment

For detailed AWS deployment instructions, see [AWS Deployment Guide](docs/aws_deployment_guide.md).

## Data Sources

The dashboard is designed to work with the following ERP data exports:

- **Buyer Data**: Customer information including categories and countries
- **Product Data**: Product catalog with groups, brands, and supplier information
- **Price Data**: Historical price records for purchase and sales prices
- **Stock Data**: Inventory levels and warehouse data
- **Sales Data**: Transaction records with quantities, prices, and dates
- **Purchase Data**: Supplier orders and procurement details

## Project Structure

The project follows a modular architecture with separate components for different functionality. For a detailed explanation of the code structure, see [Project Structure Documentation](docs/project_structure.md).

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- Streamlit for the excellent web framework
- Plotly for the interactive visualization capabilities
- SQLAlchemy for the ORM functionality