Console.js:61 AG Grid: error #200 Unable to use filter as SetFilterModule is not registered. Check if you have registered the module:
import { ModuleRegistry } from 'ag-grid-community'; 
import { SetFilterModule } from 'ag-grid-enterprise'; 

ModuleRegistry.registerModules([ SetFilterModule ]); 

For more info see: https://www.ag-grid.com/react-data-grid/modules/ 
See https://www.ag-grid.com/react-data-grid/errors/200?_version_=33.0.3&gridId=1&gridScoped=false&rowModelType=clientSide&moduleName=SetFilter&reasonOrId=filter
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Attached grid return event cellValueChanged
Console.js:61 Received component message for unregistered ComponentInstance! Object
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Received component message for unregistered ComponentInstance! Object
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Received component message for unregistered ComponentInstance! Object
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 flag allow_unsafe_jscode is on.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 ***Received Props Object
Console.js:61 *** Processed State Object
Console.js:61 AG Grid: invalid gridOptions property 'enableCellChangeFlash' did you mean any of these: enableCellEditingOnBackspace, enableFillHandle, enableCellTextSelection, enableCellExpressions, enableRangeHandle, aggregateOnlyChangedColumns, enableCharts, includeHiddenColumnsInAdvancedFilter.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid gridOptions property 'enableFilter' did you mean any of these: enableAdvancedFilter, includeHiddenColumnsInAdvancedFilter, serverSideEnableClientSideSort, enableStrictPivotColumnOrder, enableCellExpressions, enableFillHandle, includeHiddenColumnsInQuickFilter, enterNavigatesVerticallyAfterEdit.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid gridOptions property 'floatingFilter' did you mean any of these: floatingFiltersHeight, includeHiddenColumnsInQuickFilter, includeHiddenColumnsInAdvancedFilter, allowShowChangeAfterFilter, paginationPageSizeSelector, groupRemoveLowestSingleChildren, excludeChildrenWhenTreeDataFiltering, enableAdvancedFilter.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: to see all the valid gridOptions properties please check: https://www.ag-grid.com/react-data-grid/grid-options/
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: error #200 Unable to use enableRangeSelection as CellSelectionModule is not registered. Check if you have registered the module:
import { ModuleRegistry } from 'ag-grid-community'; 
import { CellSelectionModule } from 'ag-grid-enterprise'; 

ModuleRegistry.registerModules([ CellSelectionModule ]); 

For more info see: https://www.ag-grid.com/react-data-grid/modules/ 
See https://www.ag-grid.com/react-data-grid/errors/200?_version_=33.0.3&gridId=1&gridScoped=false&rowModelType=clientSide&moduleName=CellSelection&reasonOrId=enableRangeSelection
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, enableRangeSelection is deprecated. Use `cellSelection = true` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, suppressRowDeselection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of version 32.2.1, using `rowSelection` with the values "single" or "multiple" has been deprecated. Use the object value instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, rowMultiSelectWithClick is deprecated. Use `rowSelection.enableSelectionWithoutKeys` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, suppressRowClickSelection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, groupSelectsChildren is deprecated. Use `rowSelection.groupSelects = "descendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, groupSelectsFiltered is deprecated. Use `rowSelection.groupSelects = "filteredDescendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid colDef property 'groupable' did you mean any of these: headerGroupComponent, headerGroupComponentParams, rowGroupIndex, initialRowGroupIndex, headerCheckboxSelectionCurrentPageOnly, columnGroupShow, groupId, floatingFilterComponentParams.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid colDef property 'filterable' did you mean any of these: suppressFiltersToolPanel, filterValueGetter, filterParams, floatingFilterComponent, floatingFilterComponentParams, headerCheckboxSelectionFilteredOnly, filter, getQuickFilterText.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: to see all the valid colDef properties please check: https://www.ag-grid.com/react-data-grid/column-properties/
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: error #200 Unable to use filter as SetFilterModule is not registered. Check if you have registered the module:
import { ModuleRegistry } from 'ag-grid-community'; 
import { SetFilterModule } from 'ag-grid-enterprise'; 

ModuleRegistry.registerModules([ SetFilterModule ]); 

For more info see: https://www.ag-grid.com/react-data-grid/modules/ 
See https://www.ag-grid.com/react-data-grid/errors/200?_version_=33.0.3&gridId=1&gridScoped=false&rowModelType=clientSide&moduleName=SetFilter&reasonOrId=filter
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Attached grid return event cellValueChanged
Console.js:61 flag allow_unsafe_jscode is on.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 ***Received Props Object
Console.js:61 *** Processed State Object
Console.js:61 AG Grid: invalid gridOptions property 'enableCellChangeFlash' did you mean any of these: enableCellEditingOnBackspace, enableFillHandle, enableCellTextSelection, enableCellExpressions, enableRangeHandle, aggregateOnlyChangedColumns, enableCharts, includeHiddenColumnsInAdvancedFilter.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid gridOptions property 'enableFilter' did you mean any of these: enableAdvancedFilter, includeHiddenColumnsInAdvancedFilter, serverSideEnableClientSideSort, enableStrictPivotColumnOrder, enableCellExpressions, enableFillHandle, includeHiddenColumnsInQuickFilter, enterNavigatesVerticallyAfterEdit.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid gridOptions property 'floatingFilter' did you mean any of these: floatingFiltersHeight, includeHiddenColumnsInQuickFilter, includeHiddenColumnsInAdvancedFilter, allowShowChangeAfterFilter, paginationPageSizeSelector, groupRemoveLowestSingleChildren, excludeChildrenWhenTreeDataFiltering, enableAdvancedFilter.
If you are trying to annotate gridOptions with application data, use the 'gridOptions.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: to see all the valid gridOptions properties please check: https://www.ag-grid.com/react-data-grid/grid-options/
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: error #200 Unable to use enableRangeSelection as CellSelectionModule is not registered. Check if you have registered the module:
import { ModuleRegistry } from 'ag-grid-community'; 
import { CellSelectionModule } from 'ag-grid-enterprise'; 

ModuleRegistry.registerModules([ CellSelectionModule ]); 

For more info see: https://www.ag-grid.com/react-data-grid/modules/ 
See https://www.ag-grid.com/react-data-grid/errors/200?_version_=33.0.3&gridId=1&gridScoped=false&rowModelType=clientSide&moduleName=CellSelection&reasonOrId=enableRangeSelection
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, enableRangeSelection is deprecated. Use `cellSelection = true` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, suppressRowDeselection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of version 32.2.1, using `rowSelection` with the values "single" or "multiple" has been deprecated. Use the object value instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, rowMultiSelectWithClick is deprecated. Use `rowSelection.enableSelectionWithoutKeys` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, suppressRowClickSelection is deprecated. Use `rowSelection.enableClickSelection` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, groupSelectsChildren is deprecated. Use `rowSelection.groupSelects = "descendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: As of v32.2, groupSelectsFiltered is deprecated. Use `rowSelection.groupSelects = "filteredDescendants"` instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid colDef property 'groupable' did you mean any of these: headerGroupComponent, headerGroupComponentParams, rowGroupIndex, initialRowGroupIndex, headerCheckboxSelectionCurrentPageOnly, columnGroupShow, groupId, floatingFilterComponentParams.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: invalid colDef property 'filterable' did you mean any of these: suppressFiltersToolPanel, filterValueGetter, filterParams, floatingFilterComponent, floatingFilterComponentParams, headerCheckboxSelectionFilteredOnly, filter, getQuickFilterText.
If you are trying to annotate colDef with application data, use the 'colDef.context' property instead.
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: to see all the valid colDef properties please check: https://www.ag-grid.com/react-data-grid/column-properties/
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Attached grid return event cellValueChanged
Console.js:61 AG Grid: error #29 tried to call sizeColumnsToFit() but the grid is coming back with zero width, maybe the grid is not visible yet on the screen? 
See https://www.ag-grid.com/react-data-grid/errors/29?_version_=33.0.3
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 AG Grid: error #29 tried to call sizeColumnsToFit() but the grid is coming back with zero width, maybe the grid is not visible yet on the screen? 
See https://www.ag-grid.com/react-data-grid/errors/29?_version_=33.0.3
Mt.forEach.n.<computed> @ Console.js:61
Console.js:61 Client Error: File uploader error on file upload - AxiosError: Network Error
Mt.forEach.n.<computed> @ Console.js:61
ee90b543-1847-4e87-9dee-5d901bcda784-00-39scf062fbk4w.picard.replit.dev/_stcore/upload_file/b268d0e4-aca5-4b55-9e84-ac618c10387d/0697fc25-dae6-4346-a77f-2e7d2eb1e118:1 
            
            
           Failed to load resource: net::ERR_FAILED
Console.js:61 Received component message for unregistered ComponentInstance! Object
Mt.forEach.n.<computed> @ Console.js:61

                
          
          
          
         Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.