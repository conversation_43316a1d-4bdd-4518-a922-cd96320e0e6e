﻿<?xml version="1.0" encoding="utf-8"?>
<customerorders>
	<customerorder>
		<date>2010-07-12 16:07:21</date> --<PERSON><PERSON><PERSON> k<PERSON> (CustomerOrder.VoucherDate)
		<expirationdays>10</expirationdays> --<PERSON><PERSON><PERSON><PERSON> napok s<PERSON> (üresen is hagyható)
		<orderid>246</orderid> --Web-es rendelésID, amit mi is elt<PERSON> (CustomerOrder.Id)
		<customer>234</customer> --Web-es vevőid, ami a vevő a weben jött létre és már lejött korábban
		<customerid>4357</customerid> --Symbol vevő ID ha a vevő a symbolból került feltöltésre (CustomerOrder.Customer)
		<customercode>WEB00000234</customercode> --Symbol vevő CODE ha van (Customer.Code)
		<customeremail><EMAIL></customeremail> --<PERSON><PERSON><PERSON> email címe (Customer.Email) Beállítás alapján vevő azonosításra használható.
		<country>Hungary</country> --Számlázási ország (Customer.InvoiceCountry)
		<region>Baranya</region> --Számlázási megye (Customer.InvoiceRegion)
		<zip>1139</zip> --Számlázási irányítószám (Customer.InvoiceZip)
		<city>Budapest</city> --Számlázási város (Customer.InvoiceCity)
		<street>Fő utca</street> --Számlázási utca (Customer.InvoiceStreet)
		<housenumber>1.</housenumber> --Számlázási házszám (Customer.InvoiceHouseNumber)
		<transportid>43575</transportid> --Telephely Symbol belső azonosító (CustomerAddress.Id)
		<transportname>Központi raktár</transportname> --Telephely megnevezése (CustomerAddress.Name)
		<transportcountry>Hungary</transportcountry> --Telephely ország (CustomerAddress.Country)
		<transportregion>Baranya</transportregion> --Telephely megye (CustomerAddress.Region)
		<transportzip>1139</transportzip> --Telephely irányítószám (CustomerAddress.Zip)
		<transportcity>Kecskemét</transportcity> --Telephely város (CustomerAddress.City)
		<transportstreet>Mellék utca</transportstreet> --Telephely utca (CustomerAddress.Street)
		<transporthousenumber>128</transporthousenumber> --Telephely házszám (CustomerAddress.HouseNumber)
		<transportcontactname>Nagy Ervin</transportcontactname> --Telephely kapcsolattartó (CustomerAddress.ContactName)
		<currency>HUF</currency> --Pénznem (Currency.Name)
		<currencyrate>1</currencyrate> --árfolyam (CurrencyRate.Rate)
		<transportmode>Személyes átvétel</transportmode> --szállítási mód (TransportMode.Name)
		<transporttargetid>589830</transporttargetid> --Postapont száma
		<transportdate>2010-07-20</transportdate> --szállítási dátum (CustomerOrder.DeliveryDate)
		<vouchersequencecode>RHU</vouchersequencecode> --rendelés tömbjének kódja (VoucherSequence.Code)
		<paymentmethod>Készpénz</paymentmethod> --fizetési mód (PaymentMethod.Name)
		<paymentmethodtolerance>8</paymentmethodtolerance> --átutalás esetén a napon száma (PaymentMethod.ToleranceDay)
		<warehouse>Központi</warehouse> --raktár (CustomerOrder.Warehouse)
		<notifyphone>1</notifyphone> --telefonos értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifyPhone)
		<notifysms>0</notifysms> --sms értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifySms)
		<notifyemail>1</notifyemail> --email értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifyEmail)
		<splitforbid>1</splitforbid> --egyben kiszolgálandó (1=igaz, 0=hamis) (CustomerOrder.SplitForbid)
		<banktrid>ABC123</banktrid> --banki tranzakciós azonosító (CustomerOrder.BankTRID)
		<division>ABC123</division> --divízió (CustomerOrder.Division.Name)
		<comment>Rendelés megjegyzése</comment> --Rendelés megjegyzése (CustomerOrder.Comment)	
		<closedmanually>0</closedmanually> --Rendelés kézzel lezárt (1=igaz, 0=hamis) (CustomerOrder.ClosedManually)
		<strexa>aaa</strexa> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExA)
		<strexb>bbb</strexb> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExB)
		<strexc>ccc</strexc> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExC)
		<strexd>ddd</strexd> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExD)
		<dateexa>2010-07-12</dateexa> -- Rendelés egyedi dátum mező (CustomerOrder.DateExA)
		<dateexb>2010-07-12</dateexb> -- Rendelés egyedi dátum mező (CustomerOrder.DateExB)
		<dateexc>2010-07-12</dateexc> -- Rendelés egyedi dátum mező (CustomerOrder.DateExC)
		<dateexd>2010-07-12</dateexd> -- Rendelés egyedi dátum mező (CustomerOrder.DateExD)
		<numexa>111</numexa> -- Rendelés egyedi szám mező (CustomerOrder.NumExA)
		<numexb>222</numexb> -- Rendelés egyedi szám mező (CustomerOrder.NumExB)
		<numexc>333</numexc> -- Rendelés egyedi szám mező (CustomerOrder.NumExC)
		<numexd>333</numexd> -- Rendelés egyedi szám mező (CustomerOrder.NumExD)
		<boolexa>0</boolexa> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExA)
		<boolexb>1</boolexb> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExB)
		<boolexc>1</boolexc> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExC)
		<boolexd>1</boolexd> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExD)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExD)
		<feedbackurl>http://www.valami.hu/commitorder?id=246&amp;symbolid=</feedbackurl> --Visszajelzés Url-e
 		<errorurl>http://www.valami.hu/errororder?id=246&amp;errormsg=</errorurl> --Error Url-e
		<detail>
			<productid>1</productid> --ID (Product.Id)
			<productname>Termék I.</productname> --termlk neve, ha más jelenjen meg a számlán (Product.Name)
			<quantity>3</quantity> --Mennyiség (CustomerOrderDetail.Quantity)
			<vat>27</vat> --Áfa százalék			
			<vatname>KBAET</vatname> --Áfa kulcs megnevezése, az iseuvat mező kivezetésre került 
			<unipricenet>60.00000</unipricenet> -- (CustomerOrderDetail.VirtualUnitPrice)
			<uniprice>21900.00000</uniprice> -- (CustomerOrderDetail.VirtualUnitPrice)
			<netvalue>710</netvalue> --Web-en látott nettó összár, amitől nem tudunk eltérni (CustomerOrderDetail.NetValue)
			<grossvalue>65700</grossvalue> --web-en látott összár, amitől nem tudunk eltérni (CustomerOrderDetail.GrossValue)
			<discountpercent>0</discountpercent> --kedvezmény %-os értéke (CustomerOrderDetail.DiscountPercent)
			<mustmanufacturing>1</mustmanufacturing> --kiszolgálás gyártással (1=igaz, 0=hamis) (CustomerOrderDetail.MustManufacturing)
			<allocate>0</allocate> --készlet foglalás (1=igaz, 0=hamis) (CustomerOrderDetail.AllocateWarehouse)
			<detailstatus>Beérkezés alatt</detailstatus> --tétel állapot (CustomerOrderDetailStatus.Name)
			<division>ABC123</division> --divízió (CustomerOrderDetail.Division.Name)
			<comment>Tétel megjegyzése</comment> --Megjegyzés (CustomerOrderDetail.Comment)
			<strexa>aaa</strexa> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExA)
			<strexb>bbb</strexb> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExB)
			<strexc>ccc</strexc> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExC)
			<strexd>ddd</strexd> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExD)
			<dateexa>2010-07-12</dateexa> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExA)
			<dateexb>2010-07-12</dateexb> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExB)
			<dateexc>2010-07-12</dateexc> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExC)
			<dateexd>2010-07-12</dateexd> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExD)
			<numexa>111</numexa> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExA)
			<numexb>222</numexb> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExB)
			<numexc>333</numexc> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExC)
			<numexd>333</numexd> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExD)
			<boolexa>0</boolexa> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExA)
			<boolexb>1</boolexb> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExB)
			<boolexc>1</boolexc> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExC)
			<boolexd>1</boolexd> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExD)
			<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExA)
			<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExB)
			<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExC)
			<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExD)
		</detail>
		<detail>
			<productcode>B123</productcode> --Termékkód (Product.Code)
			<productname>Termék II.</productname> --termlk neve, ha más jelenjen meg a számlán (Product.Name)
			<quantity>4</quantity> --Mennyiség (CustomerOrderDetail.Quantity)
			<vat>27</vat> --Áfa százalék
			<vatname>KBAET</vatname> --Áfa kulcs megnevezése, az iseuvat mező kivezetésre került 
			<unipricenet>60.00000</unipricenet> -- (CustomerOrderDetail.VirtualUnitPrice)
			<uniprice>1900.00000</uniprice> -- (CustomerOrderDetail.VirtualUnitPrice)
			<netvalue>710</netvalue> --Web-en látott nettó összár, amitől nem tudunk eltérni (CustomerOrderDetail.NetValue)
			<grossvalue>75700</grossvalue> --web-en látott összár, amitől nem tudunk eltérni (CustomerOrderDetail.GrossValue)
			<discountpercent>10</discountpercent> --kedvezmény %-os értéke (CustomerOrderDetail.DiscountPercent)
			<mustmanufacturing>0</mustmanufacturing> --kiszolgálás gyártással (1=igaz, 0=hamis) (CustomerOrderDetail.MustManufacturing)
			<allocate>1</allocate> --készlet foglalás (1=igaz, 0=hamis) (CustomerOrderDetail.AllocateWarehouse)
			<detailstatus>Beérkezés alatt</detailstatus> --tétel állapot (CustomerOrderDetailStatus.Name)
			<division>ABC123</division> --divízió (CustomerOrderDetail.Division.Name)
			<comment>Tétel megjegyzése</comment> --Megjegyzés (CustomerOrderDetail.Comment)
			<strexa>aaa</strexa> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExA)
			<strexb>bbb</strexb> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExB)
			<strexc>ccc</strexc> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExC)
			<strexd>ddd</strexd> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExD)
			<dateexa>2010-07-12</dateexa> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExA)
			<dateexb>2010-07-12</dateexb> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExB)
			<dateexc>2010-07-12</dateexc> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExC)
			<dateexd>2010-07-12</dateexd> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExD)
			<numexa>111</numexa> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExA)
			<numexb>222</numexb> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExB)
			<numexc>333</numexc> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExC)
			<numexd>333</numexd> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExD)
			<boolexa>0</boolexa> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExA)
			<boolexb>1</boolexb> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExB)
			<boolexc>1</boolexc> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExC)
			<boolexd>1</boolexd> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExD)
			<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExA)
			<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExB)
			<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExC)
			<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExD)
		</detail>
	</customerorder>
	<customerorder>
		<date>2010-07-12 16:07:21</date> --Rendelés kelte (CustomerOrder.VoucherDate)
		<expirationdays>10</expirationdays> --Lejárat napok száma (üresen is hagyható)
		<orderid>246</orderid> --Web-es rendelésID, amit mi is eltárolunk (CustomerOrder.Id)
		<customer>234</customer> --Web-es vevőid, ami a vevő a weben jött létre és már lejött korábban
		<customerid>4357</customerid> --Symbol vevő ID ha a vevő a symbolból került feltöltésre (CustomerOrder.Customer)
		<customercode>WEB00000234</customercode> --Symbol vevő CODE ha van (Customer.Code)
		<customeremail><EMAIL></customeremail> --Vevő email címe (Customer.Email) Beállítás alapján vevő azonosításra használható.
		<country>Hungary</country> --Számlázási ország (Customer.InvoiceCountry)
		<region>Baranya</region> --Számlázási megye (Customer.InvoiceRegion)
		<zip>1139</zip> --Számlázási irányítószám (Customer.InvoiceZip)
		<city>Budapest</city> --Számlázási város (Customer.InvoiceCity)
		<street>Fő utca</street> --Számlázási utca (Customer.InvoiceStreet)
		<housenumber>1.</housenumber> --Számlázási házszám (Customer.InvoiceHouseNumber)
		<transportid>43575</transportid> --Telephely Symbol belső azonosító (CustomerAddress.Id)
		<transportname>Központi raktár</transportname> --Telephely megnevezése (CustomerAddress.Name)
		<transportcountry>Hungary</transportcountry> --Telephely ország (CustomerAddress.Country)
		<transportregion>Baranya</transportregion> --Telephely megye (CustomerAddress.Region)
		<transportzip>1139</transportzip> --Telephely irányítószám (CustomerAddress.Zip)
		<transportcity>Kecskemét</transportcity> --Telephely város (CustomerAddress.City)
		<transportstreet>Mellék utca</transportstreet> --Telephely utca (CustomerAddress.Street)
		<transporthousenumber>128</transporthousenumber> --Telephely házszám (CustomerAddress.HouseNumber)
		<transportcontactname>Nagy Ervin</transportcontactname> --Telephely kapcsolattartó (CustomerAddress.ContactName)
		<currency>HUF</currency> --Pénznem (Currency.Name)
		<currencyrate>1</currencyrate> --árfolyam (CurrencyRate.Rate)
		<transportmode>Személyes átvétel</transportmode> --szállítási mód (TransportMode.Name)
		<transporttargetid>589830</transporttargetid> --Postapont száma
		<transportdate>2010-07-20</transportdate> --szállítási dátum (CustomerOrder.DeliveryDate)
		<vouchersequencecode>RHU</vouchersequencecode> --rendelés tömbjének kódja (VoucherSequence.Code)
		<paymentmethod>Készpénz</paymentmethod> --fizetési mód (PaymentMethod.Name)
		<paymentmethodtolerance>8</paymentmethodtolerance> --átutalás esetén a napon száma (PaymentMethod.ToleranceDay)
		<warehouse>Bolt</warehouse> --raktár (CustomerOrder.Warehouse)
		<notifyphone>1</notifyphone> --telefonos értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifyPhone)
		<notifysms>0</notifysms> --sms értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifySms)
		<notifyemail>1</notifyemail> --email értesítés (1=igaz, 0=hamis) (CustomerOrder.NotifyEmail)
		<splitforbid>1</splitforbid> --egyben kiszolgálandó (1=igaz, 0=hamis) (CustomerOrder.SplitForbid)
		<banktrid>ABC123</banktrid> --banki tranzakciós azonosító (CustomerOrder.BankTRID)
		<division>ABC123</division> --divízió (CustomerOrder.Division.Name)
		<comment>Rendelés megjegyzése</comment> --Rendelés megjegyzése (CustomerOrder.Comment)
		<closedmanually>0</closedmanually> --Rendelés kézzel lezárt (1=igaz, 0=hamis) (CustomerOrder.ClosedManually)
		<strexa>aaa</strexa> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExA)
		<strexb>bbb</strexb> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExB)
		<strexc>ccc</strexc> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExC)
		<strexd>ddd</strexd> -- Rendelés egyedi szöveges mező (CustomerOrder.StrExD)
		<dateexa>2010-07-12</dateexa> -- Rendelés egyedi dátum mező (CustomerOrder.DateExA)
		<dateexb>2010-07-12</dateexb> -- Rendelés egyedi dátum mező (CustomerOrder.DateExB)
		<dateexc>2010-07-12</dateexc> -- Rendelés egyedi dátum mező (CustomerOrder.DateExC)
		<dateexd>2010-07-12</dateexd> -- Rendelés egyedi dátum mező (CustomerOrder.DateExD)
		<numexa>111</numexa> -- Rendelés egyedi szám mező (CustomerOrder.NumExA)
		<numexb>222</numexb> -- Rendelés egyedi szám mező (CustomerOrder.NumExB)
		<numexc>333</numexc> -- Rendelés egyedi szám mező (CustomerOrder.NumExC)
		<numexd>333</numexd> -- Rendelés egyedi szám mező (CustomerOrder.NumExD)
		<boolexa>0</boolexa> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExA)
		<boolexb>1</boolexb> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExB)
		<boolexc>1</boolexc> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExC)
		<boolexd>1</boolexd> -- Rendelés egyedi logikai mező (CustomerOrder.BoolExD)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Rendelés egyedi kiválasztó mező (CustomerOrder.LookupExD)
		<feedbackurl>http://www.valami.hu/commitorder?id=246&amp;symbolid=</feedbackurl> --Visszajelzés Url-e
 		<errorurl>http://www.valami.hu/errororder?id=246&amp;errormsg=</errorurl> --Error Url-e
		<detail>
			<productid>1</productid> --ID (Product.Id)
			<productname>Termék I.</productname> --termlk neve, ha más jelenjen meg a számlán (Product.Name)
			<quantity>3</quantity> --Mennyiség (CustomerOrderDetail.Quantity)
			<vat>27</vat> --Áfa százalék
			<vatname>KBAET</vatname> --Áfa kulcs megnevezése, az iseuvat mező kivezetésre került 
			<unipricenet>60.00000</unipricenet> -- (CustomerOrderDetail.VirtualUnitPrice)
			<uniprice>21900.00000</uniprice> -- (CustomerOrderDetail.VirtualUnitPrice)
			<netvalue>710</netvalue> --Web-en látott nettó összár, amitől nem tudunk eltérni (CustomerOrderDetail.NetValue)
			<grossvalue>65700</grossvalue> --web-en látott összár, amitől nem tudunk eltérni (CustomerOrderDetail.GrossValue)
			<discountpercent>0</discountpercent> --kedvezmény %-os értéke (CustomerOrderDetail.DiscountPercent)
			<mustmanufacturing>1</mustmanufacturing> --kiszolgálás gyártással (1=igaz, 0=hamis) (CustomerOrderDetail.MustManufacturing)
			<allocate>0</allocate> --készlet foglalás (1=igaz, 0=hamis) (CustomerOrderDetail.AllocateWarehouse)
			<detailstatus>Beérkezés alatt</detailstatus> --tétel állapot (CustomerOrderDetailStatus.Name)
			<division>ABC123</division> --divízió (CustomerOrderDetail.Division.Name)
			<comment>Tétel megjegyzése</comment> --Megjegyzés (CustomerOrderDetail.Comment)
			<strexa>aaa</strexa> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExA)
			<strexb>bbb</strexb> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExB)
			<strexc>ccc</strexc> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExC)
			<strexd>ddd</strexd> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExD)
			<dateexa>2010-07-12</dateexa> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExA)
			<dateexb>2010-07-12</dateexb> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExB)
			<dateexc>2010-07-12</dateexc> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExC)
			<dateexd>2010-07-12</dateexd> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExD)
			<numexa>111</numexa> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExA)
			<numexb>222</numexb> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExB)
			<numexc>333</numexc> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExC)
			<numexd>333</numexd> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExD)
			<boolexa>0</boolexa> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExA)
			<boolexb>1</boolexb> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExB)
			<boolexc>1</boolexc> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExC)
			<boolexd>1</boolexd> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExD)
			<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExA)
			<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExB)
			<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExC)
			<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExD)
		</detail>
		<detail>
			<productcode>B123</productcode> --Termékkód (Product.Code)
			<productname>Termék II.</productname> --termlk neve, ha más jelenjen meg a számlán (Product.Name)
			<quantity>4</quantity> --Mennyiség (CustomerOrderDetail.Quantity)
			<vat>27</vat> --Áfa százalék
			<vatname>KBAET</vatname> --Áfa kulcs megnevezése, az iseuvat mező kivezetésre került 
			<unipricenet>60.00000</unipricenet> -- (CustomerOrderDetail.VirtualUnitPrice)
			<uniprice>1900.00000</uniprice> -- (CustomerOrderDetail.VirtualUnitPrice)
			<netvalue>710</netvalue> --Web-en látott nettó összár, amitől nem tudunk eltérni (CustomerOrderDetail.NetValue)
			<grossvalue>75700</grossvalue> --web-en látott összár, amitől nem tudunk eltérni (CustomerOrderDetail.GrossValue)
			<discountpercent>10</discountpercent> --kedvezmény %-os értéke (CustomerOrderDetail.DiscountPercent)
			<mustmanufacturing>0</mustmanufacturing> --kiszolgálás gyártással (1=igaz, 0=hamis) (CustomerOrderDetail.MustManufacturing)
			<allocate>1</allocate> --készlet foglalás (1=igaz, 0=hamis) (CustomerOrderDetail.AllocateWarehouse)
			<detailstatus>Beérkezés alatt</detailstatus> --tétel állapot (CustomerOrderDetailStatus.Name)
			<division>ABC123</division> --divízió (CustomerOrderDetail.Division.Name)
			<comment>Tétel megjegyzése</comment> --Megjegyzés (CustomerOrderDetail.Comment)
			<strexa>aaa</strexa> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExA)
			<strexb>bbb</strexb> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExB)
			<strexc>ccc</strexc> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExC)
			<strexd>ddd</strexd> -- Tétel egyedi szöveges mező (CustomerOrderDetail.StrExD)
			<dateexa>2010-07-12</dateexa> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExA)
			<dateexb>2010-07-12</dateexb> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExB)
			<dateexc>2010-07-12</dateexc> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExC)
			<dateexd>2010-07-12</dateexd> -- Tétel egyedi dátum mező (CustomerOrderDetail.DateExD)
			<numexa>111</numexa> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExA)
			<numexb>222</numexb> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExB)
			<numexc>333</numexc> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExC)
			<numexd>333</numexd> -- Tétel egyedi szám mező (CustomerOrderDetail.NumExD)
			<boolexa>0</boolexa> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExA)
			<boolexb>1</boolexb> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExB)
			<boolexc>1</boolexc> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExC)
			<boolexd>1</boolexd> -- Tétel egyedi logikai mező (CustomerOrderDetail.BoolExD)
			<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExA)
			<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExB)
			<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExC)
			<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Tétel egyedi kiválasztó mező (CustomerOrderDetail.LookupExD)
		</detail>
	</customerorder>
</customerorders>
