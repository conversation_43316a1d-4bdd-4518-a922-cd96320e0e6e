# AWS Deployment Guide for ERP Analytics Dashboard

This guide walks through the process of deploying the ERP Analytics Dashboard to AWS, specifically focusing on database configuration with Amazon RDS.

## Prerequisites

1. An AWS account with appropriate permissions
2. Basic familiarity with AWS services
3. A fully tested and functional ERP Analytics Dashboard running in Replit

## Step 1: Set Up an Amazon RDS PostgreSQL Instance

1. **Log in to your AWS Management Console**
   - Navigate to the RDS service

2. **Create a PostgreSQL Database**
   - Click "Create database"
   - Select "Standard create"
   - Choose PostgreSQL as the engine type
   - Select the latest compatible PostgreSQL version (any version >=12 will work)

3. **Configure the Database**
   - Choose "Dev/Test" for development or "Production" for production environments
   - Select an appropriate instance size:
     - For development: `db.t3.micro` or `db.t3.small`
     - For production: `db.t3.medium` or larger based on traffic expectations
   - Set your master username and password (record these for later use)
   - Choose storage settings (default is usually sufficient to start)

4. **Configure Connectivity**
   - Place the database in an appropriate VPC
   - For development: You can choose "Yes" for public access (but secure it with proper security groups)
   - For production: Choose "No" for public access and use a private VPC

5. **Configure Security**
   - Create or select an appropriate security group
   - Ensure the security group allows traffic on port 5432 from your application servers

6. **Set Additional Configuration**
   - Set the initial database name to "erp_analytics"
   - Enable automated backups with an appropriate retention period
   - Enable encryption if needed for your data security requirements

7. **Create the Database**
   - Review all settings and click "Create database"
   - Wait for the database creation process to complete (this may take several minutes)

## Step 2: Record RDS Connection Information

Once your RDS instance is created, note the following connection details:

1. **Endpoint**: This is the hostname of your database (e.g., `mydb.xxxxxxxxxxxx.us-east-1.rds.amazonaws.com`)
2. **Port**: Usually 5432 for PostgreSQL
3. **Database name**: "erp_analytics" (or whatever you specified during creation)
4. **Master username**: The username you configured
5. **Master password**: The password you configured

## Step 3: Test Database Connection

Before proceeding with deployment, it's a good idea to test the connection to your new RDS instance:

1. From your local machine or a temporary EC2 instance, use the `psql` command-line tool:
   ```
   psql -h <endpoint> -p 5432 -U <username> -d erp_analytics
   ```

2. If the connection is successful, you'll be able to run PostgreSQL commands.

## Step 4: Deploy Your Application

There are several ways to deploy your application to AWS. Here are some options:

### Option 1: AWS App Runner (Simplest)

1. **Containerize your application**
   - Create a `Dockerfile` for your application
   - Build and test the Docker image locally

2. **Push the image to Amazon ECR or use a GitHub repository**

3. **Create an App Runner service**
   - Point it to your container image or source repository
   - Configure environment variables for database connection (see Step 5)
   - Configure compute resources based on your application needs

### Option 2: AWS Elastic Beanstalk

1. **Create an Elastic Beanstalk environment**
   - Choose Python platform
   - Configure environment variables for database connection

2. **Deploy your application code**
   - Prepare your code for deployment (add necessary configuration files)
   - Upload your code or deploy from a repository

### Option 3: Amazon EC2

1. **Launch an EC2 instance**
   - Choose an appropriate instance type
   - Configure security groups to allow HTTP/HTTPS traffic

2. **Set up your server environment**
   - Install Python, required packages, and web server

3. **Deploy your application code**
   - Transfer your code to the EC2 instance
   - Configure environment variables for database connection
   - Set up a process manager like Supervisor to keep your application running

## Step 5: Configure Environment Variables

Your application is already set up to use environment variables for database configuration. You'll need to set the following environment variables in your deployment environment:

```
DEPLOYMENT_ENV=aws
DB_HOST=<your-rds-endpoint>
DB_PORT=5432
DB_NAME=erp_analytics
DB_USER=<your-master-username>
DB_PASSWORD=<your-master-password>
```

## Step 6: Initialize the Database

On first deployment, you'll need to ensure your database tables are created:

1. **Connect to your application server**
2. **Run a one-time database initialization**
   - The application already has automatic database initialization in the `database.py` module
   - The first request to the application will trigger table creation

## Step 7: Verify Deployment

1. **Access your application URL**
2. **Navigate to Settings > System Info**
   - Verify the environment shows "AWS Production"
   - Verify the database connection is successful

## Troubleshooting

If you encounter issues with the database connection:

1. **Check Security Groups and Network ACLs**
   - Ensure your application server can reach the RDS instance on port 5432

2. **Verify Environment Variables**
   - Double-check that all required environment variables are correctly set

3. **Check Database Logs**
   - Review the RDS logs for any connection errors

4. **Check Application Logs**
   - Review your application logs for detailed error messages

## Maintenance and Monitoring

1. **Set up RDS monitoring**
   - Enable Enhanced Monitoring for more detailed metrics
   - Create CloudWatch alarms for critical metrics

2. **Configure database backups**
   - Ensure automated backups are enabled
   - Consider additional backup strategies for critical data

3. **Plan for scaling**
   - Monitor database performance and scale up or out as needed
   - Consider read replicas for read-heavy workloads

## AWS Cost Considerations

Be aware of the costs associated with running AWS services:

1. **RDS Costs**
   - Instance hours (based on instance type)
   - Storage (GB per month)
   - Backup storage
   - Data transfer

2. **Application Hosting Costs**
   - App Runner, EC2, or Elastic Beanstalk charges
   - Data transfer

3. **Cost Optimization Tips**
   - Use Reserved Instances for RDS if you plan long-term usage
   - Scale down development/test instances when not in use
   - Monitor and set budget alerts to avoid unexpected charges