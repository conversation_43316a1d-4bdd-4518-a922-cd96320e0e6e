"""
Configuration module for the ERP Analytics Dashboard.
Handles different environments (Replit, AWS) and database connections.
"""
import os

class Config:
    # Detect if running in Replit
    IS_REPLIT = 'REPL_ID' in os.environ
    
    # Detect if running in Replit deployment (has deployment domain)
    IS_REPLIT_DEPLOYMENT = IS_REPLIT and any(domain in os.environ.get('REPLIT_DOMAINS', '') for domain in ['.replit.app', '.repl.co'])
    
    # Detect if running in AWS
    IS_AWS = os.environ.get('DEPLOYMENT_ENV') == 'aws'
    
    # Detect if running in Docker
    IS_DOCKER = os.environ.get('DEPLOYMENT_ENV') == 'docker'
    
    # Required AWS environment variables
    AWS_ENV_VARS = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
    
    def check_aws_env_vars(self):
        """Check if all required AWS environment variables are set.
        Returns a tuple of (bool, list) where bool indicates if all variables are set
        and list contains the names of any missing variables."""
        missing = []
        for var in self.AWS_ENV_VARS:
            if not os.environ.get(var):
                missing.append(var)
        return len(missing) == 0, missing
    
    # Application settings
    APP_NAME = "ERP Analytics Dashboard"
    
    # Date format for display
    DATE_FORMAT = "%Y-%m-%d"
    
    # Default currency
    DEFAULT_CURRENCY = "HUF"
    
    # Database settings based on environment
    @property
    def DATABASE_URL(self):
        """
        Returns the database URL based on the current environment.
        Ensures that a valid string is always returned.
        """
        # First check if DATABASE_URL is directly set (takes highest priority)
        db_url = os.environ.get('DATABASE_URL')
        if db_url:
            return db_url
            
        if self.IS_REPLIT:
            # Default Replit DB should be detected above via DATABASE_URL env var
            # This branch is just a fallback for custom Replit configurations
            return "postgresql://postgres:postgres@localhost:5432/erp_analytics"
            
        elif self.IS_AWS:
            # Use AWS RDS database
            db_host = os.environ.get('DB_HOST')
            db_port = os.environ.get('DB_PORT', '5432')
            db_name = os.environ.get('DB_NAME')
            db_user = os.environ.get('DB_USER')
            db_password = os.environ.get('DB_PASSWORD')
            
            # Only create AWS connection string if all required values are present
            if db_host and db_name and db_user and db_password:
                return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        elif self.IS_DOCKER:
            # In Docker, we assume the database service is named 'db'
            # Default credentials from docker-compose.yml
            db_host = os.environ.get('DB_HOST', 'db')
            db_port = os.environ.get('DB_PORT', '5432')
            db_name = os.environ.get('DB_NAME', 'erp_analytics')
            db_user = os.environ.get('DB_USER', 'postgres')
            db_password = os.environ.get('DB_PASSWORD', 'postgres')
            
            return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        # Fallback for local development or if required env vars missing
        return "postgresql://postgres:postgres@localhost:5432/erp_analytics"
    
    @property
    def ENVIRONMENT_NAME(self):
        """Return a human-readable environment name for UI display"""
        if self.IS_REPLIT_DEPLOYMENT:
            return "Replit Deployment"
        elif self.IS_REPLIT:
            return "Replit Development"
        elif self.IS_AWS:
            return "AWS Production"
        elif self.IS_DOCKER:
            return "Docker Container"
        else:
            return "Local Development"
    
    # AWS specific settings
    AWS_REGION = os.environ.get('AWS_REGION', 'us-east-1')
    
    # UI settings
    THEME_PRIMARY_COLOR = "#0068C9"  # Main blue theme color
    ENABLE_DEBUG_FEATURES = IS_REPLIT and not IS_REPLIT_DEPLOYMENT  # Enable debug features only in Replit development

# Create a global instance of the config
config = Config()