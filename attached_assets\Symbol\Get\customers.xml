<?xml version="1.0" encoding="UTF-8" ?>
<Customers>
	<Customer>
		<feedbackurl>http://www.valami.hu/commitcustomer?id=1231&amp;symbolid=</feedbackurl> --Vevő Symbolos azonosítójának visszaküldésére szolgáló URL, ez az URL kerül meghívásra végén a Symbolos Id-val kiegészítve, amennyiben a vevőt sikerült elmenteni (a cím és kapcsolattartó feedback külön kezelendő)	
		<errorurl>http://www.valami.hu/errorcustomer?id=1231&amp;errormsg=</errorurl> --<PERSON><PERSON><PERSON> sikertelen mentése esetén ez az URL kerül meghívásra, a hiba leírásával kiegészítve
		<id>1231</id> --web-es ID
		<sid>4357</sid> --symbol ID
		<code>WEB00001231</code> --<PERSON><PERSON><PERSON> (Customer.Code)
		<name><PERSON><PERSON><PERSON><PERSON> István</name> --<PERSON><PERSON><PERSON> (Customer.Name)
		<country>Hungary</country> --Számlázási cím - ország (Customer.InvoiceCountry)
		<region>Baranya</region> --Számlázási cím - megye (Customer.InvoiceRegion)
		<zip>1139</zip> --Számlázási cím - irányítószám (Customer.InvoiceZip)
		<city>Budapest</city> --Számlázási cím - város (Customer.InvoiceCity)
		<street>Fő utca</street> --Számlázási cím - utca (Customer.InvoiceStreet)
		<housenumber>1.</housenumber> --Számlázási cím - házszám (Customer.InvoiceHouseNumber)
		<mailcountry>Hungary</mailcountry> --Levelezési cím - ország (Customer.MailCountry)
		<mailregion>Baranya</mailregion> --Levelezési cím - megye (Customer.MailRegion)
		<mailzip>1139</mailzip> --Levelezési cím - irányítószám (Customer.MailZip)
		<mailcity>Kecskemét</mailcity> --Levelezési cím - város (Customer.MailCity)
		<mailstreet>Mellék utca</mailstreet> --Levelezési cím - utca (Customer.MailStreet)
		<mailhousenumber>128</mailhousenumber> --Levelezési cím - házszám (Customer.MailHouseNumber)
		<taxnumber>********-1-21</taxnumber> --Adószám (Customer.TaxNumber)
		<grouptaxnumber>********-1-21</grouptaxnumber> --Vevő csoportos adószám (Customer.GroupTaxNumber)
		<eutaxnumber>HU13581280-2-41</eutaxnumber> --Vevő közösségi adószám (Customer.EUTaxNumber)
		<bankaccount>********-********</bankaccount> --Vevő bankszámlája (Customer.BankAccount)
		<bankname>OTP Bank</bankname> --Vevő bankszámlájához tartozó bank neve (Customer.BankName)
		<bankswiftcode>OTPVHUHB</bankswiftcode> --Vevő bankszámlájához tartozó SWIFT kód (Customer.BankSwiftCode)
		<contactname>Balázs Piri Balázs</contactname> -- Kapcsolattartó neve (Customer.ContactName)
		<email><EMAIL></email> --Vevő email címe (Customer.Email) Beállítás alapján vevő azonosításra használható.
		<phone>70-789-4568</phone> --Vevő telefonszáma (Customer.Phone)
		<sms>70-789-4568</sms> --Vevő SMS száma (Customer.Sms)
		<fax>70-789-4569</fax> --Vevő faxszáma (Customer.Fax)
		<iscompany>0</iscompany> -- Cég/Magánszemély (Customer.IsCompany)
		<description>Megjegyzés</description> --Vevő megjegyzés (Customer.Comment)
		<customercategory>Főcsoport 1/Alcsoport 1A/Alcsoport 1AA</customercategory> --Vevőcsoport (CustomerCategory.Name)
		<pricecategoryname>Lista ár</pricecategoryname> --Árkategória (CustomerCategory.Name)
		<discountpercent>1.5</discountpercent> --Kedvezmény % (Customer.DiscountPercent)
		<webusername>user1</webusername> --Vevő web felhasználóneve (Customer.WebUsername)
		<webpassword>userpass</webpassword> --Vevő web jelszaba (Customer.WebPassword)
		<strexa>aaa</strexa> -- Vevő egyedi szöveges mező (Customer.StrExA)
		<strexb>bbb</strexb> -- Vevő egyedi szöveges mező (Customer.StrExB)
		<strexc>ccc</strexc> -- Vevő egyedi szöveges mező (Customer.StrExC)
		<strexd>ddd</strexd> -- Vevő egyedi szöveges mező (Customer.StrExD)
		<dateexa>2010-07-12</dateexa> -- Vevő egyedi dátum mező (Customer.DateExA)
		<dateexb>2010-07-12</dateexb> -- Vevő egyedi dátum mező (Customer.DateExB)
		<dateexc>2010-07-12</dateexc> -- Vevő egyedi dátum mező (Customer.DateExC)
		<dateexd>2010-07-12</dateexd> -- Vevő egyedi dátum mező (Customer.DateExD)
		<numexa>111</numexa> -- Vevő egyedi szám mező (Customer.NumExA)
		<numexb>222</numexb> -- Vevő egyedi szám mező (Customer.NumExB)
		<numexc>333</numexc> -- Vevő egyedi szám mező (Customer.NumExC)
		<numexd>333</numexd> -- Vevő egyedi szám mező (Customer.NumExD)
		<boolexa>0</boolexa> -- Vevő egyedi logikai mező (Customer.BoolExA)
		<boolexb>1</boolexb> -- Vevő egyedi logikai mező (Customer.BoolExB)
		<boolexc>1</boolexc> -- Vevő egyedi logikai mező (Customer.BoolExC)
		<boolexd>1</boolexd> -- Vevő egyedi logikai mező (Customer.BoolExD)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Vevő egyedi kiválasztó mező (Customer.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Vevő egyedi kiválasztó mező (Customer.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Vevő egyedi kiválasztó mező (Customer.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Vevő egyedi kiválasztó mező (Customer.LookupExD)
		<customeraddresses> 
			<customeraddress>
				<feedbackurl>http://www.valami.hu/commitcustomeraddress?id=12315&amp;symbolid=</feedbackurl>
				<preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Webes azonosítója
				<sid>12315</sid> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>********-1-21</companytaxnumber> --Telephely adószáma (CustomerAddress.CompanyTaxNumber)
				<companygrouptaxnumber>********-1-21</companygrouptaxnumber> --Telephely csoportos adószám (CustomerAddress.CompanyGroupTaxNumber)
				<companyeutaxnumber>HU13581280-2-41</companyeutaxnumber> --Telephely közösségi adószám (CustomerAddress.CompanyEUTaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
			<customeraddress>
				<feedbackurl>http://www.valami.hu/commitcustomeraddress?id=12315&amp;symbolid=</feedbackurl>
			    <preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Webes azonosítója
				<sid>12315</sid> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>********-1-21</companytaxnumber> --Telephely adószáma (CustomerAddress.CompanyTaxNumber)
				<companygrouptaxnumber>********-1-21</companygrouptaxnumber> --Telephely csoportos adószám (CustomerAddress.CompanyGroupTaxNumber)
				<companyeutaxnumber>HU13581280-2-41</companyeutaxnumber> --Telephely közösségi adószám (CustomerAddress.CompanyEUTaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
		</customeraddresses>
		<customercontacts>
			<customercontact>
				<feedbackurl>http://www.valami.hu/commitcustomercontact?id=12317&amp;symbolid=</feedbackurl>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<sid>12317</sid> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
			<customercontact>
				<feedbackurl>http://www.valami.hu/commitcustomercontact?id=12317&amp;symbolid=</feedbackurl>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<sid>12317</sid> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
		</customercontacts>
	</Customer>
	<Customer>
		<feedbackurl>http://www.valami.hu/commitcustomer?id=1231&amp;symbolid=</feedbackurl>
		<errorurl>http://www.valami.hu/errorcustomer?id=1231&amp;errormsg=</errorurl>
		<id>1231</id> --web-es ID
		<sid>4357</sid> --symbol ID
		<code>WEB00001231</code> --Vevő kódja (Customer.Code)
		<name>Kovács István</name> --Vevő neve (Customer.Name)
		<country>Hungary</country> --Számlázási cím - ország (Customer.InvoiceCountry)
		<region>Baranya</region> --Számlázási cím - megye (Customer.InvoiceRegion)
		<zip>1139</zip> --Számlázási cím - irányítószám (Customer.InvoiceZip)
		<city>Budapest</city> --Számlázási cím - város (Customer.InvoiceCity)
		<street>Fő utca</street> --Számlázási cím - utca (Customer.InvoiceStreet)
		<housenumber>1.</housenumber> --Számlázási cím - házszám (Customer.InvoiceHouseNumber)
		<mailcountry>Hungary</mailcountry> --Levelezési cím - ország (Customer.MailCountry)
		<mailregion>Baranya</mailregion> --Levelezési cím - megye (Customer.MailRegion)
		<mailzip>1139</mailzip> --Levelezési cím - irányítószám (Customer.MailZip)
		<mailcity>Kecskemét</mailcity> --Levelezési cím - város (Customer.MailCity)
		<mailstreet>Mellék utca</mailstreet> --Levelezési cím - utca (Customer.MailStreet)
		<mailhousenumber>128</mailhousenumber> --Levelezési cím - házszám (Customer.MailHouseNumber)
		<taxnumber>********-1-21</taxnumber> --Adószám (Customer.TaxNumber)
		<grouptaxnumber>********-1-21</grouptaxnumber> --Vevő csoportos adószám (Customer.GroupTaxNumber)
		<eutaxnumber>HU13581280-2-41</eutaxnumber> --Vevő közösségi adószám (Customer.EUTaxNumber)
		<bankaccount>********-********</bankaccount> --Vevő bankszámlája (Customer.BankAccount)
		<bankname>OTP Bank</bankname> --Vevő bankszámlájához tartozó bank neve (Customer.BankName)
		<bankswiftcode>OTPVHUHB</bankswiftcode> --Vevő bankszámlájához tartozó SWIFT kód (Customer.BankSwiftCode)
		<contactname>Balázs Piri Balázs</contactname> -- Kapcsolattartó neve (Customer.ContactName)
		<email><EMAIL></email> --Vevő email címe (Customer.Email) Beállítás alapján vevő azonosításra használható.
		<phone>70-789-4568</phone> --Vevő telefonszáma (Customer.Phone)
		<sms>70-789-4568</sms> --Vevő SMS száma (Customer.Sms)
		<fax>70-789-4569</fax> --Vevő faxszáma (Customer.Fax)
		<iscompany>0</iscompany> -- Cég/Magánszemély (Customer.IsCompany)
		<description>Megjegyzés</description> --Vevő megjegyzés (Customer.Comment)
		<customercategory>Főcsoport 1/Alcsoport 1A/Alcsoport 1AA</customercategory> --Vevőcsoport (CustomerCategory.Name)
		<pricecategoryname>Lista ár</pricecategoryname> --Árkategória (CustomerCategory.Name)
		<discountpercent>1.5</discountpercent> --Kedvezmény % (Customer.DiscountPercent)
		<webusername>user1</webusername> --Vevő web felhasználóneve (Customer.WebUsername)
		<webpassword>userpass</webpassword> --Vevő web jelszaba (Customer.WebPassword)
		<strexa>aaa</strexa> -- Vevő egyedi szöveges mező (Customer.StrExA)
		<strexb>bbb</strexb> -- Vevő egyedi szöveges mező (Customer.StrExB)
		<strexc>ccc</strexc> -- Vevő egyedi szöveges mező (Customer.StrExC)
		<strexd>ddd</strexd> -- Vevő egyedi szöveges mező (Customer.StrExD)
		<dateexa>2010-07-12</dateexa> -- Vevő egyedi dátum mező (Customer.DateExA)
		<dateexb>2010-07-12</dateexb> -- Vevő egyedi dátum mező (Customer.DateExB)
		<dateexc>2010-07-12</dateexc> -- Vevő egyedi dátum mező (Customer.DateExC)
		<dateexd>2010-07-12</dateexd> -- Vevő egyedi dátum mező (Customer.DateExD)
		<numexa>111</numexa> -- Vevő egyedi szám mező (Customer.NumExA)
		<numexb>222</numexb> -- Vevő egyedi szám mező (Customer.NumExB)
		<numexc>333</numexc> -- Vevő egyedi szám mező (Customer.NumExC)
		<numexd>333</numexd> -- Vevő egyedi szám mező (Customer.NumExD)
		<boolexa>0</boolexa> -- Vevő egyedi logikai mező (Customer.BoolExA)
		<boolexb>1</boolexb> -- Vevő egyedi logikai mező (Customer.BoolExB)
		<boolexc>1</boolexc> -- Vevő egyedi logikai mező (Customer.BoolExC)
		<boolexd>1</boolexd> -- Vevő egyedi logikai mező (Customer.BoolExD)
		<lookupexa>Főcsoport/Alcsoport</lookupexa> -- Vevő egyedi kiválasztó mező (Customer.LookupExA)
		<lookupexb>Főcsoport/Alcsoport</lookupexb> -- Vevő egyedi kiválasztó mező (Customer.LookupExB)
		<lookupexc>Főcsoport/Alcsoport</lookupexc> -- Vevő egyedi kiválasztó mező (Customer.LookupExC)
		<lookupexd>Főcsoport/Alcsoport</lookupexd> -- Vevő egyedi kiválasztó mező (Customer.LookupExD)
		<customeraddresses> 
			<customeraddress>
				<feedbackurl>http://www.valami.hu/commitcustomeraddress?id=12315&amp;symbolid=</feedbackurl>
				<preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Webes azonosítója
				<sid>12315</sid> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>********-1-21</companytaxnumber> --Telephely adószáma (CustomerAddress.CompanyTaxNumber)
				<companygrouptaxnumber>********-1-21</companygrouptaxnumber> --Telephely csoportos adószám (CustomerAddress.CompanyGroupTaxNumber)
				<companyeutaxnumber>HU13581280-2-41</companyeutaxnumber> --Telephely közösségi adószám (CustomerAddress.CompanyEUTaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
			<customeraddress>
				<feedbackurl>http://www.valami.hu/commitcustomeraddress?id=12315&amp;symbolid=</feedbackurl>
			    <preferred>1</preferred> -- Alapértelmezett telephely-e (CustomerAddress.Preferred)
				<id>12315</id> -- Telephely Webes azonosítója
				<sid>12315</sid> -- Telephely Symbol belső azonosítója (CustomerAddress.Id)
				<code>A12</code> -- Telephely kódja (CustomerAddress.Code)
				<name>Sample</name> -- Telephely neve (CustomerAddress.Name)
				<country>Hun</country> -- Telephely ország (CustomerAddress.Country)
				<region>Baranya</region> -- Telephely megye (CustomerAddress.Region)
				<zip>7624-2</zip> -- Telephely irányítószám (CustomerAddress.Zip)
				<city>Pécs</city> -- Telephely város (CustomerAddress.City)
				<street>Báthory István utca</street> -- Telephely utca (CustomerAddress.Street)
				<housenumber>20/a</housenumber> -- Telephely házszám (CustomerAddress.HouseNumber)
				<contactname>Balázs Piri Balázs</contactname> -- Telephely kapcsolattartó neve (CustomerAddress.ContactName)
				<phone>70-785-4587</phone> -- Telephely kapcsolattartó telefonszáma (CustomerAddress.Phone)
				<fax>1-456-7989</fax> -- Telephely kapcsolattartó Fax száma (CustomerAddress.Fax)
				<email><EMAIL></email> -- Telephely kapcsolattartó Email címe (CustomerAddress.Email)
				<iscompany>0</iscompany> -- Telephely önálló cégként működik-e (CustomerAddress.IsCompany)
				<companytaxnumber>********-1-21</companytaxnumber> --Telephely adószáma (CustomerAddress.CompanyTaxNumber)
				<companygrouptaxnumber>********-1-21</companygrouptaxnumber> --Telephely csoportos adószám (CustomerAddress.CompanyGroupTaxNumber)
				<companyeutaxnumber>HU13581280-2-41</companyeutaxnumber> --Telephely közösségi adószám (CustomerAddress.CompanyEUTaxNumber)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Telephely megjegyzés (CustomerAddress.Comment)
				<deleted>0</deleted> -- Telephely törölt állapotú-e (CustomerAddress.Deleted)
			</customeraddress>
		</customeraddresses>
		<customercontacts>
			<customercontact>
				<feedbackurl>http://www.valami.hu/commitcustomercontact?id=12317&amp;symbolid=</feedbackurl>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<sid>12317</sid> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
			<customercontact>
				<feedbackurl>http://www.valami.hu/commitcustomercontact?id=12317&amp;symbolid=</feedbackurl>
				<name>Sample</name> -- Kapcsolattartó neve (CustomerContact.Name)
				<sid>12317</sid> -- Kapcsolattartó Symbol belső azonosítója (CustomerContact.Id)
				<responsibility></responsibility> -- Kapcsolattartó munkaköre (CustomerContact.Responsibility)
				<phone>70-785-4587</phone>  -- Kapcsolattartó telefonszáma (CustomerContact.Phone)
				<fax>1-456-7989</fax> -- Kapcsolattartó Fax száma (CustomerContact.Fax)
				<sms>70-4587854</sms> -- Kapcsolattartó SMS száma (CustomerContact.Sms)
				<email><EMAIL></email> -- Kapcsolattartó Email címe (CustomerContact.Email)
				<url>www.sample.hu</url> -- Kapcsolattartó webcíme (CustomerContact.Url)
				<skype>something</skype> -- Kapcsolattartó Skype elérhetősége (CustomerContact.Skype)
				<facebookurl>facebook.com/sample</facebookurl> -- Kapcsolattartó Facebook linkje (CustomerContact.FacebookUrl)
				<msn>123ert</msn> -- Kapcsolattartó MSN elérhetősége (CustomerContact.Msn)
				<description>A kansasi rónaság közepéről Dorkát és Toto kutyát a forgószél csodás vidékre repíti, a mumpicok országába. Kiderül, hogy a kislány messzire elkerült otthonától. A jóságos Északi Boszorkánytól megtudja, hogy Smaragdvárosba kell eljutnia Ozhoz, a legnagyobb varázslóhoz, mert csak az ő segítségével juthat vissza az otthonába. A hosszú vándorút során Dorka igaz barátokra talál: a Madárijesztőre, a Bádog Favágóra és a Gyáva Oroszlánra, ők is a Bölcsek Bölcse segítségére vágynak. Számtalan kaland után elérkeznek Oz fényes palotájába. A nagy varázsló "megajándékozza" a Madárijesztőt ésszel, a Bádogembert szívvel s a Gyáva Oroszlánt is bátorrá teszi. De Dorkát csak a jó Déli Boszorkány útmutatása segíti haza szeretett otthonába.</description> -- Kapcsolattartó megjegyzés (CustomerContact.Comment)
				<deleted>0</deleted> -- Kapcsolattartó törölt állapotú-e (CustomerContact.Deleted)
			</customercontact>
		</customercontacts>
	</Customer>
</Customers>