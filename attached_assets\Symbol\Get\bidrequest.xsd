﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="bidrequests" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="bidrequests" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="bidrequest">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="date" type="xs:string" minOccurs="0" />
              <xs:element name="customer" type="xs:string" minOccurs="0" />
              <xs:element name="category" type="xs:string" minOccurs="0" />
              <xs:element name="subject" type="xs:string" minOccurs="0" />
              <xs:element name="comment" type="xs:string" minOccurs="0" />
              <xs:element name="feedbackurl" type="xs:string" minOccurs="0" />
              <xs:element name="errorurl" type="xs:string" minOccurs="0" />
              <xs:element name="lead" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="companyname" type="xs:string" minOccurs="0" />
                    <xs:element name="name" type="xs:string" minOccurs="0" />
                    <xs:element name="country" type="xs:string" minOccurs="0" />
                    <xs:element name="region" type="xs:string" minOccurs="0" />
                    <xs:element name="zip" type="xs:string" minOccurs="0" />
                    <xs:element name="city" type="xs:string" minOccurs="0" />
                    <xs:element name="street" type="xs:string" minOccurs="0" />
                    <xs:element name="housenumber" type="xs:string" minOccurs="0" />
                    <xs:element name="phone" type="xs:string" minOccurs="0" />
                    <xs:element name="fax" type="xs:string" minOccurs="0" />
                    <xs:element name="sms" type="xs:string" minOccurs="0" />
                    <xs:element name="email" type="xs:string" minOccurs="0" />
                    <xs:element name="leadcategory" type="xs:string" minOccurs="0" />
                    <xs:element name="customercategory" type="xs:string" minOccurs="0" />
                    <xs:element name="leadsource" type="xs:string" minOccurs="0" />
                    <xs:element name="leadsourcecomment" type="xs:string" minOccurs="0" />
                    <xs:element name="leadtarget" type="xs:string" minOccurs="0" />
                    <xs:element name="leadtargetcomment" type="xs:string" minOccurs="0" />
                    <xs:element name="agent" type="xs:string" minOccurs="0" />
                    <xs:element name="leadstate" type="xs:string" minOccurs="0" />
                    <xs:element name="leadstatecomment" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="detail" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="productid" type="xs:string" minOccurs="0" />
                    <xs:element name="quantity" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>