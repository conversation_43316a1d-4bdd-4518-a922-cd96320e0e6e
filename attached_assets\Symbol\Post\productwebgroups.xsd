﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProductWebGroups" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="ProductWebGroups" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ProductWebGroup">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" type="xs:string" minOccurs="0" />
              <xs:element name="Parent" type="xs:string" minOccurs="0" />
              <xs:element name="Name" type="xs:string" minOccurs="0" />
              <xs:element name="description" type="xs:string" minOccurs="0" />
              <xs:element name="webname" type="xs:string" minOccurs="0" />
              <xs:element name="webkeywords" type="xs:string" minOccurs="0" />
              <xs:element name="weburl" type="xs:string" minOccurs="0" />
              <xs:element name="webmetadescription" type="xs:string" minOccurs="0" />
              <xs:element name="inactive" type="xs:string" minOccurs="0" />
              <xs:element name="picture" type="xs:string" minOccurs="0" />
              <xs:element name="productwebgrouplangs" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="productwebgrouplang" minOccurs="0" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="lang" type="xs:string" minOccurs="0" />
                          <xs:element name="name" type="xs:string" minOccurs="0" />
                          <xs:element name="weburl" type="xs:string" minOccurs="0" />
                          <xs:element name="webname" type="xs:string" minOccurs="0" />
                          <xs:element name="webdescription" type="xs:string" minOccurs="0" />
                          <xs:element name="webmetadescription" type="xs:string" minOccurs="0" />
                          <xs:element name="webkeywords" type="xs:string" minOccurs="0" />
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>