streamlit.errors.StreamlitDuplicateElementId: There are multiple button elements with the same auto-generated ID. When this element is created, it is assigned an internal ID based on the element type and provided parameters. Multiple elements with the same type and parameters will cause this error.

To fix this error, please pass a unique key argument to the button element.

Traceback:
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/exec_code.py", line 121, in exec_func_with_error_handling
    result = func()
             ^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/scriptrunner/script_runner.py", line 648, in code_to_exec
    exec(code, module.__dict__)
File "/home/<USER>/workspace/app.py", line 167, in <module>
    main()
File "/home/<USER>/workspace/app.py", line 163, in main
    settings.show()
File "/home/<USER>/workspace/app_pages/settings.py", line 27, in show
    show_margin_by_brand_settings()
File "/home/<USER>/workspace/app_pages/settings.py", line 409, in show_margin_by_brand_settings
    if st.button("Save All Changes", type="primary"):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/runtime/metrics_util.py", line 444, in wrapped_func
    result = non_optional_func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/button.py", line 243, in button
    return self.dg._button(
           ^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/widgets/button.py", line 1007, in _button
    element_id = compute_and_register_element_id(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/lib/utils.py", line 239, in compute_and_register_element_id
    _register_element_id(ctx, element_type, element_id)
File "/home/<USER>/workspace/.pythonlibs/lib/python3.11/site-packages/streamlit/elements/lib/utils.py", line 145, in _register_element_id
    raise StreamlitDuplicateElementId(element_type)