import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from database import get_session
from sqlalchemy import func, desc, and_, extract
from models import Purchase, Product

def show():
    """Display the purchase analytics page."""
    st.header("Purchase Analytics")
    
    # Get database session
    session = get_session()
    if not session:
        st.error("Failed to connect to the database. Please check your connection settings.")
        return
    
    try:
        # Add filters in main area with expander (collapsed by default)
        with st.expander("Filters", expanded=False):
            # Create a grid layout for filters
            col1, col2 = st.columns(2)
            col3, col4 = st.columns(2)
            col5, col6 = st.columns(2)
            
            # Get date range for purchase data
            try:
                min_date, max_date = session.query(
                    func.min(Purchase.invoice_date),
                    func.max(Purchase.invoice_date)
                ).first()
            except:
                min_date, max_date = datetime.now() - timedelta(days=365), datetime.now()
            
            # If no date range found, use default values
            if not min_date or not max_date:
                min_date = datetime.now() - timedelta(days=365)
                max_date = datetime.now()
            
            # Add start date filter
            with col1:
                st.write("Start Date")
                start_date = st.date_input(
                    "Start Date",
                    value=min_date,
                    min_value=min_date,
                    max_value=max_date,
                    label_visibility="collapsed"
                )
            
            # Get suppliers for filtering
            suppliers = ["All"] + [s[0] for s in session.query(Purchase.supplier_name).distinct().all() if s[0]]
            
            # Add supplier filter
            with col2:
                st.write("Supplier")
                selected_supplier = st.selectbox(
                    "Supplier",
                    options=suppliers,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Add end date filter
            with col3:
                st.write("End Date")
                end_date = st.date_input(
                    "End Date",
                    value=max_date,
                    min_value=min_date,
                    max_value=max_date,
                    label_visibility="collapsed"
                )
            
            # Get brands for filtering
            brands = ["All"] + [b[0] for b in session.query(Product.brand).distinct().all() if b[0]]
            
            # Add brand filter
            with col4:
                st.write("Brand")
                selected_brand = st.selectbox(
                    "Brand",
                    options=brands,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Get product categories for filtering
            product_groups = ["All"] + [pg[0] for pg in session.query(Product.product_group).distinct().all() if pg[0]]
            
            # Add product category filter 
            with col5:
                st.write("Product Category")
                selected_product_category = st.selectbox(
                    "Product Category",
                    options=product_groups,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Get currencies
            currencies = ["All"] + [c[0] for c in session.query(Purchase.currency).distinct().all() if c[0]]
            
            # Add currency filter
            with col6:
                st.write("Currency")
                selected_currency = st.selectbox(
                    "Currency",
                    options=currencies,
                    index=0,
                    label_visibility="collapsed"
                )
            
            # Convert "All" selections to empty lists for filtering
            selected_suppliers = [] if selected_supplier == "All" else [selected_supplier]
            selected_product_groups = [] if selected_product_category == "All" else [selected_product_category]
            selected_brands = [] if selected_brand == "All" else [selected_brand]
            selected_currencies = [] if selected_currency == "All" else [selected_currency]
            
            # Add minimum purchase value filter (hidden, set to 0)
            min_purchase_value = 0
        
        # Tabs for different purchase views
        tab1, tab2, tab3 = st.tabs(["Purchase Overview", "Supplier Analysis", "Product Purchase Analysis"])
        
        with tab1:
            st.subheader("Purchase Overview")
            
            # Query purchase data with filters
            purchase_query = session.query(
                Purchase.invoice_date,
                func.sum(Purchase.total_value_huf).label('purchase_value'),
                func.sum(Purchase.quantity).label('units_purchased'),
                func.count(Purchase.id).label('invoice_count')
            ).filter(
                Purchase.invoice_date.between(start_date, end_date)
            )
            
            # Apply additional filters
            if selected_suppliers:
                purchase_query = purchase_query.filter(Purchase.supplier_name.in_(selected_suppliers))
                
            if selected_product_groups:
                purchase_query = purchase_query.join(Product, Purchase.product_id == Product.product_id) \
                                  .filter(Product.product_group.in_(selected_product_groups))
                
            # Group by date
            purchases_by_date = purchase_query.group_by(Purchase.invoice_date).order_by(Purchase.invoice_date).all()
            
            if purchases_by_date:
                # Convert to dataframe
                purchase_df = pd.DataFrame(purchases_by_date, columns=[
                    'date', 'purchase_value', 'units_purchased', 'invoice_count'
                ])
                
                # Resample to get different time views
                purchase_df_daily = purchase_df.set_index('date').sort_index()
                purchase_df_weekly = purchase_df_daily.resample('W').sum().reset_index()
                purchase_df_monthly = purchase_df_daily.resample('M').sum().reset_index()
                
                # Display summary metrics
                total_purchases = purchase_df_daily['purchase_value'].sum()
                total_units = purchase_df_daily['units_purchased'].sum()
                total_invoices = purchase_df_daily['invoice_count'].sum()
                avg_invoice_value = total_purchases / total_invoices if total_invoices > 0 else 0
                
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.write("Total Purchase Value")
                    st.write(f"### {total_purchases:,.0f} Ft")
                with col2:
                    st.write("Avg. Invoice Value")
                    st.write(f"### {avg_invoice_value:,.0f} Ft")
                with col3:
                    st.write("Total Invoices")
                    st.write(f"### {total_invoices:,.0f}")
                with col4:
                    st.write("Units Purchased")
                    st.write(f"### {total_units:,.0f}")
                
                # Add a view selector
                st.write("View")
                view_options = st.radio(
                    "View By",
                    options=["By Time", "By Supplier", "By Product"],
                    horizontal=True,
                    label_visibility="collapsed"
                )
                
                # Purchase Trend Chart Title
                st.subheader("Purchase Trend Over Time")
                
                # Display time trend visualization
                time_option = st.radio(
                    "Select Time View",
                    options=["Daily", "Weekly", "Monthly"],
                    horizontal=True
                )
                
                if time_option == "Daily":
                    trend_df = purchase_df_daily.reset_index()
                elif time_option == "Weekly":
                    trend_df = purchase_df_weekly
                else:
                    trend_df = purchase_df_monthly
                
                fig = px.line(
                    trend_df, 
                    x='date', 
                    y='purchase_value',
                    title=f'{time_option} Purchase Trend'
                )
                fig.update_layout(xaxis_title="Date", yaxis_title="Purchase Value ($)")
                st.plotly_chart(fig, use_container_width=True)
                
                # Display units purchased vs purchase value
                st.subheader("Units Purchased vs. Purchase Value")
                
                fig = go.Figure()
                fig.add_trace(go.Bar(
                    x=trend_df['date'],
                    y=trend_df['units_purchased'],
                    name='Units Purchased',
                    marker_color='lightblue'
                ))
                
                fig.add_trace(go.Scatter(
                    x=trend_df['date'],
                    y=trend_df['purchase_value'],
                    name='Purchase Value',
                    marker_color='darkblue',
                    yaxis='y2'
                ))
                
                fig.update_layout(
                    title='Units Purchased vs. Purchase Value',
                    xaxis_title='Date',
                    yaxis_title='Units Purchased',
                    yaxis2=dict(
                        title='Purchase Value ($)',
                        overlaying='y',
                        side='right'
                    ),
                    legend=dict(
                        x=0.01,
                        y=0.99,
                        traceorder="normal",
                        font=dict(size=12),
                    )
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No purchase data available for the selected filters. Please adjust your filters or upload purchase data.")
        
        with tab2:
            st.subheader("Supplier Analysis")
            
            # Query purchase data by supplier
            supplier_query = session.query(
                Purchase.supplier_name,
                func.sum(Purchase.total_value_huf).label('purchase_value'),
                func.sum(Purchase.quantity).label('units_purchased'),
                func.count(Purchase.id).label('invoice_count')
            ).filter(
                Purchase.invoice_date.between(start_date, end_date),
                Purchase.supplier_name.isnot(None)
            )
            
            # Apply additional filters
            if selected_suppliers:
                supplier_query = supplier_query.filter(Purchase.supplier_name.in_(selected_suppliers))
                
            if selected_product_groups:
                supplier_query = supplier_query.join(Product, Purchase.product_id == Product.product_id) \
                                 .filter(Product.product_group.in_(selected_product_groups))
                
            if min_purchase_value > 0:
                having_clause = func.sum(Purchase.total_value_huf) >= min_purchase_value
            else:
                having_clause = True
                
            # Group by supplier
            purchases_by_supplier = supplier_query.group_by(
                Purchase.supplier_name
            ).having(having_clause).order_by(desc('purchase_value')).all()
            
            if purchases_by_supplier:
                # Convert to dataframe
                supplier_df = pd.DataFrame(purchases_by_supplier, columns=[
                    'Supplier Name', 'Purchase Value', 'Units Purchased', 'Invoice Count'
                ])
                
                # Calculate average invoice value
                supplier_df['Avg Invoice Value'] = supplier_df['Purchase Value'] / supplier_df['Invoice Count']
                
                # Display as interactive table
                st.dataframe(supplier_df, use_container_width=True)
                
                # Visualization: Top 10 suppliers by purchase value
                st.subheader("Top 10 Suppliers by Purchase Value")
                top_suppliers = supplier_df.head(10)
                
                fig = px.bar(
                    top_suppliers,
                    x='Supplier Name',
                    y='Purchase Value',
                    hover_data=['Units Purchased', 'Invoice Count'],
                    title='Top 10 Suppliers by Purchase Value'
                )
                st.plotly_chart(fig, use_container_width=True)
                
                # Visualization: Supplier concentration
                st.subheader("Supplier Concentration")
                
                # Calculate percentages for concentration analysis
                supplier_df['Percent of Total'] = (supplier_df['Purchase Value'] / supplier_df['Purchase Value'].sum() * 100).round(2)
                supplier_df = supplier_df.sort_values('Percent of Total', ascending=False)
                
                # Calculate cumulative percentage
                supplier_df['Cumulative Percent'] = supplier_df['Percent of Total'].cumsum()
                
                # Top suppliers table
                st.write("Supplier Concentration Table")
                concentration_df = supplier_df[['Supplier Name', 'Purchase Value', 'Percent of Total', 'Cumulative Percent']]
                st.dataframe(concentration_df, use_container_width=True)
                
                # Pareto chart of supplier concentration
                fig = go.Figure()
                fig.add_trace(go.Bar(
                    x=supplier_df['Supplier Name'].head(15),
                    y=supplier_df['Percent of Total'].head(15),
                    name='Percent of Total',
                    marker_color='purple'
                ))
                
                fig.add_trace(go.Scatter(
                    x=supplier_df['Supplier Name'].head(15),
                    y=supplier_df['Cumulative Percent'].head(15),
                    name='Cumulative Percent',
                    marker_color='red',
                    mode='lines+markers',
                    yaxis='y2'
                ))
                
                fig.update_layout(
                    title='Supplier Concentration (Top 15)',
                    xaxis_title='Supplier',
                    yaxis_title='Percent of Total (%)',
                    yaxis2=dict(
                        title='Cumulative Percent (%)',
                        overlaying='y',
                        side='right',
                        range=[0, 100]
                    ),
                    legend=dict(
                        x=0.01,
                        y=0.99,
                        traceorder="normal",
                        font=dict(size=12),
                    )
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No supplier data available for the selected filters. Please adjust your filters or upload purchase data.")
        
        with tab3:
            st.subheader("Product Purchase Analysis")
            
            # Query purchase data by product
            product_query = session.query(
                Product.product_id,
                Product.product_name,
                Product.product_group,
                func.sum(Purchase.total_value_huf).label('purchase_value'),
                func.sum(Purchase.quantity).label('units_purchased'),
                func.avg(Purchase.unit_price).label('avg_unit_price')
            ).join(Product, Purchase.product_id == Product.product_id) \
             .filter(Purchase.invoice_date.between(start_date, end_date))
            
            # Apply additional filters
            if selected_suppliers:
                product_query = product_query.filter(Purchase.supplier_name.in_(selected_suppliers))
                
            if selected_product_groups:
                product_query = product_query.filter(Product.product_group.in_(selected_product_groups))
                
            if min_purchase_value > 0:
                having_clause = func.sum(Purchase.total_value_huf) >= min_purchase_value
            else:
                having_clause = True
                
            # Group by product
            purchases_by_product = product_query.group_by(
                Product.product_id, Product.product_name, Product.product_group
            ).having(having_clause).order_by(desc('purchase_value')).all()
            
            if purchases_by_product:
                # Convert to dataframe
                product_df = pd.DataFrame(purchases_by_product, columns=[
                    'Product ID', 'Product Name', 'Product Group', 
                    'Purchase Value', 'Units Purchased', 'Avg Unit Price'
                ])
                
                # Display as interactive table
                st.dataframe(product_df, use_container_width=True)
                
                # Visualization: Top 10 products by purchase value
                st.subheader("Top 10 Products by Purchase Value")
                top_products = product_df.head(10)
                
                fig = px.bar(
                    top_products,
                    x='Product Name',
                    y='Purchase Value',
                    hover_data=['Product Group', 'Units Purchased', 'Avg Unit Price'],
                    title='Top 10 Products by Purchase Value'
                )
                st.plotly_chart(fig, use_container_width=True)
                
                # Visualization: Purchases by product group
                st.subheader("Purchases by Product Group")
                group_summary = product_df.groupby('Product Group').agg({
                    'Purchase Value': 'sum',
                    'Units Purchased': 'sum'
                }).reset_index().sort_values('Purchase Value', ascending=False)
                
                fig = px.pie(
                    group_summary, 
                    values='Purchase Value', 
                    names='Product Group',
                    title='Purchase Distribution by Product Group',
                    hole=0.4
                )
                st.plotly_chart(fig, use_container_width=True)
                
                # Visualization: Average unit price by product group
                st.subheader("Average Unit Price by Product Group")
                avg_price_by_group = product_df.groupby('Product Group')['Avg Unit Price'].mean().reset_index()
                avg_price_by_group = avg_price_by_group.sort_values('Avg Unit Price', ascending=False)
                
                fig = px.bar(
                    avg_price_by_group,
                    x='Product Group',
                    y='Avg Unit Price',
                    title='Average Unit Price by Product Group'
                )
                st.plotly_chart(fig, use_container_width=True)
                
                # Visualization: Units purchased vs purchase value by product group
                st.subheader("Units Purchased vs. Purchase Value by Product Group")
                
                fig = go.Figure()
                fig.add_trace(go.Bar(
                    x=group_summary['Product Group'],
                    y=group_summary['Units Purchased'],
                    name='Units Purchased',
                    marker_color='lightgreen'
                ))
                
                fig.add_trace(go.Scatter(
                    x=group_summary['Product Group'],
                    y=group_summary['Purchase Value'],
                    name='Purchase Value',
                    marker_color='darkgreen',
                    yaxis='y2'
                ))
                
                fig.update_layout(
                    title='Units Purchased vs. Purchase Value by Product Group',
                    xaxis_title='Product Group',
                    yaxis_title='Units Purchased',
                    yaxis2=dict(
                        title='Purchase Value ($)',
                        overlaying='y',
                        side='right'
                    ),
                    legend=dict(
                        x=0.01,
                        y=0.99,
                        traceorder="normal",
                        font=dict(size=12),
                    )
                )
                st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("No product purchase data available for the selected filters. Please adjust your filters or upload purchase data.")
                
    except Exception as e:
        st.error(f"Error retrieving purchase data: {str(e)}")
    finally:
        # Close the session
        session.close()
