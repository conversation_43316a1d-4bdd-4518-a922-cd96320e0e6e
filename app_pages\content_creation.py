"""
Content Creation Page - Marketing Content Generation with Multi-Agent System
"""

import streamlit as st
import json
import os
import base64
from datetime import datetime
from io import BytesIO
from PIL import Image
import PyPDF2
import docx
from persona_agents import CustomerPersonaAgent, PersonaContentGenerator
from auth import require_auth, get_current_user

def process_uploaded_files(uploaded_files):
    """Process uploaded files and extract content for AI analysis"""
    processed_content = {
        "documents": [],
        "images": [],
        "total_files": len(uploaded_files)
    }
    
    for file in uploaded_files:
        file_info = {
            "name": file.name,
            "type": file.type,
            "size": len(file.getvalue())
        }
        
        try:
            if file.type == "application/pdf":
                # Process PDF
                pdf_reader = PyPDF2.PdfReader(BytesIO(file.getvalue()))
                text_content = ""
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
                
                file_info["content"] = text_content[:2000]  # Limit content for AI processing
                file_info["pages"] = len(pdf_reader.pages)
                processed_content["documents"].append(file_info)
                
            elif file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                # Process Word document
                doc = docx.Document(BytesIO(file.getvalue()))
                text_content = ""
                for paragraph in doc.paragraphs:
                    text_content += paragraph.text + "\n"
                
                file_info["content"] = text_content[:2000]  # Limit content for AI processing
                file_info["paragraphs"] = len(doc.paragraphs)
                processed_content["documents"].append(file_info)
                
            elif file.type.startswith("image/"):
                # Process image
                image = Image.open(BytesIO(file.getvalue()))
                
                # Convert to base64 for AI analysis
                buffered = BytesIO()
                image.save(buffered, format="PNG")
                img_base64 = base64.b64encode(buffered.getvalue()).decode()
                
                file_info["base64"] = img_base64
                file_info["dimensions"] = image.size
                file_info["format"] = image.format
                processed_content["images"].append(file_info)
                
        except Exception as e:
            file_info["error"] = str(e)
            st.warning(f"Could not process file {file.name}: {str(e)}")
    
    return processed_content

def show_file_upload_section():
    """Display file upload interface"""
    
    with st.expander("📁 Upload Reference Materials", expanded=False):
        st.markdown("""
        **Upload files to enhance content creation:**
        - **Product Catalogs** (PDF, Word): Specifications, features, pricing
        - **Case Studies** (PDF, Word): Real project examples and outcomes  
        - **Product Images** (JPG, PNG): Visual references for content
        - **Marketing Materials** (PDF): Existing campaigns and messaging
        """)
        
        uploaded_files = st.file_uploader(
            "Choose files to upload",
            type=['pdf', 'docx', 'jpg', 'jpeg', 'png'],
            accept_multiple_files=True,
            help="Upload product catalogs, case studies, images, or other reference materials"
        )
        
        if uploaded_files:
            st.success(f"✓ {len(uploaded_files)} file(s) uploaded successfully")
            
            # Show file preview
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Uploaded Files:**")
                for file in uploaded_files:
                    file_size = len(file.getvalue()) / 1024  # KB
                    st.write(f"• {file.name} ({file_size:.1f} KB)")
            
            with col2:
                if st.button("🔍 Process Files", type="secondary"):
                    with st.spinner("Processing uploaded files..."):
                        processed = process_uploaded_files(uploaded_files)
                        st.session_state.uploaded_content = processed
                        
                        # Show processing results
                        if processed["documents"]:
                            st.write(f"✓ Processed {len(processed['documents'])} document(s)")
                        if processed["images"]:
                            st.write(f"✓ Processed {len(processed['images'])} image(s)")
            
            return uploaded_files
        
        return None

def show_content_creation():
    """Main content creation interface"""
    
    st.title("🎯 Marketing Content Creator")
    st.markdown("Generate professional marketing content using AI agents")
    
    # Initialize the persona-targeted content creation systems
    if 'persona_agent' not in st.session_state:
        st.session_state.persona_agent = CustomerPersonaAgent()
    
    if 'persona_generator' not in st.session_state:
        st.session_state.persona_generator = PersonaContentGenerator()
    
    # Show file upload section
    show_file_upload_section()
    
    # Show unified persona-targeted content creation
    show_persona_content_creation()





def show_persona_content_creation():
    """Show the persona-targeted content creation interface"""
    
    st.subheader("🎯 Customer Segment Analysis")
    
    # Show analysis configuration
    col1, col2 = st.columns([2, 1])
    with col1:
        st.markdown("**Customer Segment Analysis**")
    with col2:
        analysis_period = st.selectbox(
            "Analysis Period",
            [6, 12, 18, 24],
            index=1,
            format_func=lambda x: f"Last {x} months",
            key="persona_analysis_period"
        )
    
    # Load personas if not already cached or period changed
    if ('customer_personas' not in st.session_state or 
        'last_analysis_period' not in st.session_state or 
        st.session_state.get('last_analysis_period') != analysis_period):
        
        with st.spinner(f"Analyzing customer segments for the last {analysis_period} months..."):
            try:
                # Create new persona agent with selected period
                persona_agent = CustomerPersonaAgent(analysis_period_months=analysis_period)
                personas = persona_agent.analyze_buyer_segments()
                st.session_state.customer_personas = personas
                st.session_state.last_analysis_period = analysis_period
                
                # Update the persona agent in session state
                st.session_state.persona_agent = persona_agent
            except Exception as e:
                st.error(f"Failed to analyze customer segments: {str(e)}")
                return
    
    personas = st.session_state.customer_personas
    
    if "error" in personas:
        st.error(f"Database error: {personas['error']}")
        return
    
    # Display persona overview
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("**Available Customer Segments:**")
        st.caption("Choose 'General' for broad market appeal or select a specific customer segment for targeted content")
        
        # Persona selection
        persona_names = list(personas.keys())
        selected_persona = st.selectbox(
            "Select Customer Segment",
            persona_names,
            help="Choose the customer segment to target with your content"
        )
        
        if selected_persona and selected_persona in personas:
            persona = personas[selected_persona]
            
            # Display persona summary
            st.markdown(f"**{selected_persona}** ({persona.business_type})")
            st.markdown(f"*Brand: {persona.brand_affiliation}*")
            st.markdown(f"*{persona.description}*")
            st.markdown(f"*Analysis Period: Last {analysis_period} months*")
            
            # Key characteristics
            with st.expander("📊 Segment Characteristics"):
                chars = persona.characteristics
                st.write(f"**Avg Order Value:** {chars.get('avg_order_value', 'N/A'):,.0f} HUF")
                st.write(f"**Purchase Frequency:** {chars.get('purchase_frequency', 'N/A')} times")
                st.write(f"**Price Sensitivity:** {chars.get('price_sensitivity', 'N/A')}")
                st.write(f"**Brand Loyalty:** {chars.get('brand_loyalty', 'N/A')}")
                st.write(f"**Digital Preference:** {chars.get('digital_preference', 'N/A')}")
            
            # Pain points
            with st.expander("🎯 Pain Points"):
                for pain in persona.pain_points:
                    st.write(f"• {pain}")
    
    with col2:
        st.subheader("Generate Targeted Content")
        
        if selected_persona and selected_persona in personas:
            persona = personas[selected_persona]
            
            # Content creation form
            content_type = st.selectbox(
                "Content Type",
                ["Newsletter", "Product Article", "Facebook Post", "LinkedIn Post", "Case Study"],
                help="Select the type of content to generate for this segment"
            )
            
            # Special handling for newsletters
            if content_type == "Newsletter":
                newsletter_type = st.selectbox(
                    "Newsletter Focus",
                    ["Product Spotlight", "Seasonal Promotion", "Industry Insights", "New Arrivals", "Educational Content"],
                    help="Choose the focus of the newsletter"
                )
                user_request = f"{newsletter_type} newsletter for {selected_persona} customers"
            else:
                user_request = st.text_area(
                    "Content Request",
                    placeholder=f"Example: Create content about new Yamaha keyboards for {selected_persona} customers",
                    height=80,
                    help="Describe what content you want to create for this customer segment"
                )
            
            # Language selection
            language = st.selectbox(
                "Language",
                ["English", "Hungarian", "Romanian", "Polish"],
                help="Select the target language for the content"
            )
            
            # Show uploaded content indicator
            if 'uploaded_content' in st.session_state and st.session_state.uploaded_content['total_files'] > 0:
                uploaded = st.session_state.uploaded_content
                st.info(f"📁 Using {uploaded['total_files']} uploaded file(s) as reference material")
            
            # Generate button
            if st.button("🎯 Generate Targeted Content", type="primary", disabled=not user_request.strip()):
                generate_persona_content(user_request, content_type, language, persona)
            
            # Show recommended content themes
            with st.expander("💡 Recommended Content Themes"):
                st.markdown("**Suggested themes for this segment:**")
                for theme in persona.content_themes:
                    st.write(f"• {theme}")
    
    # Display content generation history
    if 'persona_content_history' in st.session_state and st.session_state.persona_content_history:
        st.markdown("---")
        show_persona_content_history()


def generate_persona_content(user_request: str, content_type: str, language: str, persona):
    """Generate persona-targeted content"""
    
    # Show progress
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        steps = [
            "Analyzing customer persona...",
            "Adapting content strategy...",
            "Generating targeted content...",
            "Optimizing for segment preferences...",
            "Finalizing personalized content..."
        ]
        
        for i, step in enumerate(steps):
            status_text.text(step)
            progress_bar.progress((i + 1) / len(steps))
        
        try:
            with st.spinner("Generating persona-targeted content..."):
                # Map content type and language
                lang_mapping = {"English": "en", "Hungarian": "hu", "Romanian": "ro", "Polish": "pl"}
                mapped_lang = lang_mapping.get(language, "en")
                
                # Get uploaded content from session state
                uploaded_content = st.session_state.get('uploaded_content', None)
                
                # Generate content using persona
                result = st.session_state.persona_generator.generate_persona_targeted_content(
                    persona, content_type.lower().replace(" ", "_"), user_request, mapped_lang, uploaded_content
                )
            
            progress_bar.progress(1.0)
            status_text.text("Persona-targeted content generation complete!")
            
            # Store in session state
            if 'persona_content_history' not in st.session_state:
                st.session_state.persona_content_history = []
            
            content_entry = {
                "timestamp": datetime.now(),
                "request": user_request,
                "content_type": content_type,
                "language": language,
                "persona_segment": persona.segment_name,
                "brand_affiliation": persona.brand_affiliation,
                "result": result
            }
            
            st.session_state.persona_content_history.insert(0, content_entry)
            
            # Clear progress indicators
            progress_container.empty()
            
            # Display result
            display_persona_content(result, content_type, persona)
            
        except Exception as e:
            progress_container.empty()
            st.error(f"Persona content generation failed: {str(e)}")


def show_persona_content_history():
    """Display persona-targeted content history"""
    
    st.subheader("📚 Persona Content History")
    
    # Filter controls
    col1, col2, col3 = st.columns(3)
    
    with col1:
        filter_persona = st.selectbox(
            "Filter by Persona",
            ["All"] + list(set(h["persona_segment"] for h in st.session_state.persona_content_history)),
            key="history_filter_persona"
        )
    
    with col2:
        filter_brand = st.selectbox(
            "Filter by Brand",
            ["All", "Eurhythmics", "Intermuzika"],
            key="history_filter_brand"
        )
    
    with col3:
        if st.button("🗑️ Clear Persona History"):
            st.session_state.persona_content_history = []
            st.rerun()
    
    # Display filtered history
    filtered_history = st.session_state.persona_content_history
    
    if filter_persona != "All":
        filtered_history = [h for h in filtered_history if h["persona_segment"] == filter_persona]
    
    if filter_brand != "All":
        filtered_history = [h for h in filtered_history if h["brand_affiliation"] == filter_brand]
    
    if not filtered_history:
        st.info("No persona content history found.")
        return
    
    # Display entries
    for i, entry in enumerate(filtered_history[:10]):
        with st.expander(f"{entry['persona_segment']} - {entry['content_type']} - {entry['timestamp'].strftime('%Y-%m-%d %H:%M')}"):
            
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.write(f"**Request:** {entry['request']}")
                st.write(f"**Target Segment:** {entry['persona_segment']}")
                
                if entry["result"].get("success"):
                    content = entry["result"].get("content", "")
                    if len(content) > 200:
                        st.write(f"**Content Preview:** {content[:200]}...")
                    else:
                        st.write(f"**Content:** {content}")
                else:
                    st.error(f"Generation failed: {entry['result'].get('error', 'Unknown error')}")
            
            with col2:
                st.write(f"**Type:** {entry['content_type']}")
                st.write(f"**Language:** {entry['language']}")
                st.write(f"**Brand:** {entry['brand_affiliation']}")
                
                if entry["result"].get("success") and st.button("🔄 Regenerate", key=f"regen_persona_{i}"):
                    # Need to get persona object for regeneration
                    if 'customer_personas' in st.session_state:
                        persona = st.session_state.customer_personas.get(entry['persona_segment'])
                        if persona:
                            generate_persona_content(
                                entry["request"],
                                entry["content_type"],
                                entry["language"],
                                persona
                            )


def display_persona_content(result: dict, content_type: str, persona):
    """Display persona-targeted content with segment context"""
    
    if not result.get("success", False):
        st.error(f"Persona content generation failed: {result.get('error', 'Unknown error')}")
        return
    
    st.success(f"Persona-targeted content generated for {persona.segment_name}!")
    
    # Content display with persona context
    st.subheader(f"Generated {content_type} for {persona.segment_name}")
    st.markdown(f"*Brand: {persona.brand_affiliation} | Business Type: {persona.business_type}*")
    
    # Main content
    content = result.get("content", "")
    if content:
        if content_type in ["Facebook Post", "LinkedIn Post", "Newsletter"]:
            st.markdown("### Content Preview")
            st.info(content)
        else:
            st.markdown("### Content Preview")
            st.markdown(content)
        
        # Copy to clipboard button
        if st.button("📋 Copy to Clipboard", key=f"copy_persona_{datetime.now().timestamp()}"):
            st.code(content, language="text")
            st.info("Content ready to copy! Select all text above and copy.")
    
    # Persona targeting insights
    col1, col2 = st.columns(2)
    
    with col1:
        with st.expander("🎯 Persona Targeting"):
            st.write(f"**Target Segment:** {persona.segment_name}")
            st.write(f"**Communication Style:** {persona.communication_style}")
            st.write(f"**Key Pain Points Addressed:**")
            for pain in persona.pain_points[:3]:
                st.write(f"• {pain}")
    
    with col2:
        with st.expander("📊 Content Optimization"):
            st.write(f"**Brand Alignment:** {result.get('brand_affiliation', 'N/A')}")
            st.write(f"**Business Context:** {persona.business_type}")
            st.write(f"**Preferred Channels:** {', '.join(persona.preferences.get('communication_channels', [])[:3])}")


def generate_content(user_request: str, content_type: str, language: str):
    """Generate content using the multi-agent system"""
    
    # Map display names to internal format
    type_mapping = {
        "Product Article": "product_article",
        "Facebook Post": "facebook_post",
        "LinkedIn Post": "linkedin_post",
        "Case Study": "case_study"
    }
    
    lang_mapping = {
        "English": "en",
        "Hungarian": "hu"
    }
    
    # Show progress
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Step indicators
        steps = [
            "Planning content strategy...",
            "Researching market trends...",
            "Analyzing product data...",
            "Developing content strategy...",
            "Writing content...",
            "Localizing content...",
            "Quality review...",
            "Finalizing content..."
        ]
        
        for i, step in enumerate(steps):
            status_text.text(step)
            progress_bar.progress((i + 1) / len(steps))
            
        # Generate content
        try:
            with st.spinner("Generating content with AI agents..."):
                mapped_type = type_mapping[content_type]
                mapped_lang = lang_mapping[language]
                result = st.session_state.content_creator.create_content(
                    user_request, mapped_type, mapped_lang
                )
            
            progress_bar.progress(1.0)
            status_text.text("Content generation complete!")
            
            # Store in session state
            if 'content_history' not in st.session_state:
                st.session_state.content_history = []
            
            content_entry = {
                "timestamp": datetime.now(),
                "request": user_request,
                "content_type": content_type,
                "language": language,
                "result": result
            }
            
            st.session_state.content_history.insert(0, content_entry)
            
            # Clear progress indicators
            progress_container.empty()
            
            # Display result
            display_generated_content(result, content_type)
            
        except Exception as e:
            progress_container.empty()
            st.error(f"Content generation failed: {str(e)}")


def display_generated_content(result: dict, content_type: str):
    """Display the generated content with formatting"""
    
    if not result.get("success", False):
        st.error(f"Content generation failed: {result.get('error', 'Unknown error')}")
        return
    
    st.success("Content generated successfully!")
    
    # Content display
    st.subheader(f"Generated {content_type}")
    
    # Main content
    content = result.get("content", "")
    if content:
        # Different display based on content type
        if content_type in ["Facebook Post", "LinkedIn Post"]:
            st.markdown("### Content Preview")
            st.info(content)
        else:
            st.markdown("### Content Preview")
            st.markdown(content)
        
        # Copy to clipboard button
        if st.button("📋 Copy to Clipboard", key=f"copy_{datetime.now().timestamp()}"):
            st.code(content, language="text")
            st.info("Content ready to copy! Select all text above and copy.")
    
    # Metadata and strategy
    col1, col2 = st.columns(2)
    
    with col1:
        if result.get("strategy"):
            with st.expander("📊 Content Strategy"):
                strategy = result["strategy"]
                if isinstance(strategy, dict):
                    for key, value in strategy.items():
                        st.write(f"**{key.replace('_', ' ').title()}:** {value}")
                else:
                    st.write(strategy)
    
    with col2:
        with st.expander("📈 Generation Metrics"):
            st.write(f"**Quality Score:** {result.get('quality_score', 0.0):.1f}/1.0")
            st.write(f"**Language:** {result.get('language', 'Unknown')}")
            st.write(f"**Content Type:** {result.get('content_type', 'Unknown')}")
            
            metadata = result.get("metadata", {})
            if metadata.get("errors"):
                st.write("**Warnings:**")
                for error in metadata["errors"]:
                    st.write(f"- {error}")


def show_content_history():
    """Display previously generated content"""
    
    st.subheader("📚 Content History")
    
    # Filter controls
    col1, col2, col3 = st.columns(3)
    
    with col1:
        filter_type = st.selectbox(
            "Filter by Type",
            ["All"] + ["Product Article", "Facebook Post", "LinkedIn Post", "Case Study"],
            key="history_filter_type"
        )
    
    with col2:
        filter_language = st.selectbox(
            "Filter by Language",
            ["All", "English", "Hungarian"],
            key="history_filter_language"
        )
    
    with col3:
        if st.button("🗑️ Clear History"):
            st.session_state.content_history = []
            st.rerun()
    
    # Display filtered history
    filtered_history = st.session_state.content_history
    
    if filter_type != "All":
        filtered_history = [h for h in filtered_history if h["content_type"] == filter_type]
    
    if filter_language != "All":
        filtered_history = [h for h in filtered_history if h["language"] == filter_language]
    
    if not filtered_history:
        st.info("No content history found.")
        return
    
    # Display entries
    for i, entry in enumerate(filtered_history[:10]):  # Show last 10 entries
        with st.expander(f"{entry['content_type']} - {entry['timestamp'].strftime('%Y-%m-%d %H:%M')}"):
            
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.write(f"**Request:** {entry['request']}")
                
                if entry["result"].get("success"):
                    content = entry["result"].get("content", "")
                    if len(content) > 200:
                        st.write(f"**Content Preview:** {content[:200]}...")
                    else:
                        st.write(f"**Content:** {content}")
                else:
                    st.error(f"Generation failed: {entry['result'].get('error', 'Unknown error')}")
            
            with col2:
                st.write(f"**Type:** {entry['content_type']}")
                st.write(f"**Language:** {entry['language']}")
                
                if entry["result"].get("success"):
                    quality = entry["result"].get("quality_score", 0.0)
                    st.write(f"**Quality:** {quality:.1f}/1.0")
                    
                    if st.button("🔄 Regenerate", key=f"regen_{i}"):
                        generate_content(
                            entry["request"],
                            entry["content_type"],
                            entry["language"]
                        )


if __name__ == "__main__":
    show_content_creation()