"""
AI Analytics - Conversational interface for ERP data analysis using OpenAI GPT-4o
"""
import streamlit as st
import pandas as pd
import json
import os
from openai import OpenAI
from database import get_session
from models import *
from sqlalchemy import text, func, desc, asc
from sqlalchemy.orm import joinedload
from components.ui import format_currency, format_number
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta

# the newest OpenAI model is "gpt-4o" which was released May 13, 2024.
# do not change this unless explicitly requested by the user
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
openai_client = OpenAI(api_key=OPENAI_API_KEY)

class ERPAnalyticsAgent:
    """AI agent for analyzing ERP data through natural language queries"""
    
    def __init__(self):
        self.session = get_session()
        
    def get_database_schema(self):
        """Get a description of the database schema for the AI"""
        schema_info = {
            "tables": {
                "buyers": {
                    "description": "Customer/buyer information",
                    "key_columns": ["buyer_id", "buyer_name", "country", "buyer_category", "web_access"]
                },
                "products": {
                    "description": "Product catalog with brands, categories, suppliers",
                    "key_columns": ["product_id", "product_name", "brand", "product_group", "vendor_product_group", "primary_supplier"]
                },
                "sales": {
                    "description": "Sales transactions with quantities, prices, dates",
                    "key_columns": ["buyer_id", "product_id", "quantity", "unit_price", "sale_date", "purchase_unit_price"]
                },
                "purchases": {
                    "description": "Purchase invoices from suppliers",
                    "key_columns": ["supplier_name", "product_id", "quantity", "unit_price", "invoice_date", "currency", "total_value_huf"]
                },
                "prices": {
                    "description": "Product pricing history (purchase, store, online, export prices)",
                    "key_columns": ["product_id", "purchase_price", "store_price", "online_price", "export_price", "msrp", "valid_from"]
                },
                "stocks": {
                    "description": "Current inventory levels and values",
                    "key_columns": ["product_id", "quantity", "value", "warehouse", "date_recorded"]
                },
                "competitor_scrape": {
                    "description": "Competitor pricing data from various music stores",
                    "key_columns": ["product_code", "thomann_price", "muziker_price", "r55_price", "kytary_price", "mezzo_price", "last_scraped"]
                }
            },
            "buyer_category_mapping": {
                "Kisker": ["Kiskereskedelmi egység", "Bolti vásárló"],
                "Nagyker": ["Szerződés nélküli partner", "Wholesale"],
                "Install": ["Installációs partner"],
                "Export": ["Export"],
                "Egyéb": ["Webes vásárló", "Magánszemély", "Együttműködő partner", "Dolgozók", "Rendszer-Viszonteladó", "Vegyes kereskedelem", "Saját projektünk", "Tanuló", "Zenetanár", "Beszállító"],
                "Rental": ["Rental partner"]
            },
            "important_notes": [
                "All prices are in HUF (Hungarian Forint) unless specified otherwise",
                "Product codes (termékkód) are stored as strings",
                "Sales data includes both sale and purchase information per transaction",
                "Competitor data is scraped from music instrument retailers",
                "Database buyer categories (Hungarian): 'Webes vásárló', 'Bolti vásárló', 'Szerződés nélküli partner', 'Kiskereskedelmi egység', 'Magánszemély', 'Rental partner', 'Együttműködő partner', 'Dolgozók', 'Rendszer-Viszonteladó', 'Export', 'Installációs partner', 'Vegyes kereskedelem', 'Saját projektünk', 'Tanuló', 'Zenetanár', 'Beszállító', 'Wholesale'",
                "Business category groups (English): Kisker, Nagyker, Install, Export, Egyéb, Rental",
                "When user asks about business categories (Kisker, Nagyker, etc.), use IN clause with corresponding database categories",
                "Use ILIKE for case-insensitive matching when searching product names or categories"
            ],
            "formatting_requirements": {
                "product_display": "For any product results, ALWAYS include these three columns: p.brand, p.product_id AS termékkód, p.product_name AS terméknév",
                "currency_formatting": "Format all currency values (prices, revenue, profit) using thousand separators with no decimal places and add 'HUF' suffix. Example: 1,234,567 HUF instead of 1234567.0",
                "examples": {
                    "product_query": "SELECT p.brand, p.product_id AS termékkód, p.product_name AS terméknév, SUM(s.quantity * s.unit_price) AS total_revenue FROM products p JOIN sales s ON p.product_id = s.product_id GROUP BY p.brand, p.product_id, p.product_name ORDER BY total_revenue DESC LIMIT 10",
                    "currency_display": "Present results like: 'Total revenue: 1,234,567 HUF' not '1234567.0'"
                }
            },
            "product_search_strategy": {
                "search_priority": "When user mentions a product name/code, search FIRST in product_id (termékkód) with exact match, then in product_name (terméknév) with ILIKE pattern matching",
                "search_examples": {
                    "exact_code": "WHERE p.product_id = 'ADXR=-A'",
                    "name_pattern": "WHERE p.product_name ILIKE '%ADXR%'",
                    "combined": "WHERE p.product_id = 'ADXR=-A' OR p.product_name ILIKE '%ADXR%'"
                }
            },
            "sales_metrics_requirements": {
                "mandatory_fields": "For product sales analysis, ALWAYS include: SUM(s.quantity) AS total_quantity, SUM(s.quantity * s.unit_price) AS total_revenue, SUM((s.unit_price - s.purchase_unit_price) * s.quantity) AS total_profit",
                "complete_example": "SELECT p.brand, p.product_id AS termékkód, p.product_name AS terméknév, SUM(s.quantity) AS total_quantity, SUM(s.quantity * s.unit_price) AS total_revenue, SUM((s.unit_price - s.purchase_unit_price) * s.quantity) AS total_profit FROM products p JOIN sales s ON p.product_id = s.product_id WHERE p.product_id = 'ADXR=-A' GROUP BY p.brand, p.product_id, p.product_name"
            }
        }
        return schema_info
    
    def execute_sql_query(self, query: str):
        """Execute SQL query safely and return results"""
        try:
            # Ensure only SELECT queries are executed
            query_clean = query.strip().upper()
            if not query_clean.startswith('SELECT'):
                return {"error": "Only SELECT queries are allowed"}
            
            result = self.session.execute(text(query))
            rows = result.fetchall()
            columns = result.keys()
            
            # Convert to list of dictionaries
            data = []
            for row in rows:
                data.append(dict(zip(columns, row)))
            
            return {"data": data, "columns": list(columns)}
            
        except Exception as e:
            return {"error": str(e)}
    
    def generate_response(self, user_query: str, chat_history: list = None):
        """Generate AI response for user query with conversation context"""
        
        schema_info = self.get_database_schema()
        
        system_prompt = f"""You are an expert business analyst for a Hungarian music instrument retail company. 
        You have access to an ERP database with the following schema:

        {json.dumps(schema_info, indent=2)}

        CRITICAL LANGUAGE RULES:
        - ALWAYS respond in the SAME LANGUAGE the user asks the question in
        - If user asks in Hungarian, respond completely in Hungarian
        - If user asks in English, respond in English
        - Maintain consistent language throughout your entire response

        HUNGARIAN DATABASE CONTEXT:
        - The database contains Hungarian product data, categories, and terminology
        - Product groups, categories, and descriptions are in Hungarian
        - When users mention English terms, automatically use Hungarian equivalents for database searches
        - Common product category translations:
          * drums → dob, dobszett, dobfelszerelés
          * guitar → gitár
          * piano → zongora, pianó
          * microphone → mikrofon
          * bass → basszus, basszusgitár
          * keyboard → billentyű, billentyűs hangszer
          * amplifier → erősítő
          * speaker → hangfal
        - Use your language knowledge to find appropriate Hungarian terms for any product category
        - Apply ILIKE '%hungarian_term%' pattern matching for flexible category searches
        
        BRAND NAME INTELLIGENCE - CRITICAL:
        - Brand names may have different cases in database vs user query
        - Common database variations: SHURE (uppercase), YAMAHA (check both), ROLAND (check both)
        - ALWAYS use case-insensitive matching: WHERE brand ILIKE '%brandname%'
        - Examples: "Shure mikrofon" → WHERE brand ILIKE '%shure%' AND product_group ILIKE '%mikrofon%'

        Your role is to:
        1. Understand business questions about sales, inventory, pricing, and competitors
        2. Generate appropriate SQL queries using Hungarian terminology for searches
        3. Analyze the data and provide clear, actionable insights in the user's language
        4. Format responses in a business-friendly way
        5. Maintain conversation context and remember previous questions/answers

        Important guidelines:
        - Always use SELECT queries only
        - Be precise with column names and table relationships
        - Consider date ranges when analyzing trends
        - Format large numbers clearly (use thousands separators)
        - When showing prices, remember they are in HUF
        - For competitor analysis, focus on meaningful price comparisons
        - Include relevant context about the music instrument industry
        - Remember previous queries and build on that context
        - When user mentions business categories (Kisker, Nagyker, Install, Export, Egyéb, Rental), use the mapping to query database categories with IN clause
        - Example: For "Kisker profit" use: WHERE buyer_category IN ('Kiskereskedelmi egység', 'Bolti vásárló')
        - CRITICAL: For ANY product-related queries, ALWAYS include: p.brand, p.product_id AS termékkód, p.product_name AS terméknév
        - CRITICAL: For product sales analysis, ALWAYS include quantity, revenue AND profit: SUM(s.quantity) AS total_quantity, SUM(s.quantity * s.unit_price) AS total_revenue, SUM((s.unit_price - s.purchase_unit_price) * s.quantity) AS total_profit
        - CRITICAL: When searching for products, try product_id (termékkód) FIRST with exact match, then product_name with ILIKE pattern
        - CRITICAL: For product group/category searches, use Hungarian terms with ILIKE pattern (e.g., WHERE product_group ILIKE '%dob%' for drums)
        - CRITICAL: For brand searches, ALWAYS use ILIKE for case-insensitive matching (e.g., WHERE brand ILIKE '%shure%' instead of brand = 'Shure')
        - CRITICAL: Common brand variations to handle: Shure/SHURE, Yamaha/YAMAHA, Roland/ROLAND, etc.
        - CRITICAL: Format all currency values with thousand separators, no decimals, and 'HUF' suffix (e.g., 1,234,567 HUF)
        - CRITICAL: DO NOT include SQL code in your response text - only provide the business analysis and insights
        - This is a PostgreSQL database - use PostgreSQL syntax:
          * Use EXTRACT(YEAR FROM date_column) instead of YEAR(date_column)
          * Use EXTRACT(MONTH FROM date_column) instead of MONTH(date_column)
          * Use DATE_TRUNC('month', date_column) for monthly grouping
          * Use LIKE for pattern matching with % wildcards
          * Use ILIKE for case-insensitive pattern matching

        SQL FORMATTING RULES:
        - ALWAYS wrap SQL queries in ```sql code blocks
        - Put ONLY the executable SQL query inside the code block
        - Do NOT include explanations or comments inside the SQL code block
        - Example format:
        
        Here's my analysis...
        
        ```sql
        SELECT column1, column2
        FROM table1
        WHERE condition = 'value'
        ```
        
        The query shows...
        """

        # Build conversation messages with history
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add chat history if provided
        if chat_history:
            for msg in chat_history:
                if msg["role"] == "user":
                    messages.append({"role": "user", "content": msg["content"]})
                elif msg["role"] == "assistant" and isinstance(msg["content"], dict):
                    # Extract just the response text for context
                    if "response" in msg["content"]:
                        messages.append({"role": "assistant", "content": msg["content"]["response"]})
                elif msg["role"] == "assistant":
                    messages.append({"role": "assistant", "content": str(msg["content"])})
        
        # Add current user query
        messages.append({"role": "user", "content": user_query})

        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o",
                messages=messages,
                temperature=0.1,
                max_tokens=2000
            )
            
            ai_response = response.choices[0].message.content
            
            # Check if the response contains SQL query
            if "SELECT" in ai_response.upper():
                # Extract SQL query from response - look for SQL code blocks first
                sql_query = ""
                clean_response = ai_response
                
                if "```sql" in ai_response.lower():
                    sql_start = ai_response.lower().find("```sql") + 6
                    sql_end = ai_response.find("```", sql_start)
                    if sql_end != -1:
                        sql_query = ai_response[sql_start:sql_end].strip()
                        # Remove the SQL block from the response
                        block_start = ai_response.lower().find("```sql")
                        block_end = ai_response.find("```", sql_start) + 3
                        clean_response = (ai_response[:block_start] + ai_response[block_end:]).strip()
                elif "```" in ai_response and "SELECT" in ai_response.upper():
                    # Look for any code block containing SELECT
                    parts = ai_response.split("```")
                    for i, part in enumerate(parts):
                        if "SELECT" in part.upper():
                            sql_query = part.strip()
                            # Reconstruct response without the SQL block
                            clean_parts = parts[:i] + parts[i+1:]
                            clean_response = "".join(clean_parts).strip()
                            break
                else:
                    # Fall back to finding SELECT statement directly
                    sql_start = ai_response.upper().find("SELECT")
                    sql_end = ai_response.find(";", sql_start)
                    if sql_end == -1:
                        # Look for double newline or end of query
                        remaining = ai_response[sql_start:]
                        lines = remaining.split('\n')
                        sql_lines = []
                        for line in lines:
                            line = line.strip()
                            if line and (line.upper().startswith(('SELECT', 'FROM', 'WHERE', 'GROUP', 'ORDER', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'AND', 'OR', 'HAVING', 'LIMIT')) or 
                                        any(keyword in line.upper() for keyword in ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'ORDER BY', 'JOIN', 'AND', 'OR'])):
                                sql_lines.append(line)
                            elif sql_lines and not line:
                                break
                            elif sql_lines and not any(keyword in line.upper() for keyword in ['SELECT', 'FROM', 'WHERE', 'GROUP', 'ORDER', 'JOIN', 'AND', 'OR', 'AS']):
                                break
                        sql_query = '\n'.join(sql_lines)
                        # Remove SQL from response
                        sql_block = '\n'.join(sql_lines)
                        clean_response = ai_response.replace(sql_block, "").strip()
                    else:
                        sql_query = ai_response[sql_start:sql_end]
                        clean_response = ai_response.replace(sql_query, "").strip()
                
                # Clean up the response text
                clean_response = clean_response.replace("Here's the SQL query:", "").strip()
                clean_response = clean_response.replace("SQL Query:", "").strip()
                clean_response = clean_response.replace("Query:", "").strip()
                # Remove any leftover empty lines
                clean_response = '\n'.join([line for line in clean_response.split('\n') if line.strip()])
                
                ai_response = clean_response
                
                # Execute the query
                query_result = self.execute_sql_query(sql_query.strip())
                
                return {
                    "response": ai_response,
                    "sql_query": sql_query.strip(),
                    "query_result": query_result
                }
            
            return {"response": ai_response}
            
        except Exception as e:
            return {"error": f"AI response error: {str(e)}"}

def format_dataframe_currencies(df):
    """Format currency columns in dataframe with proper HUF formatting"""
    df_formatted = df.copy()
    
    # Define currency column patterns
    currency_patterns = [
        'revenue', 'profit', 'price', 'cost', 'value', 'total_revenue', 
        'total_profit', 'total_cost', 'unit_price', 'purchase_price',
        'store_price', 'online_price', 'export_price', 'msrp'
    ]
    
    for col in df_formatted.columns:
        # Check if column name contains currency-related keywords
        if any(pattern in col.lower() for pattern in currency_patterns):
            # Convert to numeric if needed and format
            try:
                df_formatted[col] = pd.to_numeric(df_formatted[col], errors='coerce')
                df_formatted[col] = df_formatted[col].apply(
                    lambda x: format_currency(x) if pd.notna(x) else ""
                )
            except:
                continue
    
    return df_formatted

def create_chart_from_data(data, chart_type="bar", x_col=None, y_col=None, title=""):
    """Create plotly chart from query results"""
    if not data or len(data) == 0:
        return None
    
    df = pd.DataFrame(data)
    
    if chart_type == "bar" and x_col and y_col:
        fig = px.bar(df, x=x_col, y=y_col, title=title)
    elif chart_type == "line" and x_col and y_col:
        fig = px.line(df, x=x_col, y=y_col, title=title)
    elif chart_type == "pie" and x_col and y_col:
        fig = px.pie(df, names=x_col, values=y_col, title=title)
    else:
        # Default to showing the data as table
        return None
    
    return fig

def show_ai_analytics():
    """Main function to display AI Analytics page"""
    
    # Header
    st.title("🤖 AI Analytics Chat")
    st.markdown("Ask me anything about your business data - sales, inventory, pricing, competitors, and more!")
    
    # Initialize chat history
    if "chat_history" not in st.session_state:
        st.session_state.chat_history = []
    

    
    # Chat interface
    chat_container = st.container()
    
    # User input
    user_input = st.chat_input("Ask me about your business data...")
    
    if user_input:
        # Get AI response with existing chat history for context (excluding current input)
        with st.spinner("Analyzing your data..."):
            agent = ERPAnalyticsAgent()
            result = agent.generate_response(user_input, st.session_state.chat_history)
        
        # Add user message and AI response to history
        st.session_state.chat_history.append({"role": "user", "content": user_input})
        st.session_state.chat_history.append({"role": "assistant", "content": result})
    
    # Display chat history
    with chat_container:
        for i, message in enumerate(st.session_state.chat_history):
            if message["role"] == "user":
                with st.chat_message("user"):
                    st.write(message["content"])
            
            elif message["role"] == "assistant":
                with st.chat_message("assistant"):
                    result = message["content"]
                    
                    if isinstance(result, dict):
                        # Show AI response
                        if "response" in result:
                            st.write(result["response"])
                        
                        # Show SQL query if present
                        if "sql_query" in result:
                            with st.expander("🔍 SQL Query Used", expanded=False):
                                st.code(result["sql_query"], language="sql")
                        
                        # Show query results
                        if "query_result" in result and "data" in result["query_result"]:
                            data = result["query_result"]["data"]
                            if data:
                                st.subheader("📊 Results")
                                
                                # Show as table with currency formatting
                                df = pd.DataFrame(data)
                                df_formatted = format_dataframe_currencies(df)
                                st.dataframe(df_formatted, use_container_width=True)
                                
                                # Try to create a chart if appropriate
                                if len(df.columns) >= 2 and len(df) > 1 and len(df) <= 50:
                                    # Detect numeric columns for potential charts
                                    numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
                                    text_cols = df.select_dtypes(include=['object']).columns.tolist()
                                    
                                    if len(numeric_cols) >= 1 and len(text_cols) >= 1:
                                        col1, col2 = st.columns(2)
                                        with col1:
                                            chart_type = st.selectbox(
                                                "Chart Type", 
                                                ["bar", "line", "pie"], 
                                                key=f"chart_type_{i}"
                                            )
                                        with col2:
                                            show_chart = st.button("Create Chart", key=f"chart_btn_{i}")
                                        
                                        if show_chart:
                                            x_col = text_cols[0] if text_cols else df.columns[0]
                                            y_col = numeric_cols[0] if numeric_cols else df.columns[1]
                                            
                                            fig = create_chart_from_data(
                                                data, 
                                                chart_type=chart_type,
                                                x_col=x_col,
                                                y_col=y_col,
                                                title="Data Analysis Chart"
                                            )
                                            
                                            if fig:
                                                st.plotly_chart(fig, use_container_width=True)
                            else:
                                st.info("No data found for this query.")
                        
                        # Show errors
                        elif "query_result" in result and "error" in result["query_result"]:
                            st.error(f"Query Error: {result['query_result']['error']}")
                        
                        elif "error" in result:
                            st.error(f"Error: {result['error']}")
                    
                    else:
                        st.write(result)
    

    
    # Usage stats and conversation summary
    if len(st.session_state.chat_history) > 0:
        st.sidebar.markdown("---")
        st.sidebar.markdown("**Conversation Summary**")
        
        # Count user questions
        user_questions = len([msg for msg in st.session_state.chat_history if msg["role"] == "user"])
        st.sidebar.markdown(f"Questions asked: {user_questions}")
        
        # Show recent topics discussed
        if user_questions > 0:
            recent_topics = []
            for msg in st.session_state.chat_history[-6:]:  # Last 3 exchanges
                if msg["role"] == "user" and len(msg["content"]) > 10:
                    topic = msg["content"][:40] + "..." if len(msg["content"]) > 40 else msg["content"]
                    recent_topics.append(f"• {topic}")
            
            if recent_topics:
                st.sidebar.markdown("**Recent topics:**")
                for topic in recent_topics[-3:]:  # Show last 3 topics
                    st.sidebar.markdown(topic)
        
        # Estimate token usage (rough calculation)
        total_chars = sum(len(str(msg["content"])) for msg in st.session_state.chat_history)
        estimated_tokens = total_chars // 4  # Rough estimate: 4 chars per token
        estimated_cost = (estimated_tokens / 1000000) * 5  # $5 per 1M tokens for input
        
        st.sidebar.markdown(f"**Estimated Cost:** ${estimated_cost:.4f}")
        
        # Add conversation context indicator
        st.sidebar.success("✓ AI remembers this conversation")

if __name__ == "__main__":
    show_ai_analytics()