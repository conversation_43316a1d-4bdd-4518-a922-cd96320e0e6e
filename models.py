from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Date, ForeignKey, Table, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship
from database import Base
import datetime

# Association table for many-to-many relationship between Products and Categories
product_category_association = Table(
    'product_category_association', 
    Base.metadata,
    Column('product_id', String, ForeignKey('products.product_id')),
    Column('category_id', Integer, ForeignKey('product_categories.id'))
)

class Buyer(Base):
    """Model representing a buyer from the 'Vevők.xlsx' file."""
    __tablename__ = 'buyers'
    
    buyer_id = Column(String, primary_key=True)
    buyer_name = Column(String, nullable=False)
    country = Column(String)
    buyer_category = Column(String)
    web_access = Column(Boolean, default=False)
    address = Column(String)
    
    # Relationships
    sales = relationship("Sale", back_populates="buyer")
    
    def __repr__(self):
        return f"<Buyer(buyer_id='{self.buyer_id}', buyer_name='{self.buyer_name}')>"

class Product(Base):
    """Model representing a product from the 'Termékek.xlsx' file."""
    __tablename__ = 'products'
    
    product_id = Column(String, primary_key=True)
    product_name = Column(String, nullable=False)
    vendor_product_group = Column(String)
    product_group = Column(String)
    brand = Column(String)
    primary_supplier = Column(String)
    
    # Relationships
    categories = relationship("ProductCategory", secondary=product_category_association, back_populates="products")
    prices = relationship("Price", back_populates="product")
    stocks = relationship("Stock", back_populates="product")
    sales = relationship("Sale", back_populates="product")
    purchases = relationship("Purchase", back_populates="product")
    
    def __repr__(self):
        return f"<Product(product_id='{self.product_id}', product_name='{self.product_name}')>"

class ProductCategory(Base):
    """Model representing a product category from the 'Webes termékcsoportok.xlsx' file."""
    __tablename__ = 'product_categories'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    category_name = Column(String, nullable=False)
    
    # Relationships
    products = relationship("Product", secondary=product_category_association, back_populates="categories")
    
    def __repr__(self):
        return f"<ProductCategory(id={self.id}, category_name='{self.category_name}')>"

class Price(Base):
    """Model representing a price record from the 'Termék korábbi árak és készlet.xlsx' file."""
    __tablename__ = 'prices'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String, ForeignKey('products.product_id'), nullable=False)
    purchase_price = Column(Float)
    store_price = Column(Float)
    online_price = Column(Float)
    export_price = Column(Float)
    msrp = Column(Float)
    currency = Column(String, default="HUF")
    valid_from = Column(Date, default=datetime.datetime.now().date)
    
    # Relationships
    product = relationship("Product", back_populates="prices")
    
    def __repr__(self):
        return f"<Price(product_id='{self.product_id}', purchase_price={self.purchase_price})>"

class Stock(Base):
    """Model representing a stock record from the 'Termék készletérték (FIFO szerint).xlsx' file."""
    __tablename__ = 'stocks'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String, ForeignKey('products.product_id'), nullable=False)
    quantity = Column(Float)
    value = Column(Float)
    warehouse = Column(String)
    date_recorded = Column(Date, default=datetime.datetime.now().date)
    
    # Relationships
    product = relationship("Product", back_populates="stocks")
    
    def __repr__(self):
        return f"<Stock(product_id='{self.product_id}', quantity={self.quantity}, warehouse='{self.warehouse}')>"

class Sale(Base):
    """Model representing a sales record from the 'ELÁBÉ+.xlsx' file."""
    __tablename__ = 'sales'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    buyer_id = Column(String, ForeignKey('buyers.buyer_id'), nullable=False)
    product_id = Column(String, ForeignKey('products.product_id'), nullable=False)
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float)
    sale_date = Column(Date)
    invoice_id = Column(String)
    purchase_date = Column(Date)
    purchase_invoice_id = Column(String)
    purchase_unit_price = Column(Float)
    import_date = Column(DateTime, default=datetime.datetime.now)
    
    # Relationships
    buyer = relationship("Buyer", back_populates="sales")
    product = relationship("Product", back_populates="sales")
    
    def __repr__(self):
        return f"<Sale(buyer_id='{self.buyer_id}', product_id='{self.product_id}', quantity={self.quantity})>"

class Purchase(Base):
    """Model representing a purchase record from the 'Bejövő számlák tételes listája.xlsx' file."""
    __tablename__ = 'purchases'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    invoice_category = Column(String)
    invoice_id = Column(String)
    supplier_name = Column(String)
    product_id = Column(String, ForeignKey('products.product_id'), nullable=False)
    quantity = Column(Float)
    invoice_date = Column(Date)
    arrival_date = Column(Date)
    unit_price = Column(Float)
    currency = Column(String)
    total_value_huf = Column(Float)
    import_date = Column(DateTime, default=datetime.datetime.now)
    
    # Relationships
    product = relationship("Product", back_populates="purchases")
    
    def __repr__(self):
        return f"<Purchase(product_id='{self.product_id}', quantity={self.quantity}, supplier_name='{self.supplier_name}')>"

class CustomSetting(Base):
    """Model for custom settings like customs cost, shipping cost, etc."""
    __tablename__ = 'custom_settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    setting_type = Column(String, nullable=False)  # 'customs_cost', 'shipping_cost', etc.
    entity_type = Column(String, nullable=False)   # 'brand', 'region', etc.
    entity_name = Column(String, nullable=False)   # Brand name, region name, etc.
    value = Column(Float, nullable=True)  # For numeric values
    text_value = Column(String, nullable=True)  # For text values like category letters
    currency = Column(String, default="HUF")
    
    def __repr__(self):
        return f"<CustomSetting(setting_type='{self.setting_type}', entity_type='{self.entity_type}', entity_name='{self.entity_name}', value={self.value}, text_value='{self.text_value}')>"

class ExchangeRate(Base):
    """Model for storing currency exchange rates over time."""
    __tablename__ = 'exchange_rates'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    from_currency = Column(String, nullable=False)
    to_currency = Column(String, nullable=False)
    rate = Column(Float, nullable=False)
    valid_from = Column(Date, default=datetime.datetime.now().date)
    valid_to = Column(Date, nullable=True)
    is_current = Column(Boolean, default=True)
    
    # Create a unique index on from_currency, to_currency, and valid_from
    __table_args__ = (
        UniqueConstraint('from_currency', 'to_currency', 'valid_from', name='unique_rate_period'),
    )
    
    def __repr__(self):
        return f"<ExchangeRate({self.from_currency} → {self.to_currency}: {self.rate}, valid from {self.valid_from})>"

class User(Base):
    """Model for user authentication and role management."""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, nullable=False, unique=True)
    password_hash = Column(String, nullable=False)
    role = Column(String, nullable=False, default='user')  # 'admin' or 'user'
    created_date = Column(DateTime, default=datetime.datetime.now)
    last_login = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    
    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"

class CompetitorScrape(Base):
    """Model for storing competitor pricing data from web scraping."""
    __tablename__ = 'competitor_scrape'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_code = Column(String, nullable=False, unique=True)  # Termékkód - identifying field
    
    # Direct competitor URLs (existing)
    thomann_url = Column(String)
    thomann_price = Column(Float)
    thomann_stock = Column(String)  # Stock status (e.g., "In Stock", "Out of Stock", "5+ Available")
    muziker_url = Column(String)
    muziker_price = Column(Float)
    muziker_stock = Column(String)  # Stock status
    
    # Price comparison site URLs
    arukereso_url = Column(String)  # Árukereső URL - price comparison site
    argep_url = Column(String)  # Árgép URL - price comparison site
    
    # Additional competitors from price comparison sites
    r55_price = Column(Float)
    r55_stock = Column(String)
    kytary_price = Column(Float)
    kytary_stock = Column(String)
    mezzo_price = Column(Float)
    mezzo_stock = Column(String)
    allegro_price = Column(Float)
    allegro_stock = Column(String)
    pako_price = Column(Float)
    pako_stock = Column(String)
    mango_price = Column(Float)
    mango_stock = Column(String)
    plaza_price = Column(Float)
    plaza_stock = Column(String)
    diszkont_price = Column(Float)
    diszkont_stock = Column(String)
    hitspace_price = Column(Float)
    hitspace_stock = Column(String)
    
    import_date = Column(DateTime, default=datetime.datetime.now)
    last_scraped = Column(DateTime, nullable=True)  # When was this record last scraped
    
    def __repr__(self):
        return f"<CompetitorScrape(product_code={self.product_code}, thomann_price={self.thomann_price}, muziker_price={self.muziker_price})>"
