import pandas as pd
import numpy as np
import datetime
import os
import shutil
import streamlit as st
from database import get_session, close_session
from models import Buyer, Product, ProductCategory, Price, Stock, Sale, Purchase, CustomSetting, CompetitorScrape, ExchangeRate, product_category_association
from sqlalchemy import func, desc, and_, or_
from io import BytesIO, StringIO

def get_sample_excel_files():
    """Get a list of sample Excel files from the attached_assets directory."""
    sample_files = {}
    
    if os.path.exists('./attached_assets'):
        for filename in os.listdir('./attached_assets'):
            if filename.endswith('.xlsx') or filename.endswith('.xls'):
                file_path = os.path.join('./attached_assets', filename)
                
                # Determine file type based on filename according to specification
                if 'Vevők' in filename:
                    sample_files['buyers'] = file_path
                elif 'Termékek' in filename and 'korábbi' not in filename and 'k<PERSON>zlet' not in filename:
                    sample_files['products'] = file_path
                elif 'Webes termékcsoportok' in filename:
                    sample_files['product_categories'] = file_path
                elif 'korábbi <PERSON>' in filename and 'k<PERSON>zlet' in filename:
                    # This is the price file
                    sample_files['prices'] = file_path
                elif 'ELÁBÉ' in filename:
                    sample_files['sales'] = file_path
                elif 'Bejövő számlák' in filename:
                    sample_files['purchases'] = file_path
                
    return sample_files

def open_excel_file(file_path):
    """Open an Excel file from a path."""
    try:
        with open(file_path, 'rb') as file:
            return BytesIO(file.read())
    except Exception as e:
        st.error(f"Error opening Excel file: {str(e)}")
        return None

def parse_excel_data(file, file_type, selected_columns=None):
    """
    Parse uploaded Excel file based on its type.
    
    Args:
        file: The uploaded Excel file
        file_type: Type of data being imported (buyers, products, etc.)
        selected_columns: Dictionary mapping target column names to source column names from the file
    """
    try:
        df = pd.read_excel(file)
        return validate_excel_data(df, file_type, selected_columns)
    except Exception as e:
        st.error(f"Error parsing Excel file: {str(e)}")
        return None

def validate_excel_data(df, file_type, selected_columns=None):
    """
    Validate the Excel data based on expected columns for each file type.
    
    Args:
        df: DataFrame containing the Excel data
        file_type: Type of data being imported (buyers, products, etc.)
        selected_columns: A dictionary of selected columns to import (for flexible import)
    """
    # Dictionary of all expected columns
    expected_columns = {
        'buyers': ['Vevőkód', 'Vevőnév', 'Számlázási ország', 'Vevőcsoport', 'WEB hozzáférés'],
        'products': ['Termékkód', 'Terméknév', 'Gyártói csoport', 'Termékcsoport', 'Gyártó', 'Elsődleges szállító'],
        'product_categories': ['Termékkód', 'Webes termékcsoport'],
        'prices': ['Termékkód', 'Beszerzési ár', 'Bolti ár', 'Webes ár', 'Export ár', 'Lista ár'],
        'stock': ['Termékkód', 'Készlet', 'Készletérték', 'Raktár'],
        'sales': ['Vevőkód', 'Vevőnév', 'Termékkód', 'Mennyiség', 'Értékesítési egységár', 'Értékesítés kelte', 
                 'Értékesítési bizonylat', 'Beszerzés dátuma', 'Beszerzési bizonylat', 'Beszerzési egységár'],
        'purchases': ['Bizonylattömb', 'Bizonylatszám', 'Partner', 'Termékkód', 'Mennyiség', 'Kelt', 'Kézhezvétel',
                     'Nettó egységár', 'Pénznem', 'Nettó érték (HUF)'],
        'competitor_scrape': ['Termékkód', 'Thomann URL', 'Thomann Price', 'Muziker URL', 'Muziker Price', 
                              'Árukereső URL', 'Árgép URL', 'R55 Price', 'R55 Stock', 'Kytary Price', 'Kytary Stock',
                              'Mezzo Price', 'Mezzo Stock', 'Allegro Price', 'Allegro Stock', 'Páko Price', 'Páko Stock',
                              'Mango Price', 'Mango Stock', 'Pláza Price', 'Pláza Stock', 'Diszkont Price', 'Diszkont Stock',
                              'Hitspace Price', 'Hitspace Stock']
    }
    
    # Dictionary of required columns (identifiers) that must be present
    required_identifiers = {
        'buyers': ['Vevőkód'],
        'products': ['Termékkód'],
        'product_categories': ['Termékkód', 'Webes termékcsoport'],
        'prices': ['Termékkód'],
        'stock': ['Termékkód'],
        'sales': ['Vevőkód', 'Termékkód', 'Értékesítés kelte'],
        'purchases': ['Termékkód', 'Bizonylatszám', 'Kelt'],
        'competitor_scrape': ['Termékkód']
    }
    
    # For the flexible columns approach:
    if selected_columns is not None and file_type in ['buyers', 'products', 'prices', 'competitor_scrape']:
        # We're using the selected columns approach
        # First, make sure the required identifiers exist in the selected columns
        required_cols = required_identifiers.get(file_type, [])
        missing_identifiers = [col for col in required_cols if col not in df.columns]
        
        if missing_identifiers:
            st.error(f"Missing required identifier columns in {file_type} file: {', '.join(missing_identifiers)}")
            st.error(f"Available columns: {df.columns.tolist()}")
            return None
            
        # Create a new DataFrame with only the selected columns
        new_df = pd.DataFrame()
        
        # Always include the identifier columns
        for required_col in required_cols:
            if required_col in df.columns:
                new_df[required_col] = df[required_col]
        
        # Add the selected columns
        for target_col, source_col in selected_columns.items():
            if source_col and source_col in df.columns:
                new_df[target_col] = df[source_col]
            else:
                # Add empty column for selected fields that don't have a mapping
                new_df[target_col] = None
                
        # Apply data type validation to the new DataFrame
        return validate_and_convert_data_types(new_df, file_type)
        
    # Special handling for prices file which has a different format when not using selected columns
    if file_type == 'prices' and 'Termékkód' in df.columns and selected_columns is None:
        # Extract just the product code and price columns
        new_df = pd.DataFrame()
        new_df['Termékkód'] = df['Termékkód']
        
        # Map available price columns - search for relevant columns containing price terms
        price_terms = ['ár', 'price', 'costs']
        price_columns = [col for col in df.columns if any(term in col.lower() for term in price_terms)]
        
        # If we found price columns, use them - otherwise create empty columns for the expected price types
        if price_columns:
            # Try to map to our expected price types
            if any('beszerzési' in col.lower() for col in price_columns):
                new_df['Beszerzési ár'] = df[[col for col in price_columns if 'beszerzési' in col.lower()][0]]
            else:
                new_df['Beszerzési ár'] = None
                
            if any('bolti' in col.lower() for col in price_columns):
                new_df['Bolti ár'] = df[[col for col in price_columns if 'bolti' in col.lower()][0]]
            else:
                new_df['Bolti ár'] = None
                
            if any('webes' in col.lower() for col in price_columns):
                new_df['Webes ár'] = df[[col for col in price_columns if 'webes' in col.lower()][0]]
            else:
                new_df['Webes ár'] = None
                
            if any('export' in col.lower() for col in price_columns):
                new_df['Export ár'] = df[[col for col in price_columns if 'export' in col.lower()][0]]
            else:
                new_df['Export ár'] = None
                
            if any('lista' in col.lower() for col in price_columns):
                new_df['Lista ár'] = df[[col for col in price_columns if 'lista' in col.lower()][0]]
            else:
                new_df['Lista ár'] = None
            
            return new_df
        
        # If we don't have any price columns, try using numerical columns
        numerical_cols = df.select_dtypes(include=['number']).columns.tolist()
        if len(numerical_cols) >= 1:
            # We have at least one numeric column, use it as the purchase price
            new_df['Beszerzési ár'] = df[numerical_cols[0]]
            new_df['Bolti ár'] = df[numerical_cols[0]] * 1.5 if len(numerical_cols) == 1 else df[numerical_cols[1]]
            new_df['Webes ár'] = df[numerical_cols[0]] * 1.8 if len(numerical_cols) <= 2 else df[numerical_cols[2]]
            new_df['Export ár'] = df[numerical_cols[0]] * 1.6 if len(numerical_cols) <= 3 else df[numerical_cols[3]]
            new_df['Lista ár'] = df[numerical_cols[0]] * 2.0 if len(numerical_cols) <= 4 else df[numerical_cols[4]]
            
            return new_df
    
    # Standard validation for other file types or when not using selected columns
    required_columns = expected_columns.get(file_type, [])
    
    if not required_columns:
        st.error(f"Unknown file type: {file_type}")
        return None
    
    # For non-flexible imports, check if required columns exist
    if file_type not in ['buyers', 'products', 'prices', 'competitor_scrape'] or selected_columns is None:
        # Check if any required columns exist with flexible matching
        column_mapping = {}
        
        for required_col in required_columns:
            # First try exact match
            if required_col in df.columns:
                column_mapping[required_col] = required_col
                continue
                
            # Try case-insensitive match
            matches = [col for col in df.columns if col.lower() == required_col.lower()]
            if matches:
                column_mapping[required_col] = matches[0]
                continue
                
            # Try partial match (contains)
            matches = [col for col in df.columns if required_col.lower() in col.lower() or 
                      col.lower() in required_col.lower()]
            if matches:
                column_mapping[required_col] = matches[0]
                continue
        
        # For non-flexible imports, all columns must be present
        missing_columns = [col for col in required_columns if col not in column_mapping]
        
        if missing_columns:
            st.error(f"Missing columns in {file_type} file: {', '.join(missing_columns)}")
            st.error(f"Available columns: {df.columns.tolist()}")
            return None
        
        # If we had to remap any columns, create a new dataframe with the expected column names
        if len(column_mapping) != len(required_columns) or any(k != v for k, v in column_mapping.items()):
            new_df = pd.DataFrame()
            for target_col, source_col in column_mapping.items():
                new_df[target_col] = df[source_col]
            
            # Apply advanced data type validation
            return validate_and_convert_data_types(new_df, file_type)
    
    # All columns are already correct, but still need data type validation
    return validate_and_convert_data_types(df, file_type)

def validate_and_convert_data_types(df, file_type):
    """Validate and convert data types in the dataframe to ensure proper database import."""
    # Define expected numeric and date columns for each file type
    numeric_columns = {
        'prices': ['Beszerzési ár', 'Bolti ár', 'Webes ár', 'Export ár', 'Lista ár'],
        'stock': ['Készlet', 'Készletérték'],
        'sales': ['Mennyiség', 'Értékesítési egységár', 'Beszerzési egységár'],
        'purchases': ['Mennyiség', 'Nettó egységár', 'Nettó érték (HUF)'],
        'competitor_scrape': ['Thomann Price', 'Muziker Price']
    }
    
    date_columns = {
        'sales': ['Értékesítés kelte', 'Beszerzés dátuma'],
        'purchases': ['Kelt', 'Kézhezvétel']
    }
    
    # Define columns that should be boolean
    boolean_columns = {
        'buyers': ['WEB hozzáférés', 'Web Access', 'Web', 'Access', 'Hozzáférés', 'Online Access']
    }
    
    # No conversion needed for file types without special columns
    if file_type not in numeric_columns and file_type not in date_columns and file_type not in boolean_columns:
        return df
        
    # Create a copy to avoid modifying the original
    result_df = df.copy()
    
    # Process numeric columns
    if file_type in numeric_columns:
        for col in numeric_columns[file_type]:
            if col in result_df.columns:
                try:
                    # Check if the column already has a numeric data type
                    if not pd.api.types.is_numeric_dtype(result_df[col]):
                        # Try to convert to numeric, coercing errors to NaN
                        result_df[col] = pd.to_numeric(result_df[col], errors='coerce')
                        
                        # Count how many values were converted to NaN
                        nan_count = result_df[col].isna().sum()
                        if nan_count > 0:
                            st.warning(f"Column '{col}' contains {nan_count} values that could not be converted to numbers. These will be treated as zeros.")
                            # Replace NaN with 0 for database compatibility
                            result_df[col] = result_df[col].fillna(0)
                except Exception as e:
                    st.error(f"Error converting '{col}' to numeric: {str(e)}")
    
    # Process date columns
    if file_type in date_columns:
        for col in date_columns[file_type]:
            if col in result_df.columns:
                try:
                    # Skip if column is already a datetime type
                    if not pd.api.types.is_datetime64_any_dtype(result_df[col]):
                        # Try to convert to datetime, coercing errors to NaT
                        result_df[col] = pd.to_datetime(result_df[col], errors='coerce')
                        
                        # Count how many values were converted to NaT
                        nat_count = result_df[col].isna().sum()
                        if nat_count > 0:
                            st.warning(f"Column '{col}' contains {nat_count} values that could not be converted to dates. These records will have null dates.")
                except Exception as e:
                    st.error(f"Error converting '{col}' to date: {str(e)}")
    
    # Process boolean columns
    if file_type in boolean_columns:
        for col in boolean_columns[file_type]:
            if col in result_df.columns:
                try:
                    # Convert various values to boolean
                    if not pd.api.types.is_bool_dtype(result_df[col]):
                        # Create a temporary series for conversion
                        temp_series = result_df[col].astype(str).str.lower()
                        
                        # Map common values to True/False
                        true_values = ['igen', 'yes', 'true', '1', 'y', 't', 'i']
                        false_values = ['nem', 'no', 'false', '0', 'n', 'f', '-']
                        
                        # Create a boolean mask
                        bool_mask = pd.Series(False, index=temp_series.index)
                        
                        # Set True for all true_values
                        for val in true_values:
                            bool_mask = bool_mask | (temp_series == val)
                            
                        # Apply the mask to create the boolean column
                        result_df[col] = bool_mask
                        
                        st.info(f"Converted '{col}' to boolean values.")
                except Exception as e:
                    st.error(f"Error converting '{col}' to boolean: {str(e)}")
    
    return result_df

def save_buyers_data(df, import_mode="update"):
    """
    Save buyers data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing buyer data
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
    """
    session = get_session()
    if session:
        try:
            total_rows = len(df)
            count = 0
            batch_size = 100  # Process 100 buyers at a time
            
            # Delete all existing buyers if import_mode is 'delete'
            if import_mode == "delete":
                deletion_count = session.query(Buyer).delete()
                session.commit()
                st.info(f"Deleted {deletion_count} existing buyer records.")
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Processing buyers... 0/{total_rows} (0%)")
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        buyer = Buyer(
                            buyer_id=row['Vevőkód'],
                            buyer_name=row['Vevőnév'],
                            country=row['Számlázási ország'] if pd.notna(row['Számlázási ország']) else None,
                            buyer_category=row['Vevőcsoport'] if pd.notna(row['Vevőcsoport']) else None,
                            web_access=True if row['WEB hozzáférés'] == 'IGAZ' else False,
                            address=row['Számlázási cím'] if pd.notna(row['Számlázási cím']) else None
                        )
                        
                        # Check if buyer already exists
                        existing_buyer = session.query(Buyer).filter_by(buyer_id=buyer.buyer_id).first()
                        
                        if existing_buyer:
                            if import_mode == "update":
                                # Update existing buyer
                                existing_buyer.buyer_name = buyer.buyer_name
                                existing_buyer.country = buyer.country
                                existing_buyer.buyer_category = buyer.buyer_category
                                existing_buyer.web_access = buyer.web_access
                                existing_buyer.address = buyer.address
                                count += 1
                            elif import_mode == "disregard":
                                # Skip this buyer as it already exists
                                pass
                            # Note: If import_mode is delete, we've already deleted all buyers
                        else:
                            # Add new buyer
                            session.add(buyer)
                            count += 1
                    except Exception as row_error:
                        st.warning(f"Error with buyer {row.get('Vevőkód', 'unknown')}: {str(row_error)}")
                        continue
                
                # Commit each batch to save progress
                session.commit()
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing buyers... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            
            if import_mode == "update":
                status_text.text(f"Processed {count} buyers successfully (new + updated)!")
            elif import_mode == "disregard":
                status_text.text(f"Added {count} new buyers successfully (existing buyers were skipped)!")
            else:  # delete
                status_text.text(f"Imported {count} buyers successfully (all previous buyers were deleted)!")
            
            return count
            
        except Exception as e:
            session.rollback()
            st.error(f"Error saving buyers data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_products_data(df, import_mode="update"):
    """
    Save products data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing product data
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
    """
    session = get_session()
    if session:
        try:
            total_rows = len(df)
            count = 0
            batch_size = 100  # Process 100 products at a time
            
            # Delete all existing products if import_mode is 'delete'
            if import_mode == "delete":
                deletion_count = session.query(Product).delete()
                session.commit()
                st.info(f"Deleted {deletion_count} existing product records.")
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Processing products... 0/{total_rows} (0%)")
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        product = Product(
                            product_id=row['Termékkód'],
                            product_name=row['Terméknév'],
                            vendor_product_group=row['Gyártói csoport'] if pd.notna(row['Gyártói csoport']) else None,
                            product_group=row['Termékcsoport'] if pd.notna(row['Termékcsoport']) else None,
                            brand=row['Gyártó'] if pd.notna(row['Gyártó']) else None,
                            primary_supplier=row['Elsődleges szállító'] if pd.notna(row['Elsődleges szállító']) else None
                        )
                        
                        # Check if product already exists
                        existing_product = session.query(Product).filter_by(product_id=product.product_id).first()
                        
                        if existing_product:
                            if import_mode == "update":
                                # Update existing product with cleaned data
                                existing_product.product_name = product.product_name
                                existing_product.vendor_product_group = product.vendor_product_group
                                existing_product.product_group = product.product_group
                                existing_product.brand = product.brand
                                existing_product.primary_supplier = product.primary_supplier
                                count += 1
                            elif import_mode == "disregard":
                                # Skip this product as it already exists
                                pass
                            # Note: If import_mode is delete, we've already deleted all products
                        else:
                            # Add new product
                            session.add(product)
                            count += 1
                    except Exception as row_error:
                        st.warning(f"Error with product {row.get('Termékkód', 'unknown')}: {str(row_error)}")
                        continue
                
                # Commit each batch to save progress
                session.commit()
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing products... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            
            if import_mode == "update":
                status_text.text(f"Processed {count} products successfully (new + updated)!")
            elif import_mode == "disregard":
                status_text.text(f"Added {count} new products successfully (existing products were skipped)!")
            else:  # delete
                status_text.text(f"Imported {count} products successfully (all previous products were deleted)!")
            
            return count
            
        except Exception as e:
            session.rollback()
            st.error(f"Error saving products data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_product_categories_data(df, import_mode="update"):
    """
    Save product categories data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing product category data
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
                     'update': For each product ID, delete all existing categories and add the new ones
                     'disregard': Keep existing categories, only add new ones
                     'delete': Remove ALL existing category connections before importing new data
    """
    session = get_session()
    if session:
        try:
            total_rows = len(df)
            count = 0
            batch_size = 100  # Process 100 records at a time
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Processing product categories... 0/{total_rows} (0%)")
            
            # Track products we've seen for update mode
            processed_product_ids = set()
            
            # Special handling for delete mode - clear all existing category relations
            if import_mode == "delete":
                try:
                    # Get the product_category_association table
                    product_category_table = product_category_association
                    
                    # Delete all associations
                    delete_stmt = product_category_table.delete()
                    session.execute(delete_stmt)
                    session.commit()
                    st.info("🗑️ All existing product category associations have been deleted.")
                except Exception as e:
                    session.rollback()
                    st.error(f"Error deleting existing product categories: {str(e)}")
                    return 0
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        # Get the columns for product ID and category name
                        # Try both possible names for category column
                        try:
                            product_id = row['Termékkód']
                        except:
                            st.error("Cannot find 'Termékkód' column in the data.")
                            break
                            
                        try:
                            # Prioritize 'Webes termékcsoport' over other categories
                            if 'Webes termékcsoport' in row:
                                category_name = row['Webes termékcsoport']
                            elif 'Kategória' in row:
                                category_name = row['Kategória']
                            else:
                                # Try to find a column with 'termékcsoport', 'kategoria', or 'category' in the name
                                category_col = None
                                for col in row.index:
                                    if 'termékcsop' in col.lower() or 'termekcsop' in col.lower():
                                        category_col = col
                                        break
                                
                                # If no web category found, look for regular category names
                                if not category_col:
                                    for col in row.index:
                                        if 'kategoria' in col.lower() or 'category' in col.lower() or 'kat' in col.lower():
                                            category_col = col
                                            break
                                
                                if category_col:
                                    category_name = row[category_col]
                                else:
                                    st.error("Cannot identify a category column in the data.")
                                    break
                        except Exception as col_error:
                            st.error(f"Error finding category column: {str(col_error)}")
                            break
                        
                        if pd.isna(product_id) or pd.isna(category_name):
                            continue
                            
                        # Check if product exists
                        product = session.query(Product).filter_by(product_id=product_id).first()
                        if not product:
                            st.warning(f"Skipping category association for unknown product: {product_id}")
                            continue
                        
                        # For update mode, if this is the first time we're seeing this product,
                        # remove all existing category associations
                        if import_mode == "update" and product_id not in processed_product_ids:
                            product.categories.clear()
                            processed_product_ids.add(product_id)
                        
                        # Check if category exists
                        category = session.query(ProductCategory).filter_by(category_name=category_name).first()
                        if not category:
                            category = ProductCategory(category_name=category_name)
                            session.add(category)
                            session.flush()  # Generate ID for new category
                        
                        # In disregard mode, only add if not already present
                        # In update mode or delete mode, always add (since we've already cleared associations if needed)
                        if import_mode == "disregard":
                            if category not in product.categories:
                                product.categories.append(category)
                                count += 1
                        else:
                            product.categories.append(category)
                            count += 1
                    except Exception as row_error:
                        st.warning(f"Error with product category {row.get('Termékkód', 'unknown')}: {str(row_error)}")
                        continue
                
                # Commit each batch to save progress
                session.commit()
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing product categories... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            
            # Prepare success message based on import mode
            if import_mode == "update":
                message = f"✅ Processed {count} product category connections ({len(processed_product_ids)} products updated) successfully!"
            elif import_mode == "delete":
                message = f"✅ All existing product category connections were deleted. {count} new connections were added successfully!"
            else:  # disregard mode
                message = f"✅ Added {count} new product category connections successfully!"
                
            status_text.text(message)
            return count
        
        except Exception as e:
            session.rollback()
            st.error(f"Error saving product categories data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_prices_data(df, currency, import_mode="update"):
    """
    Save prices data to the database with batching and progress indicators.
    Optimized with validation caching for better performance.
    
    Args:
        df: DataFrame containing price data
        currency: Currency for the price data (e.g., 'HUF', 'EUR')
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
    """
    session = get_session()
    if session:
        try:
            total_rows = len(df)
            count = 0
            batch_size = 500  # Process 500 prices at a time for better performance
            today = datetime.datetime.now().date()
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Building validation cache...")
            
            # Build product validation cache once (major performance optimization)
            product_cache = {}
            try:
                products = session.query(Product).all()
                for product in products:
                    product_cache[product.product_id] = product
                status_text.text(f"Cached {len(product_cache)} products for validation")
            except Exception as e:
                st.error(f"Failed to build product cache: {str(e)}")
                return 0
            
            # Build existing prices cache for update mode
            existing_prices_cache = {}
            if import_mode == "update":
                try:
                    existing_prices = session.query(Price).all()
                    for price in existing_prices:
                        key = (price.product_id, price.valid_from)
                        existing_prices_cache[key] = price
                    status_text.text(f"Cached {len(existing_prices_cache)} existing price records")
                except Exception as e:
                    st.warning(f"Failed to build existing prices cache: {str(e)}")
            
            status_text.text(f"Processing prices... 0/{total_rows} (0%)")
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        product_id = row['Termékkód']
                        
                        # Use cache for product validation (eliminates database query)
                        if product_id not in product_cache:
                            continue  # Skip unknown products silently to avoid spam
                        
                        # Extract price values with safety checks
                        purchase_price = row.get('Beszerzési ár') if 'Beszerzési ár' in row and pd.notna(row['Beszerzési ár']) else None
                        store_price = row.get('Bolti ár') if 'Bolti ár' in row and pd.notna(row['Bolti ár']) else None
                        online_price = row.get('Webes ár') if 'Webes ár' in row and pd.notna(row['Webes ár']) else None
                        export_price = row.get('Export ár') if 'Export ár' in row and pd.notna(row['Export ár']) else None
                        msrp = row.get('Lista ár') if 'Lista ár' in row and pd.notna(row['Lista ár']) else None
                        
                        # Ensure at least one price column has a value
                        if all(price is None for price in [purchase_price, store_price, online_price, export_price, msrp]):
                            continue  # Skip records with no valid price values
                        
                        # Handle existing price records for update mode
                        price_key = (product_id, today)
                        if import_mode == "update" and price_key in existing_prices_cache:
                            # Update existing price record
                            existing_price = existing_prices_cache[price_key]
                            if purchase_price is not None:
                                existing_price.purchase_price = purchase_price
                            if store_price is not None:
                                existing_price.store_price = store_price
                            if online_price is not None:
                                existing_price.online_price = online_price
                            if export_price is not None:
                                existing_price.export_price = export_price
                            if msrp is not None:
                                existing_price.msrp = msrp
                            existing_price.currency = currency
                        elif import_mode == "disregard" and price_key in existing_prices_cache:
                            continue  # Skip existing records in disregard mode
                        else:
                            # Create new price record
                            price = Price(
                                product_id=product_id,
                                purchase_price=purchase_price,
                                store_price=store_price,
                                online_price=online_price,
                                export_price=export_price,
                                msrp=msrp,
                                currency=currency,
                                valid_from=today
                            )
                            session.add(price)
                        
                        count += 1
                    except Exception as row_error:
                        continue  # Skip problematic rows silently to avoid spam
                
                # Commit each batch to save progress
                try:
                    session.commit()
                except Exception as commit_error:
                    session.rollback()
                    st.error(f"Error committing batch {batch_start}-{batch_end}: {str(commit_error)}")
                    continue
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing prices... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            status_text.text(f"Completed! Processed {count} price records with validation caching")
            return count
            
        except Exception as e:
            session.rollback()
            st.error(f"Error saving prices data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_stock_data(df, import_mode="update"):
    """
    Save stock data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing stock data
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
    """
    session = get_session()
    if session:
        try:
            total_rows = len(df)
            count = 0
            batch_size = 100  # Process 100 records at a time
            today = datetime.datetime.now().date()
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Processing stock data... 0/{total_rows} (0%)")
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        product_id = row['Termékkód']
                        
                        if pd.isna(product_id):
                            continue
                            
                        # Check if product exists
                        product = session.query(Product).filter_by(product_id=product_id).first()
                        if not product:
                            st.warning(f"Skipping stock for unknown product: {product_id}")
                            continue
                        
                        stock = Stock(
                            product_id=product_id,
                            quantity=row['Készlet'] if pd.notna(row['Készlet']) else 0,
                            value=row['Készletérték'] if pd.notna(row['Készletérték']) else 0,
                            warehouse=row['Raktár'] if pd.notna(row['Raktár']) else 'Main',
                            date_recorded=today
                        )
                        
                        session.add(stock)
                        count += 1
                    except Exception as row_error:
                        st.warning(f"Error with stock for product {row.get('Termékkód', 'unknown')}: {str(row_error)}")
                        continue
                
                # Commit each batch to save progress
                session.commit()
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing stock data... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            status_text.text(f"Completed processing {count} stock records!")
            return count
        except Exception as e:
            session.rollback()
            st.error(f"Error saving stock data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_sales_data(df, import_mode="date_replace"):
    """
    Save sales data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing sales data
        import_mode: How to handle existing records - 'update', 'disregard', 'delete', or 'date_replace' (default)
                     For sales, 'date_replace' is the preferred option which replaces all records on specific dates.
    """
    session = get_session()
    if session:
        try:
            # Make sure there are no summary rows (rows without a Termékkód)
            if 'Termékkód' in df.columns:
                df = df.dropna(subset=['Termékkód'])
                
            total_rows = len(df)
            count = 0
            batch_size = 500  # Process 500 records at a time for better performance
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Processing sales data... 0/{total_rows} (0%)")
            
            # Dictionary to store dates that have been processed (deleted but maybe not imported yet)
            processed_dates = {}
            
            # For date_replace mode, collect all unique sale dates and handle them more carefully
            if import_mode == "date_replace" and 'Értékesítés kelte' in df.columns:
                try:
                    # Create a session state if it doesn't exist to track processed dates across page refreshes
                    if 'sales_processed_dates' not in st.session_state:
                        st.session_state.sales_processed_dates = {}
                    
                    st.write("DEBUG: Starting date_replace mode processing")
                    # Get all unique dates in the file
                    non_null_dates = df['Értékesítés kelte'].dropna()
                    st.write(f"DEBUG: Found {len(non_null_dates)} non-null dates")
                    
                    # Try to convert to datetime safely
                    try:
                        unique_dates = pd.to_datetime(non_null_dates, errors='coerce').dt.date.unique()
                        st.write(f"DEBUG: Parsed {len(unique_dates)} unique dates")
                        for d in unique_dates[:5]:  # Show first 5 dates for debugging
                            st.write(f"DEBUG: Sample date: {d}")
                    except Exception as conv_error:
                        st.error(f"DEBUG: Date conversion error: {str(conv_error)}")
                        # Fallback - try to manually parse dates
                        unique_dates = []
                    
                    if len(unique_dates) > 0:
                        st.write("DEBUG: Starting to delete records for dates")
                        # Delete existing records for these dates
                        delete_count = 0
                        
                        # Create lookup dict for faster checks
                        date_lookup = {date.isoformat(): date for date in unique_dates}
                        
                        # First check if any dates were already processed in a previous interrupted session
                        already_deleted = 0
                        newly_deleted = 0
                        
                        for date_str, date_obj in date_lookup.items():
                            # Skip dates that have already been fully processed (deleted and imported)
                            if date_str in st.session_state.sales_processed_dates and st.session_state.sales_processed_dates[date_str] == 'complete':
                                continue
                                
                            try:
                                # Check if we need to delete records for this date
                                if date_str not in st.session_state.sales_processed_dates or st.session_state.sales_processed_dates[date_str] != 'deleted':
                                    # Show the SQL query we're trying to execute
                                    query_text = str(session.query(Sale).filter(func.date(Sale.sale_date) == date_obj))
                                    st.write(f"DEBUG: Executing: {query_text}")
                                    
                                    # Convert date to SQL date and delete
                                    results = session.query(Sale).filter(func.date(Sale.sale_date) == date_obj).delete()
                                    delete_count += results
                                    newly_deleted += results
                                    st.write(f"DEBUG: Deleted {results} records for date {date_obj}")
                                    
                                    # Mark this date as deleted in the session state
                                    st.session_state.sales_processed_dates[date_str] = 'deleted'
                                else:
                                    # This date was already deleted in a previous session
                                    already_deleted += 1
                                    st.write(f"DEBUG: Skipping deletion for already processed date {date_obj}")
                                
                                # Store in our local tracking dictionary too
                                processed_dates[date_str] = 'deleted'
                                
                            except Exception as date_error:
                                st.error(f"Error deleting sales for date {date_obj}: {str(date_error)}")
                                import traceback
                                st.error(f"Error details: {traceback.format_exc()}")
                        
                        # Commit the deletions
                        st.write("DEBUG: Committing deletions")
                        session.commit()
                        
                        if already_deleted > 0:
                            st.info(f"Skipped {already_deleted} dates that were already processed in a previous session.")
                            
                        if newly_deleted > 0:
                            st.info(f"Removed {delete_count} existing sales records for {newly_deleted} dates in this import.")
                            
                        st.write("DEBUG: Date replace processing complete")
                except Exception as date_error:
                    session.rollback()
                    st.error(f"Error handling dates for date_replace mode: {str(date_error)}")
                    import traceback
                    st.error(f"Error details: {traceback.format_exc()}")
            
            # Pre-load all buyers and products into cache for fast validation
            status_text.text("Building validation cache...")
            
            # Load all buyers into a dictionary for fast lookup
            buyers_cache = {}
            for buyer in session.query(Buyer).all():
                buyers_cache[buyer.buyer_id] = buyer
            
            # Load all products into a dictionary for fast lookup
            products_cache = {}
            for product in session.query(Product).all():
                products_cache[product.product_id] = product
                
            st.info(f"Loaded {len(buyers_cache)} buyers and {len(products_cache)} products into validation cache")
            
            # Process sales records in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                batch_count = 0
                for _, row in batch.iterrows():
                    try:
                        buyer_id = row['Vevőkód']
                        product_id = row['Termékkód']
                        
                        if pd.isna(buyer_id) or pd.isna(product_id):
                            continue
                            
                        # Check if buyer and product exist using cache
                        buyer = buyers_cache.get(buyer_id)
                        product = products_cache.get(product_id)
                        
                        if not buyer or not product:
                            st.warning(f"Skipping sale for unknown buyer ({buyer_id}) or product ({product_id})")
                            continue
                        
                        # Parse dates
                        sale_date = None
                        if pd.notna(row['Értékesítés kelte']):
                            if isinstance(row['Értékesítés kelte'], (datetime.datetime, datetime.date)):
                                sale_date = row['Értékesítés kelte'].date() if isinstance(row['Értékesítés kelte'], datetime.datetime) else row['Értékesítés kelte']
                            else:
                                try:
                                    sale_date = pd.to_datetime(row['Értékesítés kelte']).date()
                                except Exception:
                                    pass
                        
                        purchase_date = None
                        if pd.notna(row['Beszerzés dátuma']):
                            if isinstance(row['Beszerzés dátuma'], (datetime.datetime, datetime.date)):
                                purchase_date = row['Beszerzés dátuma'].date() if isinstance(row['Beszerzés dátuma'], datetime.datetime) else row['Beszerzés dátuma']
                            else:
                                try:
                                    purchase_date = pd.to_datetime(row['Beszerzés dátuma']).date()
                                except Exception:
                                    pass
                        
                        sale = Sale(
                            buyer_id=buyer_id,
                            product_id=product_id,
                            quantity=row['Mennyiség'] if pd.notna(row['Mennyiség']) else 0,
                            unit_price=row['Értékesítési egységár'] if pd.notna(row['Értékesítési egységár']) else 0,
                            sale_date=sale_date,
                            invoice_id=row['Értékesítési bizonylat'] if pd.notna(row['Értékesítési bizonylat']) else None,
                            purchase_date=purchase_date,
                            purchase_invoice_id=row['Beszerzési bizonylat'] if pd.notna(row['Beszerzési bizonylat']) else None,
                            purchase_unit_price=row['Beszerzési egységár'] if pd.notna(row['Beszerzési egységár']) else 0
                        )
                        
                        session.add(sale)
                        count += 1
                        batch_count += 1
                        
                        # Track this date as fully processed
                        if sale_date and import_mode == "date_replace":
                            date_str = sale_date.isoformat()
                            processed_dates[date_str] = 'complete'
                            if 'sales_processed_dates' in st.session_state:
                                st.session_state.sales_processed_dates[date_str] = 'complete'
                            
                    except Exception as row_error:
                        st.warning(f"Error with sale for buyer {row.get('Vevőkód', 'unknown')} and product {row.get('Termékkód', 'unknown')}: {str(row_error)}")
                        continue
                
                # Commit each batch to save progress
                if batch_count > 0:
                    session.commit()
                    st.write(f"DEBUG: Committed batch with {batch_count} records")
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing sales data... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            status_text.text(f"Completed processing {count} sales records!")
            
            # Clear the session state tracking for completed imports
            if 'sales_processed_dates' in st.session_state and count > 0:
                st.session_state.sales_processed_dates = {}
                st.write("DEBUG: Cleared session state tracking as import completed successfully")
                
            return count
            
        except Exception as e:
            session.rollback()
            st.error(f"Error saving sales data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_purchases_data(df, import_mode="date_replace"):
    """
    Save purchases data to the database with batching and progress indicators.
    Optimized with validation caching for better performance.
    
    Args:
        df: DataFrame containing purchase data
        import_mode: How to handle existing records - 'update', 'disregard', 'delete', or 'date_replace' (default)
                     For purchases, 'date_replace' is the preferred option which replaces all records on specific dates.
    """
    session = get_session()
    if session:
        try:
            # Make sure there are no summary rows (rows without a Termékkód)
            if 'Termékkód' in df.columns:
                df = df.dropna(subset=['Termékkód'])
                
            total_rows = len(df)
            count = 0
            batch_size = 500  # Process 500 records at a time for better performance
            
            # Create a progress bar
            progress_bar = st.progress(0)
            status_text = st.empty()
            status_text.text(f"Building validation cache...")
            
            # Build product validation cache once (major performance optimization)
            product_cache = {}
            try:
                products = session.query(Product).all()
                for product in products:
                    product_cache[product.product_id] = product
                status_text.text(f"Cached {len(product_cache)} products for validation")
            except Exception as e:
                st.error(f"Failed to build product cache: {str(e)}")
                return 0
            
            status_text.text(f"Processing purchase data... 0/{total_rows} (0%)")
            
            # For date_replace mode, collect all unique purchase dates and delete existing records for those dates
            if import_mode == "date_replace" and 'Kelt' in df.columns:
                try:
                    # Get all unique invoice dates in the file
                    non_null_dates = df['Kelt'].dropna()
                    
                    # Try to convert to datetime safely
                    try:
                        unique_dates = pd.to_datetime(non_null_dates, errors='coerce').dt.date.unique()
                    except Exception as conv_error:
                        # Fallback - try to manually parse dates
                        unique_dates = []
                    
                    if len(unique_dates) > 0:
                        # Delete existing records for these dates
                        delete_count = 0
                        for date in unique_dates:
                            try:
                                # Delete records with matching invoice date
                                results = session.query(Purchase).filter(func.date(Purchase.invoice_date) == date).delete()
                                delete_count += results
                            except Exception as date_error:
                                st.error(f"Error deleting purchases for date {date}: {str(date_error)}")
                        
                        # Commit the deletions
                        session.commit()
                        if delete_count > 0:
                            st.info(f"Removed {delete_count} existing purchase records for the dates in this import.")
                except Exception as date_error:
                    session.rollback()
                    st.error(f"Error handling dates for date_replace mode: {str(date_error)}")
            
            # Process in batches
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch = df.iloc[batch_start:batch_end]
                
                for _, row in batch.iterrows():
                    try:
                        product_id = row['Termékkód']
                        
                        if pd.isna(product_id):
                            continue
                            
                        # Use cache for product validation (eliminates database query)
                        if product_id not in product_cache:
                            continue  # Skip unknown products silently to avoid spam
                        
                        # Parse dates
                        invoice_date = None
                        if pd.notna(row['Kelt']):
                            if isinstance(row['Kelt'], (datetime.datetime, datetime.date)):
                                invoice_date = row['Kelt'].date() if isinstance(row['Kelt'], datetime.datetime) else row['Kelt']
                            else:
                                try:
                                    invoice_date = pd.to_datetime(row['Kelt']).date()
                                except Exception:
                                    pass
                        
                        arrival_date = None
                        if pd.notna(row['Kézhezvétel']):
                            if isinstance(row['Kézhezvétel'], (datetime.datetime, datetime.date)):
                                arrival_date = row['Kézhezvétel'].date() if isinstance(row['Kézhezvétel'], datetime.datetime) else row['Kézhezvétel']
                            else:
                                try:
                                    arrival_date = pd.to_datetime(row['Kézhezvétel']).date()
                                except Exception:
                                    pass
                        
                        purchase = Purchase(
                            invoice_category=row['Bizonylattömb'] if pd.notna(row['Bizonylattömb']) else None,
                            invoice_id=row['Bizonylatszám'] if pd.notna(row['Bizonylatszám']) else None,
                            supplier_name=row['Partner'] if pd.notna(row['Partner']) else None,
                            product_id=product_id,
                            quantity=row['Mennyiség'] if pd.notna(row['Mennyiség']) else 0,
                            invoice_date=invoice_date,
                            arrival_date=arrival_date,
                            unit_price=row['Nettó egységár'] if pd.notna(row['Nettó egységár']) else 0,
                            currency=row['Pénznem'] if pd.notna(row['Pénznem']) else 'HUF',
                            total_value_huf=row['Nettó érték (HUF)'] if pd.notna(row['Nettó érték (HUF)']) else 0
                        )
                        
                        session.add(purchase)
                        count += 1
                    except Exception as row_error:
                        continue  # Skip problematic rows silently to avoid spam
                
                # Commit each batch to save progress
                try:
                    session.commit()
                except Exception as commit_error:
                    session.rollback()
                    st.error(f"Error committing batch {batch_start}-{batch_end}: {str(commit_error)}")
                    continue
                
                # Update progress
                progress = int(batch_end / total_rows * 100)
                progress_bar.progress(progress)
                status_text.text(f"Processing purchase data... {batch_end}/{total_rows} ({progress}%)")
            
            # Final progress update
            progress_bar.progress(100)
            status_text.text(f"Completed! Processed {count} purchase records with validation caching")
            return count
        
        except Exception as e:
            session.rollback()
            st.error(f"Error saving purchases data: {str(e)}")
            import traceback
            st.error(f"Error details: {traceback.format_exc()}")
            return 0
        finally:
            close_session(session)
    return 0

def save_competitor_scrape_data(df, import_mode="update"):
    """
    Save competitor scrape data to the database with batching and progress indicators.
    
    Args:
        df: DataFrame containing competitor scrape data
        import_mode: How to handle existing records - 'update', 'disregard', or 'delete'
    """

    
    session = get_session()
    if not session:
        st.error("Failed to connect to database")
        return 0
    
    try:
        # Handle import modes
        if import_mode == "delete":
            # Delete all existing competitor scrape data
            session.query(CompetitorScrape).delete()
            st.info("Deleted all existing competitor scrape data")
        
        total_rows = len(df)
        batch_size = 100
        count = 0
        
        # Create progress indicators
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Process in batches
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = df.iloc[batch_start:batch_end]
            
            for _, row in batch_df.iterrows():
                try:
                    product_code = row.get('Termékkód')
                    if pd.isna(product_code) or not product_code:
                        continue
                    
                    # Convert product code to string to handle number/text type issues
                    product_code = str(product_code).strip()
                    
                    # Check if record already exists
                    existing = session.query(CompetitorScrape).filter_by(product_code=product_code).first()
                    
                    if existing:
                        if import_mode == "disregard":
                            continue  # Skip existing records
                        elif import_mode == "update":
                            # Update existing record with all new competitor fields
                            existing.thomann_url = row.get('Thomann URL') if pd.notna(row.get('Thomann URL')) else existing.thomann_url
                            existing.thomann_price = row.get('Thomann Price') if pd.notna(row.get('Thomann Price')) else existing.thomann_price
                            existing.muziker_url = row.get('Muziker URL') if pd.notna(row.get('Muziker URL')) else existing.muziker_url
                            existing.muziker_price = row.get('Muziker Price') if pd.notna(row.get('Muziker Price')) else existing.muziker_price
                            
                            # Update price comparison site URLs
                            existing.arukereso_url = row.get('Árukereső URL') if pd.notna(row.get('Árukereső URL')) else existing.arukereso_url
                            existing.argep_url = row.get('Árgép URL') if pd.notna(row.get('Árgép URL')) else existing.argep_url
                            
                            # Update all new competitor prices and stocks
                            existing.r55_price = row.get('R55 Price') if pd.notna(row.get('R55 Price')) else existing.r55_price
                            existing.r55_stock = row.get('R55 Stock') if pd.notna(row.get('R55 Stock')) else existing.r55_stock
                            existing.kytary_price = row.get('Kytary Price') if pd.notna(row.get('Kytary Price')) else existing.kytary_price
                            existing.kytary_stock = row.get('Kytary Stock') if pd.notna(row.get('Kytary Stock')) else existing.kytary_stock
                            existing.mezzo_price = row.get('Mezzo Price') if pd.notna(row.get('Mezzo Price')) else existing.mezzo_price
                            existing.mezzo_stock = row.get('Mezzo Stock') if pd.notna(row.get('Mezzo Stock')) else existing.mezzo_stock
                            existing.allegro_price = row.get('Allegro Price') if pd.notna(row.get('Allegro Price')) else existing.allegro_price
                            existing.allegro_stock = row.get('Allegro Stock') if pd.notna(row.get('Allegro Stock')) else existing.allegro_stock
                            existing.pako_price = row.get('Páko Price') if pd.notna(row.get('Páko Price')) else existing.pako_price
                            existing.pako_stock = row.get('Páko Stock') if pd.notna(row.get('Páko Stock')) else existing.pako_stock
                            existing.mango_price = row.get('Mango Price') if pd.notna(row.get('Mango Price')) else existing.mango_price
                            existing.mango_stock = row.get('Mango Stock') if pd.notna(row.get('Mango Stock')) else existing.mango_stock
                            existing.plaza_price = row.get('Pláza Price') if pd.notna(row.get('Pláza Price')) else existing.plaza_price
                            existing.plaza_stock = row.get('Pláza Stock') if pd.notna(row.get('Pláza Stock')) else existing.plaza_stock
                            existing.diszkont_price = row.get('Diszkont Price') if pd.notna(row.get('Diszkont Price')) else existing.diszkont_price
                            existing.diszkont_stock = row.get('Diszkont Stock') if pd.notna(row.get('Diszkont Stock')) else existing.diszkont_stock
                            existing.hitspace_price = row.get('Hitspace Price') if pd.notna(row.get('Hitspace Price')) else existing.hitspace_price
                            existing.hitspace_stock = row.get('Hitspace Stock') if pd.notna(row.get('Hitspace Stock')) else existing.hitspace_stock
                            
                            existing.import_date = datetime.datetime.now()
                    else:
                        # Create new record with all competitor fields
                        competitor_scrape = CompetitorScrape(
                            product_code=product_code,
                            thomann_url=row.get('Thomann URL') if pd.notna(row.get('Thomann URL')) else None,
                            thomann_price=row.get('Thomann Price') if pd.notna(row.get('Thomann Price')) else None,
                            muziker_url=row.get('Muziker URL') if pd.notna(row.get('Muziker URL')) else None,
                            muziker_price=row.get('Muziker Price') if pd.notna(row.get('Muziker Price')) else None,
                            
                            # Price comparison site URLs
                            arukereso_url=row.get('Árukereső URL') if pd.notna(row.get('Árukereső URL')) else None,
                            argep_url=row.get('Árgép URL') if pd.notna(row.get('Árgép URL')) else None,
                            
                            # All new competitor prices and stocks
                            r55_price=row.get('R55 Price') if pd.notna(row.get('R55 Price')) else None,
                            r55_stock=row.get('R55 Stock') if pd.notna(row.get('R55 Stock')) else None,
                            kytary_price=row.get('Kytary Price') if pd.notna(row.get('Kytary Price')) else None,
                            kytary_stock=row.get('Kytary Stock') if pd.notna(row.get('Kytary Stock')) else None,
                            mezzo_price=row.get('Mezzo Price') if pd.notna(row.get('Mezzo Price')) else None,
                            mezzo_stock=row.get('Mezzo Stock') if pd.notna(row.get('Mezzo Stock')) else None,
                            allegro_price=row.get('Allegro Price') if pd.notna(row.get('Allegro Price')) else None,
                            allegro_stock=row.get('Allegro Stock') if pd.notna(row.get('Allegro Stock')) else None,
                            pako_price=row.get('Páko Price') if pd.notna(row.get('Páko Price')) else None,
                            pako_stock=row.get('Páko Stock') if pd.notna(row.get('Páko Stock')) else None,
                            mango_price=row.get('Mango Price') if pd.notna(row.get('Mango Price')) else None,
                            mango_stock=row.get('Mango Stock') if pd.notna(row.get('Mango Stock')) else None,
                            plaza_price=row.get('Pláza Price') if pd.notna(row.get('Pláza Price')) else None,
                            plaza_stock=row.get('Pláza Stock') if pd.notna(row.get('Pláza Stock')) else None,
                            diszkont_price=row.get('Diszkont Price') if pd.notna(row.get('Diszkont Price')) else None,
                            diszkont_stock=row.get('Diszkont Stock') if pd.notna(row.get('Diszkont Stock')) else None,
                            hitspace_price=row.get('Hitspace Price') if pd.notna(row.get('Hitspace Price')) else None,
                            hitspace_stock=row.get('Hitspace Stock') if pd.notna(row.get('Hitspace Stock')) else None
                        )
                        session.add(competitor_scrape)
                    
                    count += 1
                    
                except Exception as row_error:
                    st.warning(f"Error with competitor scrape record for product {row.get('Termékkód', 'unknown')}: {str(row_error)}")
                    continue
            
            # Commit each batch
            session.commit()
            
            # Update progress
            progress = int(batch_end / total_rows * 100)
            progress_bar.progress(progress)
            status_text.text(f"Processing competitor scrape data... {batch_end}/{total_rows} ({progress}%)")
        
        # Final progress update
        progress_bar.progress(100)
        status_text.text(f"Completed processing {count} competitor scrape records!")
        return count
        
    except Exception as e:
        session.rollback()
        st.error(f"Error saving competitor scrape data: {str(e)}")
        return 0
    finally:
        close_session(session)

def get_detailed_competitor_data(suppliers=None, brands=None, product_groups=None, web_categories=None, product_filter=None, limit=None):
    """
    Get detailed competitor data with product information for enhanced display.
    
    Args:
        suppliers: List of suppliers to filter by
        brands: List of brands to filter by  
        product_groups: List of product groups to filter by
        web_categories: List of web categories to filter by
        product_filter: Text to search for in product codes (Termékkód)
        limit: Maximum number of records to return
    
    Returns:
        List of dictionaries with detailed competitor and product information
    """

    
    session = get_session()
    if not session:
        return []
    
    try:
        from models import ProductCategory, product_category_association
        from sqlalchemy import and_
        
        # Build the base query with joins
        query = session.query(
            CompetitorScrape.product_code,
            CompetitorScrape.thomann_url,
            CompetitorScrape.thomann_price,
            CompetitorScrape.thomann_stock,
            CompetitorScrape.muziker_url,
            CompetitorScrape.muziker_price,
            CompetitorScrape.muziker_stock,
            CompetitorScrape.arukereso_url,
            CompetitorScrape.argep_url,
            CompetitorScrape.r55_price,
            CompetitorScrape.r55_stock,
            CompetitorScrape.kytary_price,
            CompetitorScrape.kytary_stock,
            CompetitorScrape.mezzo_price,
            CompetitorScrape.mezzo_stock,
            CompetitorScrape.allegro_price,
            CompetitorScrape.allegro_stock,
            CompetitorScrape.pako_price,
            CompetitorScrape.pako_stock,
            CompetitorScrape.mango_price,
            CompetitorScrape.mango_stock,
            CompetitorScrape.plaza_price,
            CompetitorScrape.plaza_stock,
            CompetitorScrape.diszkont_price,
            CompetitorScrape.diszkont_stock,
            CompetitorScrape.hitspace_price,
            CompetitorScrape.hitspace_stock,
            CompetitorScrape.import_date,
            CompetitorScrape.last_scraped,
            Product.product_name,
            Product.brand,
            Product.product_group,
            Product.primary_supplier
        ).outerjoin(
            Product, CompetitorScrape.product_code == Product.product_id
        )
        
        # If web categories filter is specified, add joins for category filtering
        if web_categories and len(web_categories) > 0:
            query = query.join(
                product_category_association, 
                Product.product_id == product_category_association.c.product_id
            ).join(
                ProductCategory,
                product_category_association.c.category_id == ProductCategory.id
            )
        
        # Apply filters
        filters = []
        
        if suppliers and len(suppliers) > 0:
            filters.append(Product.primary_supplier.in_(suppliers))
        
        if brands and len(brands) > 0:
            filters.append(Product.brand.in_(brands))
            
        if product_groups and len(product_groups) > 0:
            filters.append(Product.product_group.in_(product_groups))
            
        if web_categories and len(web_categories) > 0:
            filters.append(ProductCategory.category_name.in_(web_categories))
            
        if product_filter and len(product_filter) > 0:
            # Filter by product code containing the entered text (case-insensitive)
            filters.append(CompetitorScrape.product_code.ilike(f'%{product_filter}%'))
        
        # Only apply filters if any are specified
        if filters:
            query = query.filter(and_(*filters))
        
        # Use distinct to avoid duplicates when joining with categories
        if web_categories and len(web_categories) > 0:
            query = query.distinct()
        
        # Apply limit
        if limit:
            query = query.limit(limit)
        
        results = query.all()
        
        # Convert to list of dictionaries
        detailed_data = []
        for result in results:
            detailed_data.append({
                'product_code': result.product_code,
                'product_name': result.product_name or 'Unknown Product',
                'brand': result.brand or 'Unknown Brand',
                'product_group': result.product_group or 'Unknown Group',
                'primary_supplier': result.primary_supplier or 'Unknown Supplier',
                'thomann_url': result.thomann_url or '',
                'thomann_price': result.thomann_price or 0,
                'thomann_stock': result.thomann_stock or 'Unknown',
                'muziker_url': result.muziker_url or '',
                'muziker_price': result.muziker_price or 0,
                'muziker_stock': result.muziker_stock or 'Unknown',
                'arukereso_url': result.arukereso_url or '',
                'argep_url': result.argep_url or '',
                'r55_price': result.r55_price or 0,
                'r55_stock': result.r55_stock or 'Unknown',
                'kytary_price': result.kytary_price or 0,
                'kytary_stock': result.kytary_stock or 'Unknown',
                'mezzo_price': result.mezzo_price or 0,
                'mezzo_stock': result.mezzo_stock or 'Unknown',
                'allegro_price': result.allegro_price or 0,
                'allegro_stock': result.allegro_stock or 'Unknown',
                'pako_price': result.pako_price or 0,
                'pako_stock': result.pako_stock or 'Unknown',
                'mango_price': result.mango_price or 0,
                'mango_stock': result.mango_stock or 'Unknown',
                'plaza_price': result.plaza_price or 0,
                'plaza_stock': result.plaza_stock or 'Unknown',
                'diszkont_price': result.diszkont_price or 0,
                'diszkont_stock': result.diszkont_stock or 'Unknown',
                'hitspace_price': result.hitspace_price or 0,
                'hitspace_stock': result.hitspace_stock or 'Unknown',
                'import_date': result.import_date,
                'last_scraped': result.last_scraped,
                'scrape_status': 'Never Scraped' if not result.last_scraped else 'Scraped'
            })
        
        return detailed_data
        
    except Exception as e:
        st.error(f"Error fetching detailed competitor data: {str(e)}")
        return []
    finally:
        close_session(session)

def save_custom_setting(setting_type, entity_type, entity_name, value, currency="HUF"):
    """Save a custom setting to the database."""
    session = get_session()
    if session:
        try:
            # Check if setting already exists
            existing_setting = session.query(CustomSetting).filter_by(
                setting_type=setting_type,
                entity_type=entity_type,
                entity_name=entity_name
            ).first()
            
            # Determine if value is numeric or text
            is_numeric = isinstance(value, (int, float))
            
            if existing_setting:
                # Update existing setting
                if is_numeric:
                    existing_setting.value = float(value)
                    existing_setting.text_value = None
                else:
                    existing_setting.value = None
                    existing_setting.text_value = str(value)
                # Ensure we're using setattr for the currency to avoid LSP issues
                setattr(existing_setting, 'currency', currency)
            else:
                # Create new setting
                if is_numeric:
                    setting = CustomSetting(
                        setting_type=setting_type,
                        entity_type=entity_type,
                        entity_name=entity_name,
                        value=float(value),
                        text_value=None
                    )
                else:
                    setting = CustomSetting(
                        setting_type=setting_type,
                        entity_type=entity_type,
                        entity_name=entity_name,
                        value=None,
                        text_value=str(value)
                    )
                # Set the currency separately to avoid LSP issues
                setattr(setting, 'currency', currency)
                session.add(setting)
                
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            st.error(f"Error saving custom setting: {str(e)}")
            return False
        finally:
            close_session(session)
    return False


def save_brand_settings_data(df):
    """
    Save brand settings data to the database with batch processing (250 records at a time).
    Updates existing records with new data.
    
    Args:
        df: DataFrame containing brand settings data
    """
    total_rows = len(df)
    batch_size = 250
    count = 0
    
    # Create progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # Process in batches
        for batch_start in range(0, total_rows, batch_size):
            batch_end = min(batch_start + batch_size, total_rows)
            batch_df = df.iloc[batch_start:batch_end]
            
            status_text.text(f"Processing batch {batch_start + 1}-{batch_end} of {total_rows} records...")
            
            session = get_session()
            brand_settings_bulk = []  # Collect all settings for bulk operations
            
            try:
                with session.begin():
                    # Process each row in the batch
                    for idx, row in batch_df.iterrows():
                        try:
                            brand = str(row['Brand']).strip()
                            
                            if not brand or brand == 'nan':
                                continue
                            
                            # Prepare logistics data
                            logistics_data = {
                                'customs_exw': float(row['Customs_EXW_Percent']) if pd.notna(row['Customs_EXW_Percent']) else 0.0,
                                'shipping_exw': float(row['Shipping_EXW_Percent']) if pd.notna(row['Shipping_EXW_Percent']) else 0.0,
                                'customs_fob': float(row['Customs_FOB_Percent']) if pd.notna(row['Customs_FOB_Percent']) else 0.0,
                                'shipping_fob': float(row['Shipping_FOB_Percent']) if pd.notna(row['Shipping_FOB_Percent']) else 0.0,
                                'default_shipping': str(row['Default_Shipping']) if pd.notna(row['Default_Shipping']) else 'EXW',
                                'purchase_currency': str(row['Purchase_Currency']) if pd.notna(row['Purchase_Currency']) else 'EUR',
                                'sales_currency': str(row['Sales_Currency']) if pd.notna(row['Sales_Currency']) else 'EUR'
                            }
                            
                            # Prepare margin data
                            margin_data = {
                                'margin_a': float(row['Margin_A_Percent']) if pd.notna(row['Margin_A_Percent']) else 0.0,
                                'margin_b': float(row['Margin_B_Percent']) if pd.notna(row['Margin_B_Percent']) else 0.0,
                                'margin_c': float(row['Margin_C_Percent']) if pd.notna(row['Margin_C_Percent']) else 0.0,
                                'margin_d': float(row['Margin_D_Percent']) if pd.notna(row['Margin_D_Percent']) else 0.0,
                                'margin_eol': float(row['Margin_EOL_Percent']) if pd.notna(row['Margin_EOL_Percent']) else 0.0,
                                'margin_export': float(row['Margin_Export_Percent']) if pd.notna(row['Margin_Export_Percent']) else 0.0,
                                'default_category': str(row['Default_Margin_Category']) if pd.notna(row['Default_Margin_Category']) else 'A'
                            }
                            
                            # Prepare discount data
                            discount_data = {
                                'discount_i': float(row['Discount_I_Percent']) if pd.notna(row['Discount_I_Percent']) else 0.0,
                                'discount_ii': float(row['Discount_II_Percent']) if pd.notna(row['Discount_II_Percent']) else 0.0,
                                'discount_iii': float(row['Discount_III_Percent']) if pd.notna(row['Discount_III_Percent']) else 0.0,
                                'discount_iv': float(row['Discount_IV_Percent']) if pd.notna(row['Discount_IV_Percent']) else 0.0
                            }
                            
                            # Bulk create/update all settings for this brand using direct SQL for speed
                            brand_settings = [
                                # Logistics settings
                                {'setting_type': 'customs_cost_exw', 'entity_type': 'brand', 'entity_name': brand, 'value': logistics_data['customs_exw'], 'text_value': None},
                                {'setting_type': 'shipping_cost_exw', 'entity_type': 'brand', 'entity_name': brand, 'value': logistics_data['shipping_exw'], 'text_value': None},
                                {'setting_type': 'customs_cost_fob', 'entity_type': 'brand', 'entity_name': brand, 'value': logistics_data['customs_fob'], 'text_value': None},
                                {'setting_type': 'shipping_cost_fob', 'entity_type': 'brand', 'entity_name': brand, 'value': logistics_data['shipping_fob'], 'text_value': None},
                                {'setting_type': 'default_shipping', 'entity_type': 'brand', 'entity_name': brand, 'value': None, 'text_value': logistics_data['default_shipping']},
                                {'setting_type': 'purchase_currency', 'entity_type': 'brand', 'entity_name': brand, 'value': None, 'text_value': logistics_data['purchase_currency']},
                                {'setting_type': 'sales_currency', 'entity_type': 'brand', 'entity_name': brand, 'value': None, 'text_value': logistics_data['sales_currency']},
                                # Margin settings
                                {'setting_type': 'margin_target', 'entity_type': 'brand_A', 'entity_name': brand, 'value': margin_data['margin_a'], 'text_value': None},
                                {'setting_type': 'margin_target', 'entity_type': 'brand_B', 'entity_name': brand, 'value': margin_data['margin_b'], 'text_value': None},
                                {'setting_type': 'margin_target', 'entity_type': 'brand_C', 'entity_name': brand, 'value': margin_data['margin_c'], 'text_value': None},
                                {'setting_type': 'margin_target', 'entity_type': 'brand_D', 'entity_name': brand, 'value': margin_data['margin_d'], 'text_value': None},
                                {'setting_type': 'margin_target', 'entity_type': 'brand_EOL', 'entity_name': brand, 'value': margin_data['margin_eol'], 'text_value': None},
                                {'setting_type': 'margin_target', 'entity_type': 'brand_Export', 'entity_name': brand, 'value': margin_data['margin_export'], 'text_value': None},
                                {'setting_type': 'default_margin_category', 'entity_type': 'brand', 'entity_name': brand, 'value': None, 'text_value': margin_data['default_category']},
                                # Discount settings
                                {'setting_type': 'discount_percentage', 'entity_type': 'brand_I', 'entity_name': brand, 'value': discount_data['discount_i'], 'text_value': None},
                                {'setting_type': 'discount_percentage', 'entity_type': 'brand_II', 'entity_name': brand, 'value': discount_data['discount_ii'], 'text_value': None},
                                {'setting_type': 'discount_percentage', 'entity_type': 'brand_III', 'entity_name': brand, 'value': discount_data['discount_iii'], 'text_value': None},
                                {'setting_type': 'discount_percentage', 'entity_type': 'brand_IV', 'entity_name': brand, 'value': discount_data['discount_iv'], 'text_value': None},
                            ]
                            
                            # Collect all settings for bulk operations
                            brand_settings_bulk.extend(brand_settings)
                            
                            count += 1
                            
                        except Exception as row_error:
                            st.warning(f"Error processing brand '{brand}': {str(row_error)}")
                            continue
                    
                    # Perform bulk operations after collecting all settings for this batch
                    if brand_settings_bulk:
                        # Group by brand for bulk delete operations
                        brands_in_batch = list(set([setting['entity_name'] for setting in brand_settings_bulk]))
                        
                        # Delete existing settings for all brands in this batch
                        setting_types = list(set([setting['setting_type'] for setting in brand_settings_bulk]))
                        session.query(CustomSetting).filter(
                            CustomSetting.entity_name.in_(brands_in_batch),
                            CustomSetting.setting_type.in_(setting_types)
                        ).delete(synchronize_session=False)
                        
                        # Bulk insert all new settings
                        session.bulk_insert_mappings(CustomSetting, [
                            {
                                'setting_type': setting['setting_type'],
                                'entity_type': setting['entity_type'],
                                'entity_name': setting['entity_name'],
                                'value': setting['value'],
                                'text_value': setting['text_value'],
                                'currency': 'HUF'
                            }
                            for setting in brand_settings_bulk
                        ])
                            
            except Exception as batch_error:
                session.rollback()
                st.error(f"Error in batch {batch_start}-{batch_end}: {str(batch_error)}")
                raise batch_error
            finally:
                close_session(session)
            
            # Update progress
            progress = batch_end / total_rows
            progress_bar.progress(progress)
        
        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()
        
        return count
        
    except Exception as e:
        # Clear progress indicators on error
        progress_bar.empty()
        status_text.empty()
        st.error(f"Error saving brand settings data: {str(e)}")
        return 0


def update_or_create_setting(session, setting_type, entity_type, entity_name, value, text_value=None, currency="HUF"):
    """Helper function to update or create a custom setting within an existing session."""
    # Check if setting already exists
    existing_setting = session.query(CustomSetting).filter_by(
        setting_type=setting_type,
        entity_type=entity_type,
        entity_name=entity_name
    ).first()
    
    # Determine if value is numeric or text
    is_numeric = isinstance(value, (int, float)) and value is not None
    
    if existing_setting:
        # Update existing setting
        if is_numeric:
            existing_setting.value = float(value)
            existing_setting.text_value = None
        else:
            existing_setting.value = None
            existing_setting.text_value = str(text_value) if text_value is not None else None
        setattr(existing_setting, 'currency', currency)
    else:
        # Create new setting
        if is_numeric:
            setting = CustomSetting(
                setting_type=setting_type,
                entity_type=entity_type,
                entity_name=entity_name,
                value=float(value),
                text_value=None
            )
        else:
            setting = CustomSetting(
                setting_type=setting_type,
                entity_type=entity_type,
                entity_name=entity_name,
                value=None,
                text_value=str(text_value) if text_value is not None else None
            )
        setattr(setting, 'currency', currency)
        session.add(setting)

def get_custom_settings(setting_type=None, entity_type=None):
    """Get custom settings from the database."""
    session = get_session()
    if session:
        try:
            query = session.query(CustomSetting)
            
            if setting_type:
                query = query.filter(CustomSetting.setting_type == setting_type)
            
            if entity_type:
                query = query.filter(CustomSetting.entity_type == entity_type)
                
            settings = query.all()
            
            # Create enhanced setting objects with combined value field
            enhanced_settings = []
            for setting in settings:
                # Create a copy-like object with the appropriate value
                class EnhancedSetting:
                    def __init__(self, setting):
                        self.id = setting.id
                        self.setting_type = setting.setting_type
                        self.entity_type = setting.entity_type
                        self.entity_name = setting.entity_name
                        self.currency = setting.currency
                        # Use text_value if it exists, otherwise use numeric value
                        if setting.text_value is not None:
                            self.value = setting.text_value
                        else:
                            self.value = setting.value
                
                enhanced_settings.append(EnhancedSetting(setting))
                
            return enhanced_settings
        except Exception as e:
            st.error(f"Error retrieving custom settings: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_all_logistics_settings_for_brands():
    """Optimized function to get all logistics settings for all brands in a single query."""
    session = get_session()
    logistics_data = {}
    
    if session:
        try:
            # Get all brands first
            brands = get_brands()
            
            # Initialize data structure for all brands
            for brand in brands:
                logistics_data[brand] = {
                    'customs_exw': 0.0,
                    'shipping_exw': 0.0,
                    'customs_fob': 0.0,
                    'shipping_fob': 0.0,
                    'default_shipping': 'EXW',
                    'purchase_currency': 'EUR',
                    'sales_currency': 'HUF'
                }
            
            # Get all logistics-related settings in one query
            logistics_setting_types = [
                'customs_cost_exw', 'customs_cost_fob',
                'shipping_cost_exw', 'shipping_cost_fob',
                'default_shipping', 'purchase_currency', 'sales_currency'
            ]
            
            settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(logistics_setting_types),
                CustomSetting.entity_type == 'brand',
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            # Process all settings and populate data structure
            for setting in settings:
                brand = setting.entity_name
                if brand in logistics_data:
                    # Map setting types to data keys
                    type_mapping = {
                        'customs_cost_exw': 'customs_exw',
                        'customs_cost_fob': 'customs_fob',
                        'shipping_cost_exw': 'shipping_exw',
                        'shipping_cost_fob': 'shipping_fob',
                        'default_shipping': 'default_shipping',
                        'purchase_currency': 'purchase_currency',
                        'sales_currency': 'sales_currency'
                    }
                    
                    data_key = type_mapping.get(setting.setting_type)
                    if data_key:
                        # Use text_value for non-numeric settings, value for numeric
                        if setting.text_value is not None:
                            logistics_data[brand][data_key] = setting.text_value
                        else:
                            logistics_data[brand][data_key] = setting.value or 0.0
            
        except Exception as e:
            st.error(f"Error fetching logistics settings: {str(e)}")
        finally:
            close_session(session)
    
    return logistics_data

def get_all_margin_settings_for_brands():
    """Optimized function to get all margin settings for all brands in a single query."""
    session = get_session()
    margin_data = {}
    
    if session:
        try:
            # Get all brands first
            brands = get_brands()
            
            # Initialize data structure for all brands
            for brand in brands:
                margin_data[brand] = {
                    'margin_a': 0.0,
                    'margin_b': 0.0,
                    'margin_c': 0.0,
                    'margin_d': 0.0,
                    'margin_eol': 0.0,
                    'margin_export': 0.0,
                    'default_category': 'A'
                }
            
            # Get all margin-related settings in one query
            margin_setting_types = ['margin_target', 'default_margin_category']
            
            settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(margin_setting_types),
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            # Process all settings and populate data structure
            for setting in settings:
                brand = setting.entity_name
                if brand in margin_data:
                    if setting.setting_type == 'margin_target':
                        # Extract category from entity_type (e.g., 'brand_A' -> 'A')
                        if setting.entity_type.startswith('brand_'):
                            category = setting.entity_type.replace('brand_', '')
                            category_map = {
                                'A': 'margin_a',
                                'B': 'margin_b', 
                                'C': 'margin_c',
                                'D': 'margin_d',
                                'EOL': 'margin_eol',
                                'Export': 'margin_export'
                            }
                            
                            data_key = category_map.get(category)
                            if data_key and setting.value is not None:
                                margin_data[brand][data_key] = float(setting.value)
                    
                    elif setting.setting_type == 'default_margin_category':
                        if setting.text_value:
                            margin_data[brand]['default_category'] = setting.text_value
            
        except Exception as e:
            st.error(f"Error fetching margin settings: {str(e)}")
        finally:
            close_session(session)
    
    return margin_data

def save_all_margin_settings(updated_data, original_data=None):
    """Save margin settings in optimized bulk operations, only updating changed values."""
    session = get_session()
    if not session:
        return False, 0, 0
    
    try:
        changes_count = 0
        total_operations = 0
        
        # If original data provided, only save changes
        if original_data:
            changes_to_save = {}
            for brand, new_settings in updated_data.items():
                original_settings = original_data.get(brand, {})
                brand_changes = {}
                
                for key, new_value in new_settings.items():
                    original_value = original_settings.get(key)
                    if original_value != new_value:
                        brand_changes[key] = new_value
                
                if brand_changes:
                    changes_to_save[brand] = brand_changes
            
            updated_data = changes_to_save
        
        # Get existing settings for all brands
        brands = list(updated_data.keys())
        existing_settings = {}
        
        if brands:
            margin_setting_types = ['margin_target', 'default_margin_category']
            settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(margin_setting_types),
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            for setting in settings:
                key = (setting.entity_name, setting.setting_type, setting.entity_type)
                existing_settings[key] = setting
        
        new_settings_to_add = []
        
        # Process each brand's settings
        for brand, settings in updated_data.items():
            total_operations += len(settings)
            
            for setting_key, value in settings.items():
                # Map setting keys to database format
                if setting_key == 'default_category':
                    setting_type = 'default_margin_category'
                    entity_type = 'brand'
                elif setting_key.startswith('margin_'):
                    setting_type = 'margin_target'
                    category_map = {
                        'margin_a': 'brand_A',
                        'margin_b': 'brand_B',
                        'margin_c': 'brand_C',
                        'margin_d': 'brand_D',
                        'margin_eol': 'brand_EOL',
                        'margin_export': 'brand_Export'
                    }
                    entity_type = category_map.get(setting_key, 'brand')
                else:
                    continue
                
                existing_setting = existing_settings.get((brand, setting_type, entity_type))
                
                # Determine if value is numeric or text
                is_numeric = isinstance(value, (int, float))
                
                if existing_setting:
                    # Update existing setting
                    if is_numeric:
                        if existing_setting.value != float(value):
                            existing_setting.value = float(value)
                            existing_setting.text_value = None
                            changes_count += 1
                    else:
                        if existing_setting.text_value != str(value):
                            existing_setting.value = None
                            existing_setting.text_value = str(value)
                            changes_count += 1
                    existing_setting.currency = "HUF"
                else:
                    # Create new setting
                    if is_numeric:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type=entity_type,
                            entity_name=brand,
                            value=float(value),
                            text_value=None,
                            currency="HUF"
                        )
                    else:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type=entity_type,
                            entity_name=brand,
                            value=None,
                            text_value=str(value),
                            currency="HUF"
                        )
                    new_settings_to_add.append(new_setting)
                    changes_count += 1
        
        # Bulk add new settings
        if new_settings_to_add:
            session.add_all(new_settings_to_add)
        
        # Commit all changes in one transaction
        session.commit()
        return True, changes_count, total_operations
        
    except Exception as e:
        session.rollback()
        st.error(f"Error saving margin settings: {str(e)}")
        return False, 0, 0
    finally:
        close_session(session)

def get_all_discount_settings_for_brands():
    """Optimized function to get all discount settings for all brands in a single query."""
    session = get_session()
    discount_data = {}
    
    if session:
        try:
            # Get all brands first
            brands = get_brands()
            
            # Initialize data structure for all brands
            for brand in brands:
                discount_data[brand] = {
                    'discount_i': 0.0,
                    'discount_ii': 0.0,
                    'discount_iii': 0.0,
                    'discount_iv': 0.0
                }
            
            # Get all discount-related settings in one query
            discount_setting_types = ['discount_percentage']
            
            settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(discount_setting_types),
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            # Process all settings and populate data structure
            for setting in settings:
                brand = setting.entity_name
                if brand in discount_data:
                    if setting.setting_type == 'discount_percentage':
                        # Extract category from entity_type (e.g., 'brand_I' -> 'I')
                        if setting.entity_type.startswith('brand_'):
                            category = setting.entity_type.replace('brand_', '')
                            category_map = {
                                'I': 'discount_i',
                                'II': 'discount_ii', 
                                'III': 'discount_iii',
                                'IV': 'discount_iv'
                            }
                            
                            data_key = category_map.get(category)
                            if data_key and setting.value is not None:
                                discount_data[brand][data_key] = float(setting.value)
            
        except Exception as e:
            st.error(f"Error fetching discount settings: {str(e)}")
        finally:
            close_session(session)
    
    return discount_data

def save_all_discount_settings(updated_data, original_data=None):
    """Save discount settings in optimized bulk operations, only updating changed values."""
    session = get_session()
    if not session:
        return False, 0, 0
    
    try:
        changes_count = 0
        total_operations = 0
        
        # If original data provided, only save changes
        if original_data:
            changes_to_save = {}
            for brand, new_settings in updated_data.items():
                original_settings = original_data.get(brand, {})
                brand_changes = {}
                
                for key, new_value in new_settings.items():
                    original_value = original_settings.get(key)
                    if original_value != new_value:
                        brand_changes[key] = new_value
                
                if brand_changes:
                    changes_to_save[brand] = brand_changes
            
            updated_data = changes_to_save
        
        # Get existing settings for all brands
        brands = list(updated_data.keys())
        existing_settings = {}
        
        if brands:
            discount_setting_types = ['discount_percentage']
            settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(discount_setting_types),
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            for setting in settings:
                key = (setting.entity_name, setting.setting_type, setting.entity_type)
                existing_settings[key] = setting
        
        new_settings_to_add = []
        
        # Process each brand's settings
        for brand, settings in updated_data.items():
            total_operations += len(settings)
            
            for setting_key, value in settings.items():
                # Map setting keys to database format
                if setting_key.startswith('discount_'):
                    setting_type = 'discount_percentage'
                    category_map = {
                        'discount_i': 'brand_I',
                        'discount_ii': 'brand_II',
                        'discount_iii': 'brand_III',
                        'discount_iv': 'brand_IV'
                    }
                    entity_type = category_map.get(setting_key, 'brand')
                else:
                    continue
                
                existing_setting = existing_settings.get((brand, setting_type, entity_type))
                
                # Determine if value is numeric or text
                is_numeric = isinstance(value, (int, float))
                
                if existing_setting:
                    # Update existing setting
                    if is_numeric:
                        if existing_setting.value != float(value):
                            existing_setting.value = float(value)
                            existing_setting.text_value = None
                            changes_count += 1
                    else:
                        if existing_setting.text_value != str(value):
                            existing_setting.value = None
                            existing_setting.text_value = str(value)
                            changes_count += 1
                    existing_setting.currency = "HUF"
                else:
                    # Create new setting
                    if is_numeric:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type=entity_type,
                            entity_name=brand,
                            value=float(value),
                            text_value=None,
                            currency="HUF"
                        )
                    else:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type=entity_type,
                            entity_name=brand,
                            value=None,
                            text_value=str(value),
                            currency="HUF"
                        )
                    new_settings_to_add.append(new_setting)
                    changes_count += 1
        
        # Bulk add new settings
        if new_settings_to_add:
            session.add_all(new_settings_to_add)
        
        # Commit all changes in one transaction
        session.commit()
        return True, changes_count, total_operations
        
    except Exception as e:
        session.rollback()
        st.error(f"Error saving discount settings: {str(e)}")
        return False, 0, 0
    finally:
        close_session(session)

def save_all_logistics_settings(updated_data, original_data=None):
    """Save logistics settings in optimized bulk operations, only updating changed values."""
    session = get_session()
    if not session:
        return False, 0, 0
    
    try:
        changes_count = 0
        total_operations = 0
        
        # If original data provided, only save changes
        if original_data:
            changes_to_save = {}
            for brand, new_settings in updated_data.items():
                original_settings = original_data.get(brand, {})
                brand_changes = {}
                
                for key, new_value in new_settings.items():
                    original_value = original_settings.get(key)
                    if original_value != new_value:
                        brand_changes[key] = new_value
                
                if brand_changes:
                    changes_to_save[brand] = brand_changes
            
            data_to_save = changes_to_save
        else:
            data_to_save = updated_data
        
        if not data_to_save:
            return True, 0, 0  # No changes to save
        
        # Prepare bulk operations
        setting_mappings = {
            'customs_exw': 'customs_cost_exw',
            'customs_fob': 'customs_cost_fob',
            'shipping_exw': 'shipping_cost_exw',
            'shipping_fob': 'shipping_cost_fob',
            'default_shipping': 'default_shipping',
            'purchase_currency': 'purchase_currency',
            'sales_currency': 'sales_currency'
        }
        
        # Get all existing settings in one query
        brand_list = list(data_to_save.keys())
        setting_types = list(setting_mappings.values())
        
        existing_settings = session.query(CustomSetting).filter(
            CustomSetting.setting_type.in_(setting_types),
            CustomSetting.entity_type == 'brand',
            CustomSetting.entity_name.in_(brand_list)
        ).all()
        
        # Create lookup dictionary for existing settings
        existing_lookup = {}
        for setting in existing_settings:
            key = (setting.setting_type, setting.entity_name)
            existing_lookup[key] = setting
        
        # Process changes
        new_settings_to_add = []
        
        for brand, settings in data_to_save.items():
            for data_key, value in settings.items():
                setting_type = setting_mappings.get(data_key)
                if not setting_type:
                    continue
                
                total_operations += 1
                lookup_key = (setting_type, brand)
                existing_setting = existing_lookup.get(lookup_key)
                
                # Determine if value is numeric or text
                is_numeric = isinstance(value, (int, float))
                
                if existing_setting:
                    # Update existing setting
                    if is_numeric:
                        if existing_setting.value != float(value):
                            existing_setting.value = float(value)
                            existing_setting.text_value = None
                            changes_count += 1
                    else:
                        if existing_setting.text_value != str(value):
                            existing_setting.value = None
                            existing_setting.text_value = str(value)
                            changes_count += 1
                    existing_setting.currency = "HUF"
                else:
                    # Create new setting
                    if is_numeric:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type='brand',
                            entity_name=brand,
                            value=float(value),
                            text_value=None,
                            currency="HUF"
                        )
                    else:
                        new_setting = CustomSetting(
                            setting_type=setting_type,
                            entity_type='brand',
                            entity_name=brand,
                            value=None,
                            text_value=str(value),
                            currency="HUF"
                        )
                    new_settings_to_add.append(new_setting)
                    changes_count += 1
        
        # Bulk add new settings
        if new_settings_to_add:
            session.add_all(new_settings_to_add)
        
        # Commit all changes in one transaction
        session.commit()
        return True, changes_count, total_operations
        
    except Exception as e:
        session.rollback()
        st.error(f"Error saving logistics settings: {str(e)}")
        return False, 0, 0
    finally:
        close_session(session)

def clean_product_brands():
    """Clean up incorrectly stored brands in the database."""
    session = get_session()
    if session:
        try:
            # Get all products with problematic brands (containing "/")
            problematic_products = session.query(Product).filter(Product.brand.like('%/%')).all()
            
            updated_count = 0
            for product in problematic_products:
                # Move incorrect brand to vendor_product_group if it's empty
                if not product.vendor_product_group and "/" in product.brand:
                    product.vendor_product_group = product.brand
                
                # Try to extract real brand from product_group pattern like "Shure / ..."
                if product.product_group and "/" in product.product_group:
                    parts = product.product_group.split("/")
                    if len(parts) >= 2:
                        potential_brand = parts[0].strip()
                        # Only use if it looks like a real brand (no spaces, capitalized)
                        if potential_brand and " " not in potential_brand and potential_brand[0].isupper():
                            product.brand = potential_brand
                        else:
                            product.brand = None
                    else:
                        product.brand = None
                else:
                    product.brand = None
                
                updated_count += 1
            
            session.commit()
            st.success(f"Cleaned up {updated_count} product brand records.")
            return updated_count
            
        except Exception as e:
            session.rollback()
            st.error(f"Error cleaning product brands: {str(e)}")
            return 0
        finally:
            close_session(session)
    return 0

def get_brands():
    """Get list of all brands from products."""
    try:
        session = get_session()
        if session:
            try:
                brands = session.query(Product.brand).distinct().order_by(Product.brand).all()
                # Return only valid brand names (no "/" and not empty)
                filtered_brands = []
                for brand in brands:
                    if brand[0] and "/" not in brand[0]:
                        filtered_brands.append(brand[0].strip())
                
                return filtered_brands or ["Default Brand"]
            except Exception as e:
                st.error(f"Error retrieving brands: {str(e)}")
                return ["Default Brand"]
            finally:
                close_session(session)
        return ["Default Brand"]
    except Exception as e:
        st.error(f"Critical error retrieving brands: {str(e)}")
        return ["Default Brand"]

def get_warehouses():
    """Get list of all warehouses from stock."""
    session = get_session()
    if session:
        try:
            warehouses = session.query(Stock.warehouse).distinct().order_by(Stock.warehouse).all()
            return [warehouse[0] for warehouse in warehouses if warehouse[0]]
        except Exception as e:
            st.error(f"Error retrieving warehouses: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_product_categories():
    """Get list of all product categories."""
    session = get_session()
    if session:
        try:
            categories = session.query(ProductCategory.category_name).distinct().order_by(ProductCategory.category_name).all()
            return [category[0] for category in categories if category[0]]
        except Exception as e:
            st.error(f"Error retrieving product categories: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_product_groups():
    """Get list of all product groups from the Products table."""
    session = get_session()
    if session:
        try:
            from models import Product
            groups = session.query(Product.product_group).distinct().order_by(Product.product_group).all()
            return [group[0] for group in groups if group[0]]
        except Exception as e:
            st.error(f"Error retrieving product groups: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_buyers():
    """Get list of all buyers."""
    session = get_session()
    if session:
        try:
            buyers = session.query(Buyer).order_by(Buyer.buyer_name).all()
            return buyers
        except Exception as e:
            st.error(f"Error retrieving buyers: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_suppliers():
    """Get list of all suppliers from purchases."""
    session = get_session()
    if session:
        try:
            suppliers = session.query(Purchase.supplier_name).distinct().order_by(Purchase.supplier_name).all()
            return [supplier[0] for supplier in suppliers if supplier[0]]
        except Exception as e:
            st.error(f"Error retrieving suppliers: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_countries():
    """Get list of all countries from buyers."""
    session = get_session()
    if session:
        try:
            countries = session.query(Buyer.country).distinct().order_by(Buyer.country).all()
            return [country[0] for country in countries if country[0]]
        except Exception as e:
            st.error(f"Error retrieving countries: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_currencies():
    """Get list of all currencies from prices and purchases."""
    session = get_session()
    if session:
        try:
            price_currencies = session.query(Price.currency).distinct().all()
            purchase_currencies = session.query(Purchase.currency).distinct().all()
            
            currencies = set([currency[0] for currency in price_currencies + purchase_currencies if currency[0]])
            return sorted(list(currencies))
        except Exception as e:
            st.error(f"Error retrieving currencies: {str(e)}")
            return ["HUF", "EUR", "USD", "RON"]  # Default currencies
        finally:
            close_session(session)
    return ["HUF", "EUR", "USD", "RON"]  # Default currencies

def get_sales_overview(start_date=None, end_date=None, brand=None, category=None, buyer=None, country=None):
    """Get sales overview data with optional filters."""
    session = get_session()
    if session:
        try:
            query = session.query(
                func.sum(Sale.quantity).label('total_quantity'),
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit')
            )
            
            # Apply filters
            if start_date:
                query = query.filter(Sale.sale_date >= start_date)
            if end_date:
                query = query.filter(Sale.sale_date <= end_date)
            if brand:
                query = query.join(Product).filter(Product.brand == brand)
            if category:
                query = query.join(Product).join(Product.categories).filter(ProductCategory.category_name == category)
            if buyer:
                query = query.filter(Sale.buyer_id == buyer)
            if country:
                query = query.join(Buyer).filter(Buyer.country == country)
                
            result = query.first()
            
            # Format the results
            total_quantity = float(result.total_quantity) if result.total_quantity else 0
            total_revenue = float(result.total_revenue) if result.total_revenue else 0
            total_profit = float(result.total_profit) if result.total_profit else 0
            margin_percentage = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            return {
                'total_quantity': total_quantity,
                'total_revenue': total_revenue,
                'total_profit': total_profit,
                'margin_percentage': margin_percentage
            }
        except Exception as e:
            st.error(f"Error retrieving sales overview: {str(e)}")
            return {
                'total_quantity': 0,
                'total_revenue': 0,
                'total_profit': 0,
                'margin_percentage': 0
            }
        finally:
            close_session(session)
    return {
        'total_quantity': 0,
        'total_revenue': 0,
        'total_profit': 0,
        'margin_percentage': 0
    }

def get_stock_overview(warehouse=None, brand=None, category=None):
    """Get stock overview data with optional filters."""
    session = get_session()
    if session:
        try:
            query = session.query(
                func.sum(Stock.quantity).label('total_quantity'),
                func.sum(Stock.value).label('total_value')
            )
            
            # Apply filters
            if warehouse:
                query = query.filter(Stock.warehouse == warehouse)
            if brand:
                query = query.join(Product).filter(Product.brand == brand)
            if category:
                query = query.join(Product).join(Product.categories).filter(ProductCategory.category_name == category)
                
            # Get only the latest stock records for each product/warehouse combination
            subquery = session.query(
                Stock.product_id,
                Stock.warehouse,
                func.max(Stock.date_recorded).label('max_date')
            ).group_by(Stock.product_id, Stock.warehouse).subquery()
            
            query = query.join(
                subquery,
                and_(
                    Stock.product_id == subquery.c.product_id,
                    Stock.warehouse == subquery.c.warehouse,
                    Stock.date_recorded == subquery.c.max_date
                )
            )
                
            result = query.first()
            
            # Format the results
            total_quantity = float(result.total_quantity) if result.total_quantity else 0
            total_value = float(result.total_value) if result.total_value else 0
            
            return {
                'total_quantity': total_quantity,
                'total_value': total_value
            }
        except Exception as e:
            st.error(f"Error retrieving stock overview: {str(e)}")
            return {
                'total_quantity': 0,
                'total_value': 0
            }
        finally:
            close_session(session)
    return {
        'total_quantity': 0,
        'total_value': 0
    }

def get_purchases_overview(start_date=None, end_date=None, supplier=None, brand=None, currency=None):
    """Get purchases overview data with optional filters."""
    session = get_session()
    if session:
        try:
            query = session.query(
                func.sum(Purchase.quantity).label('total_quantity'),
                func.sum(Purchase.total_value_huf).label('total_value')
            )
            
            # Apply filters
            if start_date:
                query = query.filter(Purchase.invoice_date >= start_date)
            if end_date:
                query = query.filter(Purchase.invoice_date <= end_date)
            if supplier:
                query = query.filter(Purchase.supplier_name == supplier)
            if brand:
                query = query.join(Product).filter(Product.brand == brand)
            if currency:
                query = query.filter(Purchase.currency == currency)
                
            result = query.first()
            
            # Format the results
            total_quantity = float(result.total_quantity) if result.total_quantity else 0
            total_value = float(result.total_value) if result.total_value else 0
            
            return {
                'total_quantity': total_quantity,
                'total_value': total_value
            }
        except Exception as e:
            st.error(f"Error retrieving purchases overview: {str(e)}")
            return {
                'total_quantity': 0,
                'total_value': 0
            }
        finally:
            close_session(session)
    return {
        'total_quantity': 0,
        'total_value': 0
    }

def get_sales_by_brand(start_date=None, end_date=None, country=None):
    """Get sales data grouped by brand."""
    session = get_session()
    if session:
        try:
            query = session.query(
                Product.brand,
                func.sum(Sale.quantity).label('total_quantity'),
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit')
            ).join(Product)
            
            # Apply filters
            if start_date:
                query = query.filter(Sale.sale_date >= start_date)
            if end_date:
                query = query.filter(Sale.sale_date <= end_date)
            if country:
                query = query.join(Buyer).filter(Buyer.country == country)
                
            query = query.group_by(Product.brand).order_by(func.sum(Sale.quantity * Sale.unit_price).desc())
            
            results = query.all()
            
            data = []
            for row in results:
                if row.brand:  # Ignore null brands
                    total_revenue = float(row.total_revenue) if row.total_revenue else 0
                    total_profit = float(row.total_profit) if row.total_profit else 0
                    margin_percentage = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
                    
                    data.append({
                        'brand': row.brand,
                        'quantity': float(row.total_quantity) if row.total_quantity else 0,
                        'revenue': total_revenue,
                        'profit': total_profit,
                        'margin': margin_percentage
                    })
            
            return data
        except Exception as e:
            st.error(f"Error retrieving sales by brand: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_sales_by_month(start_date=None, end_date=None, brand=None):
    """Get sales data grouped by month."""
    session = get_session()
    if session:
        try:
            # Extract year and month from sale_date
            if 'postgresql' in str(session.bind.engine.url):
                # PostgreSQL date extraction
                year_month = func.date_trunc('month', Sale.sale_date)
            else:
                # SQLite date extraction fallback
                year_month = func.strftime('%Y-%m-01', Sale.sale_date)
            
            query = session.query(
                year_month.label('month'),
                func.sum(Sale.quantity).label('total_quantity'),
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit')
            )
            
            # Apply filters
            if start_date:
                query = query.filter(Sale.sale_date >= start_date)
            if end_date:
                query = query.filter(Sale.sale_date <= end_date)
            if brand:
                query = query.join(Product).filter(Product.brand == brand)
                
            query = query.group_by(year_month).order_by(year_month)
            
            results = query.all()
            
            data = []
            for row in results:
                if row.month:  # Ignore null months
                    month_str = row.month.strftime('%Y-%m')
                    total_revenue = float(row.total_revenue) if row.total_revenue else 0
                    total_profit = float(row.total_profit) if row.total_profit else 0
                    
                    data.append({
                        'month': month_str,
                        'quantity': float(row.total_quantity) if row.total_quantity else 0,
                        'revenue': total_revenue,
                        'profit': total_profit
                    })
            
            return data
        except Exception as e:
            st.error(f"Error retrieving sales by month: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_stock_by_warehouse():
    """Get stock data grouped by warehouse."""
    session = get_session()
    if session:
        try:
            # Get only the latest stock records for each product/warehouse combination
            subquery = session.query(
                Stock.product_id,
                Stock.warehouse,
                func.max(Stock.date_recorded).label('max_date')
            ).group_by(Stock.product_id, Stock.warehouse).subquery()
            
            query = session.query(
                Stock.warehouse,
                func.sum(Stock.quantity).label('total_quantity'),
                func.sum(Stock.value).label('total_value')
            ).join(
                subquery,
                and_(
                    Stock.product_id == subquery.c.product_id,
                    Stock.warehouse == subquery.c.warehouse,
                    Stock.date_recorded == subquery.c.max_date
                )
            ).group_by(Stock.warehouse).order_by(func.sum(Stock.value).desc())
            
            results = query.all()
            
            data = []
            for row in results:
                if row.warehouse:  # Ignore null warehouses
                    data.append({
                        'warehouse': row.warehouse,
                        'quantity': float(row.total_quantity) if row.total_quantity else 0,
                        'value': float(row.total_value) if row.total_value else 0
                    })
            
            return data
        except Exception as e:
            st.error(f"Error retrieving stock by warehouse: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_purchases_by_supplier(start_date=None, end_date=None):
    """Get purchase data grouped by supplier."""
    session = get_session()
    if session:
        try:
            query = session.query(
                Purchase.supplier_name,
                func.sum(Purchase.quantity).label('total_quantity'),
                func.sum(Purchase.total_value_huf).label('total_value')
            )
            
            # Apply filters
            if start_date:
                query = query.filter(Purchase.invoice_date >= start_date)
            if end_date:
                query = query.filter(Purchase.invoice_date <= end_date)
                
            query = query.group_by(Purchase.supplier_name).order_by(func.sum(Purchase.total_value_huf).desc())
            
            results = query.all()
            
            data = []
            for row in results:
                if row.supplier_name:  # Ignore null suppliers
                    data.append({
                        'supplier': row.supplier_name,
                        'quantity': float(row.total_quantity) if row.total_quantity else 0,
                        'value': float(row.total_value) if row.total_value else 0
                    })
            
            return data
        except Exception as e:
            st.error(f"Error retrieving purchases by supplier: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_top_products(metric='sales', limit=10, start_date=None, end_date=None):
    """Get top products by sales quantity, revenue, or profit."""
    session = get_session()
    if session:
        try:
            if metric == 'sales':
                # Top products by sales quantity
                query = session.query(
                    Product.product_id,
                    Product.product_name,
                    func.sum(Sale.quantity).label('total_quantity')
                ).join(Product)
                
                # Apply filters
                if start_date:
                    query = query.filter(Sale.sale_date >= start_date)
                if end_date:
                    query = query.filter(Sale.sale_date <= end_date)
                    
                query = query.group_by(Product.product_id, Product.product_name).order_by(func.sum(Sale.quantity).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'product_id': row.product_id,
                        'product_name': row.product_name,
                        'value': float(row.total_quantity) if row.total_quantity else 0
                    })
                
                return data
            elif metric == 'revenue':
                # Top products by revenue
                query = session.query(
                    Product.product_id,
                    Product.product_name,
                    func.sum(Sale.quantity * Sale.unit_price).label('total_revenue')
                ).join(Product)
                
                # Apply filters
                if start_date:
                    query = query.filter(Sale.sale_date >= start_date)
                if end_date:
                    query = query.filter(Sale.sale_date <= end_date)
                    
                query = query.group_by(Product.product_id, Product.product_name).order_by(func.sum(Sale.quantity * Sale.unit_price).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'product_id': row.product_id,
                        'product_name': row.product_name,
                        'value': float(row.total_revenue) if row.total_revenue else 0
                    })
                
                return data
            elif metric == 'profit':
                # Top products by profit
                query = session.query(
                    Product.product_id,
                    Product.product_name,
                    func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit')
                ).join(Product)
                
                # Apply filters
                if start_date:
                    query = query.filter(Sale.sale_date >= start_date)
                if end_date:
                    query = query.filter(Sale.sale_date <= end_date)
                    
                query = query.group_by(Product.product_id, Product.product_name).order_by(func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'product_id': row.product_id,
                        'product_name': row.product_name,
                        'value': float(row.total_profit) if row.total_profit else 0
                    })
                
                return data
            elif metric == 'stock_value':
                # Top products by stock value
                # Get only the latest stock records for each product
                subquery = session.query(
                    Stock.product_id,
                    func.max(Stock.date_recorded).label('max_date')
                ).group_by(Stock.product_id).subquery()
                
                query = session.query(
                    Product.product_id,
                    Product.product_name,
                    func.sum(Stock.value).label('total_value')
                ).join(Product).join(
                    subquery,
                    and_(
                        Stock.product_id == subquery.c.product_id,
                        Stock.date_recorded == subquery.c.max_date
                    )
                ).group_by(Product.product_id, Product.product_name).order_by(func.sum(Stock.value).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'product_id': row.product_id,
                        'product_name': row.product_name,
                        'value': float(row.total_value) if row.total_value else 0
                    })
                
                return data
        except Exception as e:
            st.error(f"Error retrieving top products: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_top_buyers(metric='quantity', limit=10, start_date=None, end_date=None):
    """Get top buyers by sales quantity or revenue."""
    session = get_session()
    if session:
        try:
            if metric == 'quantity':
                # Top buyers by quantity
                query = session.query(
                    Buyer.buyer_id,
                    Buyer.buyer_name,
                    func.sum(Sale.quantity).label('total_quantity')
                ).join(Buyer)
                
                # Apply filters
                if start_date:
                    query = query.filter(Sale.sale_date >= start_date)
                if end_date:
                    query = query.filter(Sale.sale_date <= end_date)
                    
                query = query.group_by(Buyer.buyer_id, Buyer.buyer_name).order_by(func.sum(Sale.quantity).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'buyer_id': row.buyer_id,
                        'buyer_name': row.buyer_name,
                        'value': float(row.total_quantity) if row.total_quantity else 0
                    })
                
                return data
            elif metric == 'revenue':
                # Top buyers by revenue
                query = session.query(
                    Buyer.buyer_id,
                    Buyer.buyer_name,
                    func.sum(Sale.quantity * Sale.unit_price).label('total_revenue')
                ).join(Buyer)
                
                # Apply filters
                if start_date:
                    query = query.filter(Sale.sale_date >= start_date)
                if end_date:
                    query = query.filter(Sale.sale_date <= end_date)
                    
                query = query.group_by(Buyer.buyer_id, Buyer.buyer_name).order_by(func.sum(Sale.quantity * Sale.unit_price).desc()).limit(limit)
                
                results = query.all()
                
                data = []
                for row in results:
                    data.append({
                        'buyer_id': row.buyer_id,
                        'buyer_name': row.buyer_name,
                        'value': float(row.total_revenue) if row.total_revenue else 0
                    })
                
                return data
        except Exception as e:
            st.error(f"Error retrieving top buyers: {str(e)}")
            return []
        finally:
            close_session(session)
    return []

def get_placeholder_image_path():
    """
    Returns a local placeholder image path to use when external images can't be loaded.
    Creates a simple placeholder SVG if one doesn't exist.
    """
    placeholder_path = "assets/placeholder_image.svg"
    
    # Create assets directory if it doesn't exist
    os.makedirs("assets", exist_ok=True)
    
    # Create a simple placeholder SVG if it doesn't exist
    if not os.path.exists(placeholder_path):
        with open(placeholder_path, "w") as f:
            f.write('''<svg width="1280" height="720" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f0f2f6"/>
                <text x="50%" y="50%" font-family="Arial" font-size="24" text-anchor="middle" fill="#6c757d">
                    ERP Analytics Dashboard
                </text>
            </svg>''')
    
    return placeholder_path

def calculate_kpis(start_date=None, end_date=None):
    """Calculate key performance indicators."""
    session = get_session()
    if session:
        try:
            # Sales KPIs
            sales_query = session.query(
                func.sum(Sale.quantity).label('total_quantity'),
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity * (Sale.unit_price - Sale.purchase_unit_price)).label('total_profit'),
                func.count(func.distinct(Sale.buyer_id)).label('buyer_count')
            )
            
            # Apply date filters
            if start_date:
                sales_query = sales_query.filter(Sale.sale_date >= start_date)
            if end_date:
                sales_query = sales_query.filter(Sale.sale_date <= end_date)
                
            sales_result = sales_query.first()
            
            # Stock KPIs
            # Get only the latest stock records for each product
            stock_subquery = session.query(
                Stock.product_id,
                func.max(Stock.date_recorded).label('max_date')
            ).group_by(Stock.product_id).subquery()
            
            stock_query = session.query(
                func.sum(Stock.quantity).label('total_stock_quantity'),
                func.sum(Stock.value).label('total_stock_value'),
                func.count(func.distinct(Stock.product_id)).label('product_count'),
                func.count(func.distinct(Stock.warehouse)).label('warehouse_count')
            ).join(
                stock_subquery,
                and_(
                    Stock.product_id == stock_subquery.c.product_id,
                    Stock.date_recorded == stock_subquery.c.max_date
                )
            )
                
            stock_result = stock_query.first()
            
            # Purchase KPIs
            purchase_query = session.query(
                func.sum(Purchase.quantity).label('total_purchase_quantity'),
                func.sum(Purchase.total_value_huf).label('total_purchase_value'),
                func.count(func.distinct(Purchase.supplier_name)).label('supplier_count')
            )
            
            # Apply date filters
            if start_date:
                purchase_query = purchase_query.filter(Purchase.invoice_date >= start_date)
            if end_date:
                purchase_query = purchase_query.filter(Purchase.invoice_date <= end_date)
                
            purchase_result = purchase_query.first()
            
            # Calculate KPIs
            total_sales_quantity = float(sales_result.total_quantity) if sales_result.total_quantity else 0
            total_sales_revenue = float(sales_result.total_revenue) if sales_result.total_revenue else 0
            total_sales_profit = float(sales_result.total_profit) if sales_result.total_profit else 0
            total_buyer_count = int(sales_result.buyer_count) if sales_result.buyer_count else 0
            
            total_stock_quantity = float(stock_result.total_stock_quantity) if stock_result.total_stock_quantity else 0
            total_stock_value = float(stock_result.total_stock_value) if stock_result.total_stock_value else 0
            total_product_count = int(stock_result.product_count) if stock_result.product_count else 0
            total_warehouse_count = int(stock_result.warehouse_count) if stock_result.warehouse_count else 0
            
            total_purchase_quantity = float(purchase_result.total_purchase_quantity) if purchase_result.total_purchase_quantity else 0
            total_purchase_value = float(purchase_result.total_purchase_value) if purchase_result.total_purchase_value else 0
            total_supplier_count = int(purchase_result.supplier_count) if purchase_result.supplier_count else 0
            
            # Calculate derived KPIs
            margin_percentage = (total_sales_profit / total_sales_revenue * 100) if total_sales_revenue > 0 else 0
            stock_turnover = (total_sales_quantity / total_stock_quantity) if total_stock_quantity > 0 else 0
            
            return {
                'sales_quantity': total_sales_quantity,
                'sales_revenue': total_sales_revenue,
                'sales_profit': total_sales_profit,
                'margin_percentage': margin_percentage,
                'buyer_count': total_buyer_count,
                
                'stock_quantity': total_stock_quantity,
                'stock_value': total_stock_value,
                'product_count': total_product_count,
                'warehouse_count': total_warehouse_count,
                
                'purchase_quantity': total_purchase_quantity,
                'purchase_value': total_purchase_value,
                'supplier_count': total_supplier_count,
                
                'stock_turnover': stock_turnover
            }
        except Exception as e:
            st.error(f"Error calculating KPIs: {str(e)}")
            return {
                'sales_quantity': 0,
                'sales_revenue': 0,
                'sales_profit': 0,
                'margin_percentage': 0,
                'buyer_count': 0,
                
                'stock_quantity': 0,
                'stock_value': 0,
                'product_count': 0,
                'warehouse_count': 0,
                
                'purchase_quantity': 0,
                'purchase_value': 0,
                'supplier_count': 0,
                
                'stock_turnover': 0
            }
        finally:
            close_session(session)
    return {
        'sales_quantity': 0,
        'sales_revenue': 0,
        'sales_profit': 0,
        'margin_percentage': 0,
        'buyer_count': 0,
        
        'stock_quantity': 0,
        'stock_value': 0,
        'product_count': 0,
        'warehouse_count': 0,
        
        'purchase_quantity': 0,
        'purchase_value': 0,
        'supplier_count': 0,
        
        'stock_turnover': 0
    }

# Pricing-specific functions

def get_suppliers_for_pricing():
    """Get list of unique suppliers from purchases table."""
    from database import close_session
    session = get_session()
    suppliers = []
    
    if session:
        try:
            from models import Purchase
            result = session.query(Purchase.supplier_name).distinct().filter(
                Purchase.supplier_name.isnot(None)
            ).all()
            suppliers = [row[0] for row in result if row[0]]
        except Exception as e:
            print(f"Error fetching suppliers: {str(e)}")
        finally:
            close_session(session)
    
    return sorted(suppliers)

def get_brands_by_supplier(suppliers):
    """Get brands filtered by selected suppliers."""
    from database import close_session
    session = get_session()
    brands = []
    
    if session:
        try:
            from models import Product
            result = session.query(Product.brand).distinct().filter(
                Product.primary_supplier.in_(suppliers),
                Product.brand.isnot(None)
            ).all()
            brands = [row[0] for row in result if row[0]]
        except Exception as e:
            print(f"Error fetching brands: {str(e)}")
        finally:
            close_session(session)
    
    return sorted(brands)

def get_products_for_pricing(suppliers, brands):
    """Get products with purchase prices for pricing calculations. Currency is now determined per-brand."""
    from database import close_session
    session = get_session()
    products = []
    
    if session:
        try:
            from models import Product, Price, CustomSetting
            from sqlalchemy import and_
            
            # First, get brand currency settings
            brand_currencies = {}
            currency_settings = session.query(CustomSetting).filter(
                CustomSetting.setting_type.in_(['purchase_currency', 'sales_currency']),
                CustomSetting.entity_name.in_(brands)
            ).all()
            
            for setting in currency_settings:
                brand = setting.entity_name
                if brand not in brand_currencies:
                    brand_currencies[brand] = {}
                if setting.setting_type == 'purchase_currency':
                    brand_currencies[brand]['purchase_currency'] = setting.text_value or 'EUR'
                elif setting.setting_type == 'sales_currency':
                    brand_currencies[brand]['sales_currency'] = setting.text_value or 'EUR'
            
            # Join products with latest prices
            query = session.query(
                Product.product_id,
                Product.product_name,
                Product.brand,
                Product.primary_supplier,
                Price.purchase_price,
                Price.store_price,
                Price.online_price,
                Price.export_price,
                Price.msrp,
                Price.currency
            ).join(
                Price, Product.product_id == Price.product_id
            )
            
            # Build supplier filter with partial matching
            supplier_conditions = []
            for supplier in suppliers:
                if supplier.endswith('...'):
                    # Handle truncated supplier names by using LIKE matching
                    supplier_prefix = supplier[:-3]  # Remove '...'
                    supplier_conditions.append(Product.primary_supplier.like(f'{supplier_prefix}%'))
                else:
                    supplier_conditions.append(Product.primary_supplier == supplier)
            
            from sqlalchemy import or_
            if supplier_conditions:
                supplier_filter = or_(*supplier_conditions)
            else:
                supplier_filter = True
            
            query = query.filter(
                and_(
                    supplier_filter,
                    Product.brand.in_(brands),
                    Price.purchase_price.isnot(None),  # Only products with actual purchase prices
                    Price.purchase_price != 0  # Exclude zero purchase prices
                )
            )
            
            results = query.all()
            
            for row in results:
                # Only use actual purchase prices, no fallbacks
                if row.purchase_price and row.purchase_price > 0:
                    brand = row.brand
                    product_id = row.product_id
                    
                    # Get brand-specific currencies
                    brand_currency_info = brand_currencies.get(brand, {})
                    sales_currency = brand_currency_info.get('sales_currency', 'EUR')
                    
                    # Get HUF MSRP for current MSRP comparison
                    huf_msrp = 0
                    huf_price_query = session.query(Price.msrp).filter(
                        Price.product_id == product_id,
                        Price.currency == 'HUF'
                    ).first()
                    if huf_price_query:
                        huf_msrp = huf_price_query[0] or 0
                    else:
                        huf_msrp = row.msrp or 0
                    
                    # Get sales currency MSRP (Net Man MSRP) - no fallback, only preferred currency
                    sales_currency_msrp = 0
                    actual_msrp_currency = sales_currency  # Track the actual currency used
                    sales_price_query = session.query(Price.msrp).filter(
                        Price.product_id == product_id,
                        Price.currency == sales_currency,
                        Price.msrp.isnot(None),
                        Price.msrp != 0
                    ).first()
                    if sales_price_query:
                        sales_currency_msrp = sales_price_query[0] or 0
                        actual_msrp_currency = sales_currency
                    else:
                        # Only use current row if it matches the sales currency
                        if row.currency == sales_currency:
                            sales_currency_msrp = row.msrp or 0
                            actual_msrp_currency = sales_currency
                        else:
                            # No fallback - leave empty if preferred currency has no MSRP
                            sales_currency_msrp = 0
                            actual_msrp_currency = sales_currency
                    
                    products.append({
                        'product_id': product_id,
                        'product_name': row.product_name,
                        'brand': brand,
                        'primary_supplier': row.primary_supplier,
                        'purchase_price': row.purchase_price,
                        'store_price': row.store_price,
                        'msrp': sales_currency_msrp,  # This is now the sales currency MSRP
                        'msrp_currency': actual_msrp_currency,  # The actual currency of the MSRP value
                        'current_huf_msrp': huf_msrp  # Always in HUF for accurate comparison
                    })
                
        except Exception as e:
            print(f"Error fetching products for pricing: {str(e)}")
        finally:
            close_session(session)
    
    return products

def get_default_shipping_method(brand):
    """Get default shipping method for a brand from logistics settings."""
    from database import close_session
    session = get_session()
    default_shipping = "EXW"  # Default fallback
    
    if session:
        try:
            from models import CustomSetting
            
            # Get default shipping method for brand
            shipping_setting = session.query(CustomSetting).filter(
                CustomSetting.setting_type == 'default_shipping',
                CustomSetting.entity_type == 'brand',
                CustomSetting.entity_name == brand
            ).first()
            
            if shipping_setting and shipping_setting.text_value:
                default_shipping = shipping_setting.text_value
                
        except Exception as e:
            print(f"Error fetching default shipping method: {str(e)}")
        finally:
            close_session(session)
    
    return default_shipping

def get_customs_shipping_rates(brand, shipping_method):
    """Get customs and shipping rates from custom settings based on shipping method."""
    from database import close_session
    session = get_session()
    customs_rate = 0.0
    shipping_rate = 0.0
    
    if session:
        try:
            from models import CustomSetting
            
            # Determine the correct setting type based on shipping method
            method_suffix = shipping_method.lower() if shipping_method else 'exw'
            
            # Get customs cost for brand and method
            customs_setting = session.query(CustomSetting).filter(
                CustomSetting.setting_type == f'customs_cost_{method_suffix}',
                CustomSetting.entity_type == 'brand',
                CustomSetting.entity_name == brand
            ).first()
            
            if customs_setting and customs_setting.value is not None:
                customs_rate = customs_setting.value / 100.0  # Convert percentage to decimal
            
            # Get shipping cost for brand and method
            shipping_setting = session.query(CustomSetting).filter(
                CustomSetting.setting_type == f'shipping_cost_{method_suffix}',
                CustomSetting.entity_type == 'brand',
                CustomSetting.entity_name == brand
            ).first()
            
            if shipping_setting and shipping_setting.value is not None:
                shipping_rate = shipping_setting.value / 100.0  # Convert percentage to decimal
                
        except Exception as e:
            print(f"Error fetching customs/shipping rates for {brand} {shipping_method}: {str(e)}")
        finally:
            close_session(session)
    
    return customs_rate, shipping_rate

def get_margin_default_category(brand):
    """Get default margin category for a brand from margin matrix."""
    from database import close_session
    session = get_session()
    margin_percentage = None
    
    if session:
        try:
            from models import CustomSetting
            
            # Get margin default category for brand
            margin_setting = session.query(CustomSetting).filter(
                CustomSetting.setting_type == 'default_margin_category',
                CustomSetting.entity_type == 'brand',
                CustomSetting.entity_name == brand
            ).first()
            
            if margin_setting:
                # Get the actual margin percentage for the category
                category = margin_setting.text_value or margin_setting.value  # This should be A, B, C, D, or EOL
                
                # Get margin percentage for the category
                margin_rate_setting = session.query(CustomSetting).filter(
                    CustomSetting.setting_type == 'margin_target',
                    CustomSetting.entity_type == f'brand_{category}',
                    CustomSetting.entity_name == brand
                ).first()
                
                if margin_rate_setting:
                    # Convert percentage to multiplier (e.g., 35% -> 1.35)
                    margin_percentage = 1.0 + (margin_rate_setting.value / 100.0)
                    
        except Exception as e:
            print(f"Error fetching margin default: {str(e)}")
        finally:
            close_session(session)
    
    return margin_percentage

def get_all_margins_for_brand(brand):
    """Get all available margin rates for a brand from margin matrix."""
    from database import close_session
    session = get_session()
    margins = []
    
    if session:
        try:
            from models import CustomSetting
            
            # Get all margin categories for this brand
            categories = ['A', 'B', 'C', 'D', 'EOL', 'Export']
            
            for category in categories:
                margin_setting = session.query(CustomSetting).filter(
                    CustomSetting.setting_type == 'margin_target',
                    CustomSetting.entity_type == f'brand_{category}',
                    CustomSetting.entity_name == brand
                ).first()
                
                if margin_setting:
                    # Convert percentage to multiplier (e.g., 35% -> 1.35)
                    margin_multiplier = 1.0 + (margin_setting.value / 100.0)
                    margins.append({
                        'category': category,
                        'value': margin_multiplier,
                        'percentage': margin_setting.value
                    })
            
        except Exception as e:
            print(f"Error fetching margins for brand {brand}: {str(e)}")
        finally:
            close_session(session)
    
    return margins

def get_export_margin_for_brand(brand):
    """Get export margin rate for a brand from margin matrix."""
    from database import close_session
    session = get_session()
    export_margin_percentage = 0
    
    if session:
        try:
            from models import CustomSetting
            
            # Get export margin for this brand
            margin_setting = session.query(CustomSetting).filter(
                CustomSetting.setting_type == 'margin_target',
                CustomSetting.entity_type == 'brand_Export',
                CustomSetting.entity_name == brand
            ).first()
            
            if margin_setting:
                # Return as percentage (e.g., 35)
                export_margin_percentage = margin_setting.value
                
        except Exception as e:
            print(f"Error fetching export margin: {str(e)}")
        finally:
            close_session(session)
    
    return export_margin_percentage

def get_discount_rates_by_brand(brand):
    """Get discount rates (I/II/III/IV) for a brand as percentages."""
    from database import close_session
    session = get_session()
    discount_rates = {}
    
    if session:
        try:
            from models import CustomSetting
            
            for category in ['I', 'II', 'III', 'IV']:
                discount_setting = session.query(CustomSetting).filter(
                    CustomSetting.setting_type == 'discount_percentage',
                    CustomSetting.entity_type == f'brand_{category}',
                    CustomSetting.entity_name == brand
                ).first()
                
                if discount_setting and discount_setting.value is not None:
                    discount_rates[category] = discount_setting.value  # Return as percentage
                    
        except Exception as e:
            print(f"Error fetching discount rates for brand {brand}: {str(e)}")
        finally:
            close_session(session)
    
    return discount_rates

def calculate_landing_cost(purchase_price, customs_rate, shipping_rate):
    """Calculate landing cost with customs and shipping."""
    if not purchase_price:
        return 0
    customs_rate = customs_rate or 0
    shipping_rate = shipping_rate or 0
    return purchase_price * (1 + customs_rate) * (1 + shipping_rate)

def calculate_partner_pricing_cascade(landing_cost, margin_rate, discount_rates):
    """Calculate partner pricing cascade based on margin and discounts."""
    # Step 1: Calculate best partner price (Category IV)
    best_partner_price = landing_cost * (1 + margin_rate)
    
    # Step 2: Calculate other categories by reverse discount application
    category_iv = best_partner_price
    category_iii = category_iv / (1 - discount_rates['III'])
    category_ii = category_iii / (1 - discount_rates['II'])
    category_i = category_ii / (1 - discount_rates['I'])
    
    return {
        'Category IV': round(category_iv, 2),
        'Category III': round(category_iii, 2),
        'Category II': round(category_ii, 2),
        'Category I': round(category_i, 2)
    }

def calculate_msrp_from_category_i(category_i_price):
    """Calculate MSRP from Category I price."""
    net_msrp = category_i_price / 0.8  # Category I is 80% of MSRP
    gross_msrp = net_msrp * 1.27  # Add 27% VAT
    
    return {
        'Net HUF NEW MSRP': round(net_msrp, 2),
        'Br HUF NEW MSRP': round(gross_msrp, 2)
    }

def get_sales_history_data(product_ids, months):
    """Get sales history data for products over specified months."""
    from database import close_session
    session = get_session()
    sales_data = {}
    
    if session:
        try:
            from models import Sale
            from sqlalchemy import func
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=months * 30)
            
            # Query all products at once for better performance
            results = session.query(
                Sale.product_id,
                func.count(Sale.id).label('sales_count'),
                func.max(Sale.sale_date).label('last_sale_date')
            ).filter(
                Sale.product_id.in_(product_ids),
                Sale.sale_date >= cutoff_date
            ).group_by(Sale.product_id).all()
            
            # Build results dictionary
            for result in results:
                sales_data[result.product_id] = {
                    'sales_count': result.sales_count or 0,
                    'last_sale_date': result.last_sale_date
                }
                
        except Exception as e:
            print(f"Error fetching sales history: {str(e)}")
        finally:
            close_session(session)
    
    return sales_data

def get_purchase_history_data(product_ids, months):
    """Get purchase history data for products over specified months."""
    from database import close_session
    session = get_session()
    purchase_data = {}
    
    if session:
        try:
            from models import Purchase
            from sqlalchemy import func
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=months * 30)
            
            # Query all products at once for better performance
            results = session.query(
                Purchase.product_id,
                func.count(Purchase.id).label('purchase_count'),
                func.max(Purchase.invoice_date).label('last_purchase_date')
            ).filter(
                Purchase.product_id.in_(product_ids),
                Purchase.invoice_date >= cutoff_date
            ).group_by(Purchase.product_id).all()
            
            # Build results dictionary
            for result in results:
                purchase_data[result.product_id] = {
                    'purchase_count': result.purchase_count or 0,
                    'last_purchase_date': result.last_purchase_date
                }
                
        except Exception as e:
            print(f"Error fetching purchase history: {str(e)}")
        finally:
            close_session(session)
    
    return purchase_data

def get_latest_stock_data(product_ids):
    """Get latest stock data for products."""
    from database import close_session
    session = get_session()
    stock_data = {}
    
    if session:
        try:
            from models import Stock
            from sqlalchemy import func
            
            for product_id in product_ids:
                result = session.query(
                    func.sum(Stock.quantity).label('total_quantity'),
                    func.sum(Stock.value).label('total_value')
                ).filter(
                    Stock.product_id == product_id
                ).first()
                
                if result:
                    stock_data['Stock'] = result.total_quantity or 0
                    stock_data['Stock Value'] = result.total_value or 0
                
        except Exception as e:
            print(f"Error fetching stock data: {str(e)}")
        finally:
            close_session(session)
    
    return stock_data

def get_exchange_rate_from_settings(currency):
    """Get exchange rate from settings for a specific currency."""
    from database import close_session
    session = get_session()
    if not session:
        raise ValueError(f"No database connection available to get exchange rate for {currency}")
    
    try:
        from models import CustomSetting
        
        # Get exchange rate setting for the currency
        rate_setting = session.query(CustomSetting).filter(
            CustomSetting.setting_type == 'exchange_rate',
            CustomSetting.entity_type == currency,
            CustomSetting.entity_name == 'rate'
        ).first()
        
        if rate_setting:
            return float(rate_setting.value)
        else:
            raise ValueError(f"Exchange rate not found in settings for currency: {currency}")
            
    except Exception as e:
        print(f"Error fetching exchange rate for {currency}: {str(e)}")
        raise e
    finally:
        close_session(session)

def apply_currency_conversion(amount, from_currency, to_currency, custom_rate=None):
    """Apply currency conversion using custom rate or database rates."""
    if from_currency == to_currency:
        return amount
    
    if custom_rate:
        return amount * custom_rate
    
    # Get rate from settings
    rate = get_exchange_rate_from_settings(from_currency)
    return amount * rate
