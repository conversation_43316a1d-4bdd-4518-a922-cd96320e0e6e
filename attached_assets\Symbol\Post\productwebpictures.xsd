﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="ProductWebPictures" xmlns="" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
  <xs:element name="ProductWebPictures" msdata:IsDataSet="true" msdata:Locale="en-US">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ProductWebPicture">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="product" type="xs:string" minOccurs="0" />
              <xs:element name="productcode" type="xs:string" minOccurs="0" />
              <xs:element name="WebPicture" minOccurs="0" maxOccurs="unbounded">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="caption" type="xs:string" minOccurs="0" />
                    <xs:element name="webpicture" type="xs:string" minOccurs="0" />
                    <xs:element name="prime" type="xs:string" minOccurs="0" />
                    <xs:element name="titletext" type="xs:string" minOccurs="0" />
                    <xs:element name="alttext" type="xs:string" minOccurs="0" />
                    <xs:element name="order" type="xs:string" minOccurs="0" />
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>