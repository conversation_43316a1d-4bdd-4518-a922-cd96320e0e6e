[LaunchDarkly] LaunchDarkly client initialized
2_app-59d4b8b10da893ae.js:37 If you do not provide children, you must specify an aria-label for accessibility
(anonymous) @ _app-59d4b8b10da893ae.js:37Understand this warning
icons.util.repl.co/bash.svg:1 
            
            
           Failed to load resource: net::ERR_NAME_NOT_RESOLVEDUnderstand this error
3_app-59d4b8b10da893ae.js:37 If you do not provide children, you must specify an aria-label for accessibility
(anonymous) @ _app-59d4b8b10da893ae.js:37Understand this warning
/repls:1 Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.
_app-59d4b8b10da893ae.js:37 onClick is deprecated, please use onPress
(anonymous) @ _app-59d4b8b10da893ae.js:37Understand this warning
2_app-59d4b8b10da893ae.js:37 If you do not provide children, you must specify an aria-label for accessibility
(anonymous) @ _app-59d4b8b10da893ae.js:37Understand this warning
6[Report Only] Refused to compile or instantiate WebAssembly module because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'".

 [Report Only] Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'".

index.js:12 [Report Only] Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'".

 [Report Only] Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'".

index.js:12 [Report Only] Refused to evaluate a string as JavaScript because 'unsafe-eval' is not an allowed source of script in the following Content Security Policy directive: "script-src 'self'".

image_1749168298087.png:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()Understand this error
17Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.Understand this error
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'ambient-light-sensor'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'battery'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'execution-while-not-rendered'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'execution-while-out-of-viewport'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'layout-animations'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'legacy-image-formats'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'navigation-override'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'oversized-images'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'publickey-credentials'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'speaker-selection'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'unoptimized-images'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'unsized-media'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Unrecognized feature: 'pointer-lock'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Allow attribute will take precedence over 'allowfullscreen'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
framework-e5ff3ac173b1d6d8.js:1 Allow attribute will take precedence over 'allowpaymentrequest'.
_ @ framework-e5ff3ac173b1d6d8.js:1Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'ambient-light-sensor'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'battery'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'execution-while-not-rendered'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'execution-while-out-of-viewport'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'layout-animations'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'legacy-image-formats'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'navigation-override'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'oversized-images'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'publickey-credentials'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'speaker-selection'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'unoptimized-images'.Understand this warning
workspace_iframe.html:32 Unrecognized feature: 'unsized-media'.Understand this warning
workspace_iframe.html:32 Allow attribute will take precedence over 'allowfullscreen'.Understand this warning
workspace_iframe.html:32 Allow attribute will take precedence over 'allowpaymentrequest'.Understand this warning
ee90b543-1847-4e87-9dee-5d901bcda784-00-39scf062fbk4w.picard.replit.dev/Pricing/_stcore/health:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)Understand this error
ee90b543-1847-4e87-9dee-5d901bcda784-00-39scf062fbk4w.picard.replit.dev/Pricing/_stcore/host-config:1 
            
            
           Failed to load resource: the server responded with a status of 404 (Not Found)Understand this error
index.BqDl3eRM.js:41 Unrecognized feature: 'document-domain'.
Ve @ index.BqDl3eRM.js:41Understand this warning
index.BqDl3eRM.js:41 Unrecognized feature: 'vr'.
Ve @ index.BqDl3eRM.js:41Understand this warning
index.BqDl3eRM.js:41 Unrecognized feature: 'wake-lock'.
Ve @ index.BqDl3eRM.js:41Understand this warning
[Violation] Potential permissions policy violation: publickey-credentials-get is not allowed in this document.