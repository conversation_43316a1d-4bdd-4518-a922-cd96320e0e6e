# customer_categories_rendering.py
import streamlit as st
import pandas as pd

# Define category and subcategory color mappings
category_colors = {
    'Kisker': '#2c7be5',
    'Nagyker': '#6f42c1',
    'Install': '#4e9b1c',
    'Rental': '#a9731c',
    'Export': '#b04c4c',
    'Egyéb': '#888888'
}

subcategory_border_colors = {
    'Kisker': '#cfe3fb',
    'Nagyker': '#dacdf1',
    'Install': '#d9eccf',
    'Rental': '#f0e1c9',
    'Export': '#f3caca',
    'Egyéb': '#d8d8d8'
}

def format_compact_number(value):
    if value >= 1_000_000:
        return f"{value/1_000_000:.1f}M"
    elif value >= 1_000:
        return f"{value/1_000:.1f}K"
    return f"{value:.0f}"

def render_customer_performance(main_category_totals, main_category_profits, 
                                 main_category_comparison, main_category_profit_comparison,
                                 current_sub_lookup, comparison_sub_lookup,
                                 buyer_to_main_map, display_order, 
                                 buyer_data=None, buyer_comparison_data=None):
    
    # Detect theme from session state (same as sales chart)
    is_dark_theme = st.session_state.get('dark_theme', True)
    
    # Define theme colors
    if is_dark_theme:
        theme_colors = {
            'bg_color': '#0e1117',
            'card_bg': '#262730',
            'text_primary': '#fafafa',
            'text_secondary': '#b0b0b0',
            'text': '#fafafa',
            'border_color': '#404040',
            'border': '#404040',
            'table_header_bg': '#1e1e1e',
            'table_hover': '#333333',
            'hover_bg': '#333333',
            'expander_line': '#404040'
        }
    else:
        theme_colors = {
            'bg_color': '#ffffff',
            'card_bg': '#ffffff',
            'text_primary': '#262730',
            'text_secondary': '#666666',
            'text': '#262730',
            'border_color': '#e2e2e2',
            'border': '#e2e2e2',
            'table_header_bg': '#f5f5f5',
            'table_hover': '#f9f9f9',
            'hover_bg': '#f9f9f9',
            'expander_line': '#f0f0f0'
        }
    
    # Apply theme-aware CSS with compact spacing
    st.markdown(f"""
    <style>
    /* Hide the expander label text */
    .streamlit-expanderHeader p {{
        display: none !important;
    }}

    /* Compact expander spacing */
    .streamlit-expanderHeader {{
        padding-top: 2px !important;
        padding-bottom: 2px !important;
        margin-bottom: 2px !important;
    }}
    .streamlit-expanderContent {{
        padding-top: 6px !important;
        padding-bottom: 6px !important;
        margin-bottom: 4px !important;
    }}
    
    /* Reduce column spacing */
    div[data-testid="column"] {{
        padding-left: 4px !important;
        padding-right: 4px !important;
    }}

    /* Completely hide expander arrows and reclaim space */
    [data-testid="stExpander"] details summary {{
        list-style: none !important;
        outline: none !important;
        cursor: pointer !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }}
    
    [data-testid="stExpander"] details summary::-webkit-details-marker {{
        display: none !important;
    }}
    
    [data-testid="stExpander"] details summary::-moz-list-bullet {{
        display: none !important;
    }}
    
    /* Remove all arrow-related elements and spacing */
    [data-testid="stExpander"] details summary > div {{
        margin: 0 !important;
        padding: 0 !important;
    }}
    
    [data-testid="stExpander"] details summary::before {{
        display: none !important;
    }}
    
    [data-testid="stExpander"] details summary::after {{
        display: none !important;
    }}

    /* Style for collapsed expander: minimal line */
    [data-testid="stExpander"] {{
        border: none !important;
        box-shadow: none !important;
        background-color: transparent !important;
    }}
    
    [data-testid="stExpander"] > div:first-child {{
        border-radius: 0 !important;
        cursor: pointer !important;
        height: 1px !important;
        background-color: {theme_colors['expander_line']} !important;
        border-bottom: none !important;
        margin: 2px 0 10px 0 !important;
        padding: 0 !important;
    }}
    
    /* Styling for expanded state - still minimal but functional */
    [data-testid="stExpander"] [aria-expanded="true"] {{
        background-color: transparent !important;
        border-bottom: none !important;
        margin-bottom: 0 !important;
    }}
    
    /* Remove padding within the expander content */
    .streamlit-expanderContent {{
        padding: 0 !important;
    }}

    /* Table styling for top buyers - theme aware */
    .top-buyers-table {{
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        background-color: {theme_colors['card_bg']};
    }}
    .top-buyers-table th {{
        background-color: {theme_colors['table_header_bg']};
        padding: 8px 10px;
        text-align: left;
        font-size: 0.8rem;
        font-weight: 600;
        color: {theme_colors['text_secondary']};
        border-bottom: 1px solid {theme_colors['border_color']};
    }}
    .top-buyers-table td {{
        padding: 8px 10px;
        border-bottom: 1px solid {theme_colors['border_color']};
        font-size: 0.85rem;
        color: {theme_colors['text_primary']};
        background-color: {theme_colors['card_bg']};
    }}
    .top-buyers-table tr:hover td {{
        background-color: {theme_colors['table_hover']} !important;
    }}
    .positive-change {{
        color: #10b981;
        font-weight: 600;
    }}
    .negative-change {{
        color: #ef4444;
        font-weight: 600;
    }}
    </style>
    """, unsafe_allow_html=True)
    
    st.markdown("### Customer Categories Performance", unsafe_allow_html=True)

    expand_all = st.checkbox("Expand all subcategories", value=False)

    # Order updated for better visual grouping
    main_category_pairs = [("Kisker", "Nagyker"), ("Install", "Export"), ("Egyéb", "Rental")]
    for left_cat, right_cat in main_category_pairs:
        col1, col2 = st.columns(2)
        with col1:
            display_main_and_subcategory(left_cat, expand_all,
                                         main_category_totals, main_category_profits,
                                         main_category_comparison, main_category_profit_comparison,
                                         current_sub_lookup, comparison_sub_lookup,
                                         buyer_to_main_map, display_order, theme_colors)
        with col2:
            display_main_and_subcategory(right_cat, expand_all,
                                         main_category_totals, main_category_profits,
                                         main_category_comparison, main_category_profit_comparison,
                                         current_sub_lookup, comparison_sub_lookup,
                                         buyer_to_main_map, display_order, theme_colors)
                                         
    # Display Top 10 Buyers table after the customer categories section
    if buyer_data is not None:
        display_top_buyers_table(buyer_data, buyer_comparison_data, theme_colors)

def display_main_and_subcategory(main_cat, expand_all,
                                  main_category_totals, main_category_profits,
                                  main_category_comparison, main_category_profit_comparison,
                                  current_sub_lookup, comparison_sub_lookup,
                                  buyer_to_main_map, display_order, theme_colors):
    if main_cat not in main_category_totals or main_category_totals[main_cat] == 0:
        return

    current_revenue = main_category_totals[main_cat]
    current_profit = main_category_profits[main_cat]
    prev_revenue = main_category_comparison.get(main_cat, 0)
    prev_profit = main_category_profit_comparison.get(main_cat, 0)
    
    # Calculate total revenue across all categories
    total_revenue = sum(main_category_totals.values())
    
    # Calculate this category's percentage of total revenue
    revenue_share_pct = (current_revenue / total_revenue * 100) if total_revenue > 0 else 0

    revenue_pct = ((current_revenue / prev_revenue) - 1) * 100 if prev_revenue > 0 else None
    profit_pct = ((current_profit / prev_profit) - 1) * 100 if prev_profit > 0 else None
    # Calculate actual margin from profit and revenue
    margin = (current_profit / current_revenue * 100) if current_revenue > 0 else 0
    # Log the values for debugging
    print(f"Category: {main_cat}, Revenue: {current_revenue}, Profit: {current_profit}, Calculated Margin: {margin}")

    revenue_display = format_compact_number(current_revenue)
    profit_display = format_compact_number(current_profit)
    revenue_pct_display = f"{revenue_pct:+.1f}%" if revenue_pct is not None else "N/A"
    profit_pct_display = f"{profit_pct:+.1f}%" if profit_pct is not None else "N/A"
    margin_display = f"{margin:.1f}%"
    revenue_share_display = f"{revenue_share_pct:.1f}%"

    text_color = "#10b981" if revenue_pct is not None and revenue_pct >= 0 else "#ef4444"
    profit_color = "#10b981" if profit_pct is not None and profit_pct >= 0 else "#ef4444"

    st.markdown(f"""
    <div style="
        border: 2px solid {category_colors[main_cat]};
        border-radius: 8px;
        padding: 6px 10px;
        margin-bottom: 2px;
        background-color: {theme_colors['card_bg']};
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: baseline; gap: 10px;">
                <div style="font-size: 1.1rem; font-weight: bold; color: {theme_colors['text_primary']};">{main_cat}</div>
                <div style="font-size: 0.8rem; color: {theme_colors['text_secondary']}; font-weight: 500;">({revenue_share_display} of total)</div>
            </div>
            <div style="display: flex; gap: 24px;">
                <div style="text-align: center;">
                    <div style="font-size: 0.75rem; color: {theme_colors['text_secondary']}; margin-bottom: 2px;">Revenue</div>
                    <div style="font-size: 0.95rem; font-weight: 600; color: {theme_colors['text_primary']};">{revenue_display}</div>
                    <div style="color: {text_color}; font-size: 0.8rem; font-weight: bold;">{revenue_pct_display}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 0.75rem; color: {theme_colors['text_secondary']}; margin-bottom: 2px;">Profit</div>
                    <div style="font-size: 0.95rem; font-weight: 600; color: {theme_colors['text_primary']};">{profit_display}</div>
                    <div style="color: {profit_color}; font-size: 0.8rem; font-weight: bold;">{profit_pct_display}</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 0.75rem; color: {theme_colors['text_secondary']}; margin-bottom: 2px;">Margin</div>
                    <div style="font-size: 0.95rem; font-weight: 600; color: {theme_colors['text_primary']};">{margin_display}</div>
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Use empty string for title to make the expander more compact
    with st.expander("", expanded=expand_all):
        subcategory_cols = st.columns(3)
        
        # Get subcategories for this main category - these should be the actual buyer categories, not individual buyers
        subcategories = current_sub_lookup.get(main_cat, {})
        
        # Sort subcategories by their values (descending)
        sorted_subcategories = sorted(subcategories.items(), key=lambda x: x[1], reverse=True)
        
        for i, (sub, current) in enumerate(sorted_subcategories):
            if current == 0:
                continue
            
            # Get comparison data
            prev = comparison_sub_lookup.get(main_cat, {}).get(sub, 0)
            delta_pct = ((current / prev - 1) * 100) if prev and prev > 0 else None
            delta_str = f"{delta_pct:+.1f}%" if delta_pct is not None else "N/A"
            delta_color = "#10b981" if delta_pct and delta_pct > 0 else "#ef4444" if delta_pct else "#6b7280"
            compact_value = format_compact_number(current)

            with subcategory_cols[i % 3]:
                st.markdown(f"""
                <div style="
                    border-left: 3px solid {subcategory_border_colors[main_cat]};
                    background-color: {theme_colors['card_bg']};
                    border-radius: 4px;
                    padding: 6px 8px;
                    margin-bottom: 6px;
                    border: 1px solid {theme_colors['border_color']};
                    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 0.75rem; font-weight: 600; color: {theme_colors['text_primary']}; margin-bottom: 2px; line-height: 1.2;">{sub}</div>
                    <div style="display: flex; align-items: baseline; gap: 6px;">
                        <span style="font-size: 0.8rem; font-weight: 600; color: {theme_colors['text_primary']};">{compact_value}</span>
                        <span style="font-size: 0.7rem; font-weight: bold; color: {delta_color};">{delta_str}</span>
                    </div>
                </div>
                """, unsafe_allow_html=True)

def display_top_buyers_table(buyer_data, buyer_comparison_data=None, theme_colors=None):
    """
    Display a table of the top 10 buyers with revenue, profit and margin data.
    
    Args:
        buyer_data: DataFrame with buyer data including buyer_name, revenue, profit
        buyer_comparison_data: Optional DataFrame with comparison data for the same buyers
    """
    st.markdown("### Top 10 Buyers")
    
    # Check if data is available
    if buyer_data is None or len(buyer_data) == 0:
        st.info("No buyer data available for the selected filters.")
        return
    
    # Process the data to create a complete dataframe with all needed values
    processed_data = []
    
    # Process each buyer's data
    for _, row in buyer_data.iterrows():
        buyer_name = row['buyer_name']
        current_revenue = row['sales_value']
        current_profit = row['profit']
        
        # Calculate margin
        margin = (current_profit / current_revenue * 100) if current_revenue > 0 else 0
        
        # Default values for comparison
        revenue_change_pct = None
        profit_change_pct = None
        margin_change_pp = None
        
        # If comparison data is available, calculate changes
        if buyer_comparison_data is not None and buyer_name in buyer_comparison_data['buyer_name'].values:
            # Get comparison data for this buyer
            comp_row = buyer_comparison_data[buyer_comparison_data['buyer_name'] == buyer_name].iloc[0]
            comp_revenue = comp_row['sales_value']
            comp_profit = comp_row['profit']
            
            # Calculate revenue change
            if comp_revenue > 0:
                revenue_change_pct = ((current_revenue / comp_revenue) - 1) * 100
            
            # Calculate profit change
            if comp_profit > 0:
                profit_change_pct = ((current_profit / comp_profit) - 1) * 100
            
            # Calculate margin change (percentage points)
            comp_margin = (comp_profit / comp_revenue * 100) if comp_revenue > 0 else 0
            margin_change_pp = margin - comp_margin
        
        # Add to processed data
        processed_data.append({
            'buyer_name': buyer_name,
            'revenue': current_revenue,
            'revenue_display': format_compact_number(current_revenue),
            'revenue_change_pct': revenue_change_pct,
            'revenue_change_display': f"{revenue_change_pct:+.1f}%" if revenue_change_pct is not None else "N/A",
            'profit': current_profit,
            'profit_display': format_compact_number(current_profit),
            'profit_change_pct': profit_change_pct,
            'profit_change_display': f"{profit_change_pct:+.1f}%" if profit_change_pct is not None else "N/A",
            'margin': margin,
            'margin_display': f"{margin:.1f}%",
            'margin_change_pp': margin_change_pp,
            'margin_change_display': f"{margin_change_pp:+.1f}pp" if margin_change_pp is not None else "N/A"
        })
    
    # Create dataframe
    df = pd.DataFrame(processed_data)
    
    # Initialize session state for sorting if it doesn't exist
    if 'sort_column' not in st.session_state:
        st.session_state.sort_column = 'revenue'
    if 'sort_ascending' not in st.session_state:
        st.session_state.sort_ascending = False
    
    # Define mapping between sort columns and display names
    sort_options = {
        'buyer_name': 'Buyer Name',
        'revenue': 'Revenue',
        'revenue_change_pct': 'Revenue Change',
        'profit': 'Profit', 
        'profit_change_pct': 'Profit Change',
        'margin': 'Margin',
        'margin_change_pp': 'Margin Change'
    }
    
    # Get the current sort column and direction from session state
    sort_col = st.session_state.sort_column
    sort_asc = st.session_state.sort_ascending
    
    # Replace None/NaN values with appropriate values for sorting
    df_for_sorting = df.copy()
    
    # Replace NaN/None with very small numbers for percentage columns to ensure they appear last in descending sort
    for col in ['revenue_change_pct', 'profit_change_pct', 'margin_change_pp']:
        if col in df_for_sorting.columns:
            df_for_sorting[col] = df_for_sorting[col].fillna(-9999) if not sort_asc else df_for_sorting[col].fillna(9999)
    
    # First sort by the chosen metric to get the top 10
    if sort_col == 'buyer_name':
        # For names, we sort alphabetically and take first/last 10 depending on direction
        if sort_asc:
            # For ascending, take first 10 alphabetically
            filtered_df = df_for_sorting.sort_values(by=sort_col, ascending=True).head(10)
        else:
            # For descending, take last 10 alphabetically
            filtered_df = df_for_sorting.sort_values(by=sort_col, ascending=True).tail(10)
    else:
        # For numeric columns, we take top 10 highest values (or lowest if ascending)
        filtered_df = df_for_sorting.sort_values(by=sort_col, ascending=sort_asc).head(10)
    
    # Reapply the final sort direction
    df = filtered_df.sort_values(by=sort_col, ascending=sort_asc)
    
    # Convert to styled table for display
    # First create a clean display version of the DataFrame
    display_df = pd.DataFrame()
    display_df['Buyer Name'] = df['buyer_name']
    display_df['Revenue'] = df['revenue_display']
    display_df['Revenue Change'] = df['revenue_change_display']
    display_df['Profit'] = df['profit_display']
    display_df['Profit Change'] = df['profit_change_display']
    display_df['Margin'] = df['margin_display']
    display_df['Margin Change'] = df['margin_change_display']
    
    # Create a function to style cells based on value
    def style_change_values(val):
        if val != 'N/A' and isinstance(val, str):
            if val.startswith('+'):
                return 'color: #10b981; font-weight: 600'
            elif val.startswith('-'):
                return 'color: #ef4444; font-weight: 600'
        return ''
    
    # Apply the styling using the newer map method instead of deprecated applymap
    styled_df = display_df.style.map(style_change_values, subset=['Revenue Change', 'Profit Change', 'Margin Change'])
    
    # Set column alignment and width
    styled_df = styled_df.set_properties(**{
        'text-align': 'left',
        'white-space': 'nowrap',
        'font-size': '0.9rem',
        'padding': '8px 12px',
    })
    
    # Get theme colors with defaults
    if theme_colors is None:
        theme_colors = {
            'background': '#ffffff',
            'text': '#000000',
            'border': '#e5e7eb',
            'card_bg': '#f9fafb',
            'hover_bg': '#f3f4f6'
        }
    
    # Set table style with theme-aware colors
    styled_df = styled_df.set_table_styles([
        {'selector': 'th', 'props': [
            ('font-weight', '600'),
            ('text-align', 'left'),
            ('padding', '8px 12px'),
            ('background-color', theme_colors['card_bg']),
            ('border-bottom', f"1px solid {theme_colors['border']}"),
            ('color', theme_colors['text']),
            ('font-size', '0.9rem')
        ]},
        {'selector': 'td', 'props': [
            ('padding', '8px 12px'),
            ('border-bottom', f"1px solid {theme_colors['border']}"),
            ('font-size', '0.9rem'),
            ('color', theme_colors['text'])
        ]},
        {'selector': 'tr:hover', 'props': [
            ('background-color', theme_colors['hover_bg'])
        ]},
    ])
    
    # Create a custom caption for the sorted column
    sort_indicator = '↑' if sort_asc else '↓'
    col_map = {
        'buyer_name': 'Buyer Name', 
        'revenue': 'Revenue',
        'revenue_change_pct': 'Revenue Change',
        'profit': 'Profit', 
        'profit_change_pct': 'Profit Change',
        'margin': 'Margin',
        'margin_change_pp': 'Margin Change'
    }
    
    # Add caption instead of modifying columns directly
    sort_col_display = col_map.get(sort_col, sort_col)
    
    # Put the sort buttons in a horizontal row
    col1, col2, col3, col4, col5, col6 = st.columns(6)
    
    # Column mapping for header clicks (without buyer_name)
    header_cols = [
        ('revenue', 'Revenue', col1),
        ('revenue_change_pct', 'Revenue Change', col2),
        ('profit', 'Profit', col3),
        ('profit_change_pct', 'Profit Change', col4),
        ('margin', 'Margin', col5),
        ('margin_change_pp', 'Margin Change', col6)
    ]
    
    # Define custom CSS for each button and place in columns
    for col_key, col_name, col in header_cols:
        sort_icon = ""
        if sort_col == col_key:
            sort_icon = " ↓" if not sort_asc else " ↑" 
            
        # Style for active vs inactive buttons
        if sort_col == col_key:
            button_style = "primary"
        else:
            button_style = "secondary"
            
        # Create the button
        btn_key = f"sort_{col_key}"
        if col.button(f"{col_name}{sort_icon}", key=btn_key, type=button_style, use_container_width=True):
            if st.session_state.sort_column == col_key:
                # Toggle sort direction if clicking the same column
                st.session_state.sort_ascending = not st.session_state.sort_ascending
            else:
                # Set new column with appropriate default sort direction
                st.session_state.sort_column = col_key
                # Default sort: descending for numeric columns
                st.session_state.sort_ascending = False
            st.rerun()
    
    # Add a horizontal line below the header buttons
    st.markdown("<hr style='margin-top: 0; margin-bottom: 0; height: 1px; border: none; background-color: #ddd;'>", unsafe_allow_html=True)
    
    # Display the dataframe
    styled_df = styled_df.set_table_styles([
        {'selector': 'thead', 'props': 'display:none;'},  # Hide the header row
    ], overwrite=False)
    st.dataframe(styled_df, hide_index=True, use_container_width=True)
    
    # Show sort information with more detailed explanation
    sort_dir_text = "ascending" if sort_asc else "descending"
    sort_text = f"*Table shows top 10 buyers by {sort_col_display}. When you click a different sort button, the list will show top 10 buyers for that metric.*"
    st.caption(sort_text)