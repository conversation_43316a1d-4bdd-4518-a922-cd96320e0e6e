"""
Customer Persona Intelligence System
Analyzes buyer data to create detailed customer personas and targeted marketing content
"""

import os
import json
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from database import get_session
from models import Product, Sale, Buyer, CompetitorScrape
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, extract


@dataclass
class CustomerPersona:
    """Data class for customer persona profiles"""
    segment_name: str
    business_type: str  # B2B or B2C
    brand_affiliation: str  # Eurhythmics or Intermuzika
    description: str
    characteristics: Dict[str, Any]
    preferences: Dict[str, Any]
    pain_points: List[str]
    marketing_approach: Dict[str, Any]
    content_themes: List[str]
    communication_style: str
    seasonal_patterns: Dict[str, Any]


class CustomerPersonaAgent:
    """Agent for analyzing buyer data and creating customer personas"""
    
    BUYER_CATEGORY_MAPPING = {
        'Nagyker': {
            'business_type': 'B2B',
            'brand': 'Eurhythmics',
            'description': 'Reseller partners - musical instrument and DJ stores',
            'target_audience': 'Retail store owners and buyers'
        },
        'Install': {
            'business_type': 'B2B', 
            'brand': 'Eurhythmics',
            'description': 'Installation partners - system integrators',
            'target_audience': 'Professional installers and system integrators'
        },
        'Kisker': {
            'business_type': 'B2C',
            'brand': 'Intermuzika', 
            'description': 'End customers - webshop and store buyers',
            'target_audience': 'Individual musicians and music enthusiasts'
        },
        'Export': {
            'business_type': 'B2B',
            'brand': 'Eurhythmics',
            'description': 'International B2B partners outside Hungary',
            'target_audience': 'International distributors and resellers'
        },
        'Rental': {
            'business_type': 'B2B',
            'brand': 'Eurhythmics',
            'description': 'Equipment rental partners',
            'target_audience': 'Event rental companies and production houses'
        },
        'Egyéb': {
            'business_type': 'Mixed',
            'brand': 'Both',
            'description': 'Other categories - suppliers, employees, music teachers',
            'target_audience': 'Diverse stakeholders and partners'
        },
        'General': {
            'business_type': 'Universal',
            'brand': 'Both',
            'description': 'General audience - broad market appeal',
            'target_audience': 'All customer segments and general market'
        }
    }
    
    def __init__(self, analysis_period_months: int = 12):
        self.session = None
        self.analysis_period_months = analysis_period_months
        self.analysis_start_date = datetime.now() - timedelta(days=analysis_period_months * 30)
        self.analysis_end_date = datetime.now()
    
    def analyze_buyer_segments(self) -> Dict[str, Any]:
        """Analyze all buyer segments and create detailed personas"""
        self.session = get_session()
        if not self.session:
            return {"error": "Database connection failed"}
        
        personas = {}
        
        try:
            for category, config in self.BUYER_CATEGORY_MAPPING.items():
                persona_data = self._analyze_segment(category, config)
                if persona_data:
                    personas[category] = persona_data
            
            return personas
            
        except Exception as e:
            return {"error": str(e)}
        finally:
            if self.session:
                self.session.close()
    
    def _analyze_segment(self, category: str, config: Dict) -> Optional[CustomerPersona]:
        """Analyze a specific buyer segment"""
        
        # Get buyer category mapping for database queries
        db_categories = self._get_db_categories_for_segment(category)
        
        # Analyze purchasing patterns
        purchasing_data = self._analyze_purchasing_patterns(db_categories)
        
        # Analyze product preferences
        product_preferences = self._analyze_product_preferences(db_categories)
        
        # Analyze seasonal patterns
        seasonal_patterns = self._analyze_seasonal_patterns(db_categories)
        
        # Analyze customer behavior
        behavior_analysis = self._analyze_customer_behavior(db_categories)
        
        # Create persona based on analysis
        persona = self._create_persona_profile(
            category, config, purchasing_data, product_preferences, 
            seasonal_patterns, behavior_analysis
        )
        
        return persona
    
    def _get_db_categories_for_segment(self, category: str) -> List[str]:
        """Map segment to database buyer categories based on actual data structure"""
        mapping = {
            'Kisker': ['Webes vásárló', 'Bolti vásárló', 'Magánszemély'],
            'Nagyker': ['Kiskereskedelmi egység'],  # Excluded 'Wholesale' as requested
            'Install': ['Installációs partner', 'Rendszer-Viszonteladó', 'Szerződés nélküli partner', 'Vegyes kereskedelem'],
            'Export': ['Export'],
            'Rental': ['Rental partner'],
            'Egyéb': ['Együttműködő partner', 'Zenetanár', 'Dolgozók', 'Beszállító', 'Wholesale'],  # Moved Wholesale here
            'General': []  # No specific buyer categories - uses all data
        }
        return mapping.get(category, [category])
    
    def _analyze_purchasing_patterns(self, db_categories: List[str]) -> Dict[str, Any]:
        """Analyze purchasing patterns for the segment"""
        
        if not self.session:
            return {
                'total_revenue': 0,
                'total_quantity': 0,
                'total_transactions': 0,
                'avg_order_value': 0,
                'avg_purchases_per_customer': 0,
                'customer_count': 0
            }
        
        # Handle General persona (all data)
        if not db_categories:
            query_filter = []
        else:
            query_filter = [Buyer.buyer_category.in_(db_categories)]
        
        try:
            # Total revenue and transactions with date filtering
            total_stats = self.session.query(
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                func.sum(Sale.quantity).label('total_quantity'),
                func.count(Sale.id).label('total_transactions'),
                func.avg(Sale.quantity * Sale.unit_price).label('avg_order_value')
            ).join(Buyer).filter(
                Buyer.buyer_category.in_(db_categories),
                Sale.sale_date >= self.analysis_start_date,
                Sale.sale_date <= self.analysis_end_date
            ).first()
            
            # Purchase frequency analysis with date filtering
            frequency_analysis = self.session.query(
                Buyer.buyer_id,
                func.count(Sale.id).label('purchase_count'),
                func.min(Sale.sale_date).label('first_purchase'),
                func.max(Sale.sale_date).label('last_purchase')
            ).join(Sale).filter(
                Buyer.buyer_category.in_(db_categories),
                Sale.sale_date >= self.analysis_start_date,
                Sale.sale_date <= self.analysis_end_date
            ).group_by(Buyer.buyer_id).all()
            
            # Calculate average purchase frequency
            avg_purchases_per_customer = sum(f.purchase_count for f in frequency_analysis) / len(frequency_analysis) if frequency_analysis else 0
            
            return {
                'total_revenue': float(total_stats.total_revenue or 0) if total_stats else 0,
                'total_quantity': int(total_stats.total_quantity or 0) if total_stats else 0,
                'total_transactions': int(total_stats.total_transactions or 0) if total_stats else 0,
                'avg_order_value': float(total_stats.avg_order_value or 0) if total_stats else 0,
                'avg_purchases_per_customer': round(avg_purchases_per_customer, 2),
                'customer_count': len(frequency_analysis)
            }
        except Exception as e:
            return {
                'total_revenue': 0,
                'total_quantity': 0,
                'total_transactions': 0,
                'avg_order_value': 0,
                'avg_purchases_per_customer': 0,
                'customer_count': 0,
                'error': str(e)
            }
    
    def _analyze_product_preferences(self, db_categories: List[str]) -> Dict[str, Any]:
        """Analyze product category preferences"""
        
        # Top brands by revenue
        top_brands = self.session.query(
            Product.brand,
            func.sum(Sale.quantity * Sale.unit_price).label('revenue'),
            func.sum(Sale.quantity).label('quantity')
        ).join(Sale).join(Buyer).filter(
            Buyer.buyer_category.in_(db_categories)
        ).group_by(Product.brand).order_by(desc('revenue')).limit(10).all()
        
        # Top product groups
        top_categories = self.session.query(
            Product.product_group,
            func.sum(Sale.quantity * Sale.unit_price).label('revenue'),
            func.sum(Sale.quantity).label('quantity')
        ).join(Sale).join(Buyer).filter(
            Buyer.buyer_category.in_(db_categories)
        ).group_by(Product.product_group).order_by(desc('revenue')).limit(10).all()
        
        # Price range analysis
        price_analysis = self.session.query(
            func.min(Sale.unit_price).label('min_price'),
            func.max(Sale.unit_price).label('max_price'),
            func.avg(Sale.unit_price).label('avg_price'),
            func.percentile_cont(0.5).within_group(Sale.unit_price).label('median_price')
        ).join(Buyer).filter(Buyer.buyer_category.in_(db_categories)).first()
        
        return {
            'top_brands': [
                {'brand': b.brand, 'revenue': float(b.revenue), 'quantity': int(b.quantity)}
                for b in top_brands
            ],
            'top_categories': [
                {'category': c.product_group, 'revenue': float(c.revenue), 'quantity': int(c.quantity)}
                for c in top_categories
            ],
            'price_preferences': {
                'min_price': float(price_analysis.min_price or 0),
                'max_price': float(price_analysis.max_price or 0),
                'avg_price': float(price_analysis.avg_price or 0),
                'median_price': float(price_analysis.median_price or 0)
            }
        }
    
    def _analyze_seasonal_patterns(self, db_categories: List[str]) -> Dict[str, Any]:
        """Analyze seasonal purchasing patterns"""
        
        # Monthly sales patterns
        monthly_sales = self.session.query(
            extract('month', Sale.sale_date).label('month'),
            func.sum(Sale.quantity * Sale.unit_price).label('revenue'),
            func.count(Sale.id).label('transactions')
        ).join(Buyer).filter(
            Buyer.buyer_category.in_(db_categories)
        ).group_by(extract('month', Sale.sale_date)).order_by('month').all()
        
        # Quarterly patterns
        quarterly_sales = self.session.query(
            extract('quarter', Sale.sale_date).label('quarter'),
            func.sum(Sale.quantity * Sale.unit_price).label('revenue')
        ).join(Buyer).filter(
            Buyer.buyer_category.in_(db_categories)
        ).group_by(extract('quarter', Sale.sale_date)).order_by('quarter').all()
        
        return {
            'monthly_patterns': [
                {'month': int(m.month), 'revenue': float(m.revenue), 'transactions': int(m.transactions)}
                for m in monthly_sales
            ],
            'quarterly_patterns': [
                {'quarter': int(q.quarter), 'revenue': float(q.revenue)}
                for q in quarterly_sales
            ]
        }
    
    def _analyze_customer_behavior(self, db_categories: List[str]) -> Dict[str, Any]:
        """Analyze customer behavior patterns"""
        
        # Geographic distribution
        geographic_dist = self.session.query(
            Buyer.country,
            func.count(Buyer.buyer_id).label('customer_count'),
            func.sum(Sale.quantity * Sale.unit_price).label('revenue')
        ).join(Sale).filter(
            Buyer.buyer_category.in_(db_categories)
        ).group_by(Buyer.country).order_by(desc('revenue')).all()
        
        # Web access preferences
        web_access_stats = self.session.query(
            Buyer.web_access,
            func.count(Buyer.buyer_id).label('count')
        ).filter(Buyer.buyer_category.in_(db_categories)).group_by(Buyer.web_access).all()
        
        return {
            'geographic_distribution': [
                {'country': g.country, 'customer_count': int(g.customer_count), 'revenue': float(g.revenue)}
                for g in geographic_dist
            ],
            'web_access_preference': [
                {'has_web_access': bool(w.web_access), 'count': int(w.count)}
                for w in web_access_stats
            ]
        }
    
    def _create_persona_profile(self, category: str, config: Dict, purchasing_data: Dict, 
                               product_preferences: Dict, seasonal_patterns: Dict, 
                               behavior_analysis: Dict) -> CustomerPersona:
        """Create a comprehensive persona profile"""
        
        # Determine characteristics based on data
        characteristics = {
            'avg_order_value': purchasing_data['avg_order_value'],
            'purchase_frequency': purchasing_data['avg_purchases_per_customer'],
            'price_sensitivity': self._determine_price_sensitivity(product_preferences['price_preferences']),
            'brand_loyalty': self._calculate_brand_loyalty(product_preferences['top_brands']),
            'digital_preference': self._calculate_digital_preference(behavior_analysis),
            'geographic_focus': behavior_analysis['geographic_distribution'][:3] if behavior_analysis['geographic_distribution'] else []
        }
        
        # Determine preferences
        preferences = {
            'preferred_brands': product_preferences['top_brands'][:5],
            'preferred_categories': product_preferences['top_categories'][:5],
            'peak_seasons': self._identify_peak_seasons(seasonal_patterns),
            'communication_channels': self._determine_preferred_channels(config['business_type'])
        }
        
        # Define pain points based on segment
        pain_points = self._define_pain_points(category, characteristics)
        
        # Marketing approach
        marketing_approach = self._define_marketing_approach(category, config, characteristics)
        
        # Content themes
        content_themes = self._define_content_themes(category, product_preferences)
        
        # Communication style
        communication_style = self._define_communication_style(config['business_type'])
        
        return CustomerPersona(
            segment_name=category,
            business_type=config['business_type'],
            brand_affiliation=config['brand'],
            description=config['description'],
            characteristics=characteristics,
            preferences=preferences,
            pain_points=pain_points,
            marketing_approach=marketing_approach,
            content_themes=content_themes,
            communication_style=communication_style,
            seasonal_patterns=seasonal_patterns
        )
    
    def _determine_price_sensitivity(self, price_data: Dict) -> str:
        """Determine price sensitivity level"""
        avg_price = price_data['avg_price']
        if avg_price > 100000:  # 100k HUF
            return "Low - Premium focused"
        elif avg_price > 50000:  # 50k HUF
            return "Medium - Value conscious"
        else:
            return "High - Budget focused"
    
    def _calculate_brand_loyalty(self, top_brands: List[Dict]) -> str:
        """Calculate brand loyalty based on brand concentration"""
        if not top_brands:
            return "Unknown"
        
        total_revenue = sum(b['revenue'] for b in top_brands)
        top_brand_share = top_brands[0]['revenue'] / total_revenue if total_revenue > 0 else 0
        
        if top_brand_share > 0.5:
            return "High - Strong brand preference"
        elif top_brand_share > 0.3:
            return "Medium - Some brand preference"
        else:
            return "Low - Brand agnostic"
    
    def _calculate_digital_preference(self, behavior_data: Dict) -> str:
        """Calculate digital preference"""
        web_access_data = behavior_data.get('web_access_preference', [])
        if not web_access_data:
            return "Unknown"
        
        total_customers = sum(w['count'] for w in web_access_data)
        web_enabled = sum(w['count'] for w in web_access_data if w['has_web_access'])
        
        web_percentage = web_enabled / total_customers if total_customers > 0 else 0
        
        if web_percentage > 0.7:
            return "High - Digital native"
        elif web_percentage > 0.4:
            return "Medium - Hybrid approach"
        else:
            return "Low - Traditional channels"
    
    def _identify_peak_seasons(self, seasonal_data: Dict) -> List[str]:
        """Identify peak purchasing seasons"""
        monthly_patterns = seasonal_data.get('monthly_patterns', [])
        if not monthly_patterns:
            return []
        
        # Sort by revenue and get top 3 months
        sorted_months = sorted(monthly_patterns, key=lambda x: x['revenue'], reverse=True)[:3]
        
        month_names = {
            1: "January", 2: "February", 3: "March", 4: "April",
            5: "May", 6: "June", 7: "July", 8: "August",
            9: "September", 10: "October", 11: "November", 12: "December"
        }
        
        return [month_names[m['month']] for m in sorted_months]
    
    def _determine_preferred_channels(self, business_type: str) -> List[str]:
        """Determine preferred communication channels"""
        if business_type == 'B2B':
            return ["Email", "LinkedIn", "Trade publications", "Industry events"]
        else:
            return ["Email", "Facebook", "Instagram", "Website", "In-store"]
    
    def _define_pain_points(self, category: str, characteristics: Dict) -> List[str]:
        """Define pain points for each segment"""
        pain_points_map = {
            'Nagyker': [
                "Inventory management complexity",
                "Margin pressure from online retailers",
                "Need for product training and support",
                "Seasonal demand fluctuations"
            ],
            'Install': [
                "Technical specification requirements",
                "Project timeline pressures", 
                "Integration complexity",
                "Client education needs"
            ],
            'Kisker': [
                "Product selection overwhelm",
                "Price comparison pressure",
                "Technical knowledge gaps",
                "After-sales support concerns"
            ],
            'Export': [
                "Currency fluctuation risks",
                "Shipping and logistics complexity",
                "Local market regulation compliance",
                "Cultural and language barriers"
            ],
            'Rental': [
                "Equipment durability concerns",
                "Maintenance and repair needs",
                "Inventory optimization",
                "Seasonal demand peaks"
            ]
        }
        return pain_points_map.get(category, [])
    
    def _define_marketing_approach(self, category: str, config: Dict, characteristics: Dict) -> Dict[str, Any]:
        """Define marketing approach for each segment"""
        approaches = {
            'Nagyker': {
                "focus": "Business growth and profitability",
                "key_messages": ["Margin optimization", "Inventory support", "Marketing co-op"],
                "content_style": "Professional, ROI-focused",
                "decision_factors": ["Profit margins", "Customer demand", "Brand reputation"]
            },
            'Install': {
                "focus": "Technical excellence and project success",
                "key_messages": ["Technical specifications", "Installation support", "Project reliability"],
                "content_style": "Technical, detailed, solution-oriented",
                "decision_factors": ["Technical specs", "Reliability", "Support quality"]
            },
            'Kisker': {
                "focus": "Personal satisfaction and value",
                "key_messages": ["Quality instruments", "Fair pricing", "Expert advice"],
                "content_style": "Inspiring, educational, personal",
                "decision_factors": ["Sound quality", "Price", "Brand reputation"]
            },
            'Export': {
                "focus": "International partnership and growth",
                "key_messages": ["Global brand access", "Competitive pricing", "Logistics support"],
                "content_style": "Professional, partnership-focused",
                "decision_factors": ["Brand portfolio", "Pricing", "Support quality"]
            },
            'Rental': {
                "focus": "Equipment reliability and profitability",
                "key_messages": ["Durable equipment", "Maintenance support", "Rental optimization"],
                "content_style": "Professional, reliability-focused",
                "decision_factors": ["Equipment durability", "Maintenance costs", "Rental demand"]
            }
        }
        return approaches.get(category, {})
    
    def _define_content_themes(self, category: str, product_preferences: Dict) -> List[str]:
        """Define content themes for each segment"""
        base_themes = {
            'Nagyker': [
                "Retail sales strategies",
                "New product launches",
                "Seasonal promotions",
                "Customer education materials"
            ],
            'Install': [
                "Technical installation guides",
                "System design best practices", 
                "Case studies and projects",
                "Product specifications"
            ],
            'Kisker': [
                "Product reviews and comparisons",
                "How-to guides and tutorials",
                "Artist spotlights",
                "Music education content"
            ],
            'Export': [
                "Market expansion opportunities",
                "International success stories",
                "Regulatory compliance updates",
                "Cultural market insights"
            ],
            'Rental': [
                "Equipment maintenance tips",
                "Event production guides",
                "Industry trend analysis",
                "Equipment ROI optimization"
            ]
        }
        
        themes = base_themes.get(category, [])
        
        # Add brand-specific themes based on top brands
        top_brands = product_preferences.get('top_brands', [])[:3]
        for brand_data in top_brands:
            themes.append(f"{brand_data['brand']} product highlights")
        
        return themes
    
    def _define_communication_style(self, business_type: str) -> str:
        """Define communication style based on business type"""
        if business_type == 'B2B':
            return "Professional, technical, ROI-focused, partnership-oriented"
        else:
            return "Friendly, educational, inspiring, personal"


class PersonaContentGenerator:
    """Generate targeted content based on customer personas"""
    
    def __init__(self):
        self.api_key = os.environ.get('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1/chat/completions"
    
    def generate_persona_targeted_content(self, persona: CustomerPersona, content_type: str, 
                                        specific_request: str, language: str = "en", 
                                        uploaded_content: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate content specifically targeted to a persona"""
        
        # Build reference material context from uploaded files
        reference_context = self._build_reference_context(uploaded_content) if uploaded_content and uploaded_content.get('total_files', 0) > 0 else ""
        
        prompt = self._create_persona_prompt(persona, content_type, specific_request, language, reference_context)
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "gpt-4o",
            "messages": [
                {
                    "role": "system",
                    "content": f"You are a marketing expert specializing in {persona.business_type} content for the music industry. Create content that resonates with {persona.segment_name} customers."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()
            
            content = result['choices'][0]['message']['content']
            return {
                "success": True,
                "content": content,
                "persona_segment": persona.segment_name,
                "brand_affiliation": persona.brand_affiliation
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def _build_reference_context(self, uploaded_content: Optional[Dict]) -> str:
        """Build context from uploaded reference materials"""
        if not uploaded_content or uploaded_content.get('total_files', 0) == 0:
            return ""
        
        context = "\n\nREFERENCE MATERIALS:\n"
        
        # Add document content
        for doc in uploaded_content.get('documents', []):
            context += f"\n--- {doc['name']} ---\n"
            context += doc.get('content', '')[:1500]  # Limit content
            context += "\n"
        
        # Add image descriptions
        for img in uploaded_content.get('images', []):
            context += f"\n--- Image: {img['name']} ---\n"
            context += f"Product image available for visual reference ({img['dimensions'][0]}x{img['dimensions'][1]})\n"
        
        return context
    
    def _create_persona_prompt(self, persona: CustomerPersona, content_type: str, 
                              specific_request: str, language: str, reference_context: str = "") -> str:
        """Create a detailed prompt based on persona characteristics"""
        
        lang_instruction = "Write in Hungarian" if language == "hu" else "Write in English"
        
        return f"""
        {lang_instruction}. Create {content_type} for {persona.segment_name} customers based on this request: {specific_request}
        
        PERSONA PROFILE:
        Segment: {persona.segment_name} ({persona.business_type} - {persona.brand_affiliation})
        Description: {persona.description}
        
        CHARACTERISTICS:
        - Average order value: {persona.characteristics.get('avg_order_value', 'N/A')} HUF
        - Purchase frequency: {persona.characteristics.get('purchase_frequency', 'N/A')} times
        - Price sensitivity: {persona.characteristics.get('price_sensitivity', 'N/A')}
        - Brand loyalty: {persona.characteristics.get('brand_loyalty', 'N/A')}
        - Digital preference: {persona.characteristics.get('digital_preference', 'N/A')}
        
        PREFERENCES:
        - Top brands: {[b['brand'] for b in persona.preferences.get('preferred_brands', [])[:3]]}
        - Top categories: {[c['category'] for c in persona.preferences.get('preferred_categories', [])[:3]]}
        - Peak seasons: {persona.preferences.get('peak_seasons', [])}
        
        PAIN POINTS TO ADDRESS:
        {chr(10).join(f"- {pain}" for pain in persona.pain_points)}
        
        MARKETING APPROACH:
        - Focus: {persona.marketing_approach.get('focus', 'N/A')}
        - Key messages: {persona.marketing_approach.get('key_messages', [])}
        - Content style: {persona.marketing_approach.get('content_style', 'N/A')}
        - Decision factors: {persona.marketing_approach.get('decision_factors', [])}
        
        COMMUNICATION STYLE: {persona.communication_style}
        
        CONTENT REQUIREMENTS:
        - Address their specific pain points
        - Use their preferred communication style
        - Reference their preferred brands/categories when relevant
        - Focus on their key decision factors
        - Align with {persona.brand_affiliation} brand positioning
        - Consider their {persona.business_type} context
        
        Generate content that speaks directly to this persona's needs, preferences, and business context.
        {reference_context}
        """


# Example usage and testing
if __name__ == "__main__":
    # Test persona analysis
    persona_agent = CustomerPersonaAgent()
    personas = persona_agent.analyze_buyer_segments()
    
    # Print persona summary
    for segment, persona in personas.items():
        if isinstance(persona, CustomerPersona):
            print(f"\n=== {segment} PERSONA ===")
            print(f"Business Type: {persona.business_type}")
            print(f"Brand: {persona.brand_affiliation}")
            print(f"Description: {persona.description}")
            print(f"Key Characteristics: {persona.characteristics}")