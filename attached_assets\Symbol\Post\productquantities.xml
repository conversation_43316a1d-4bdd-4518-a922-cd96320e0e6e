<?xml version="1.0" encoding="UTF-8" ?>
<ProductQuantities>
    <ProductQuantity>
        <Warehouse>-1</Warehouse> --<PERSON><PERSON><PERSON><PERSON> Symbol besl<PERSON> azonosítója (WareHouse.Id)
        <WarehouseName>Központ</WarehouseName> --<PERSON><PERSON><PERSON><PERSON> neve (Warehouse.Name)
		<WarehouseSite>1</WarehouseSite> --<PERSON><PERSON><PERSON><PERSON> cég azon<PERSON>ít<PERSON> (Warehouse.Site)
		<Product>482</Product> --Termék Symbol belső azonosítója (Product.Id)
		<ProductCode>547P</ProductCode> --Termékkód (Product.Code)
        <Quantity>48</Quantity> --Mennyiség (WareHouseBalance.Balance)
        <StrictAllocate>5</StrictAllocate> --<PERSON><PERSON><PERSON><PERSON><PERSON> foglalt (WareHouseBalance.StrictAllocated)
        <NonStrictAllocate>12</NonStrictAllocate> --Nem szigor<PERSON> foglalt
    </ProductQuantity>
	<ProductQuantity>
        <Warehouse>-1</Warehouse> --<PERSON><PERSON><PERSON>r Symbol besl<PERSON> azonosítója (WareHouse.Id)
        <WarehouseName>Központ</WarehouseName> --Rakt<PERSON>r neve (Warehouse.Name)
		<WarehouseSite>1</WarehouseSite> --Raktár cég azonosító (Warehouse.Site)
		<Product>582</Product> --Termék Symbol belső azonosítója (Product.Id)
		<ProductCode>547FGP</ProductCode> --Termékkód (Product.Code)
        <Quantity>53</Quantity> --Mennyiség (WareHouseBalance.Balance)
        <StrictAllocate>3</StrictAllocate> --Szigorúan foglalt (WareHouseBalance.StrictAllocated)
        <NonStrictAllocate>10</NonStrictAllocate> --Nem szigorúan foglalt
    </ProductQuantity>
</ProductQuantities>
