# ERP Analytics Dashboard Project Structure

## Project Overview

The ERP Analytics Dashboard is a comprehensive business intelligence application built with Streamlit that visualizes and analyzes data from an ERP system. It provides insights into sales, purchases, inventory, and customer performance through interactive visualizations and detailed metrics.

## Key Technologies

- **Framework**: Streamlit for interactive web interface
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Data Processing**: Pandas for data manipulation and analysis
- **Visualization**: Plotly for interactive charts and graphs
- **Environment Support**: Deployable on Replit, AWS, and via Docker

## Directory Structure

```
/
├── app_pages/                # Page modules for the multi-page application
│   ├── __init__.py           # Makes the directory a package
│   ├── overview.py           # Dashboard overview page
│   ├── sales_analytics.py    # Sales performance analytics
│   ├── stock_analytics.py    # Inventory and stock analytics
│   ├── purchase_analytics.py # Purchase and supplier analytics
│   ├── upload.py             # Data import and management
│   └── settings.py           # Application settings
│
├── assets/                   # Static assets for the application
│   └── placeholder_image.svg # Fallback image
│
├── components/               # Reusable UI and functional components
│   ├── customer_categories_rendering.py # Customer category visualization
│   ├── data_loader.py        # Excel data import functionality
│   ├── overview_visualizations.py # Charts for overview dashboard
│   ├── product_portfolio_analysis.py # Product performance analysis
│   ├── sales_overview.py     # Sales metrics and visualizations
│   ├── stock_analytics_functions.py # Stock analysis utilities
│   └── ui.py                 # Shared UI components and styling
│
├── docs/                     # Documentation files
│   ├── aws_deployment_guide.md   # AWS deployment instructions
│   ├── docker_deployment_guide.md # Docker deployment guide
│   └── project_structure.md  # This document
│
├── .streamlit/               # Streamlit configuration
│   └── config.toml           # Streamlit settings
│
├── app.py                    # Main application entry point
├── config.py                 # Application configuration
├── database.py               # Database connection and session management
├── models.py                 # SQLAlchemy ORM models
├── utils.py                  # Utility functions for data processing
├── pyproject.toml            # Python project dependencies
├── Dockerfile                # Docker container configuration
└── docker-compose.yml        # Docker Compose services definition
```

## Core Modules Explained

### Application Entry Point

- **app.py**: Main entry point that initializes the Streamlit application, sets up navigation, and routes to the appropriate pages.

### Configuration and Database

- **config.py**: Handles environment-specific configuration with the `Config` class, supporting Replit, AWS, and Docker environments.
- **database.py**: Manages database connections and sessions with functions like `init_db()`, `get_session()`, and connection validation.
- **models.py**: Defines the SQLAlchemy ORM models for all database tables including Buyers, Products, Sales, Purchases, etc.

### App Pages

- **overview.py**: Dashboard landing page with KPIs and high-level metrics across all areas.
- **sales_analytics.py**: Detailed sales analysis with filters for time periods, customers, and products.
- **stock_analytics.py**: Inventory analysis with warehouse and product filters.
- **purchase_analytics.py**: Supplier and purchasing analytics.
- **upload.py**: Data import functionality with Excel file mapping.
- **settings.py**: Application configuration and customization options.

### Components

- **customer_categories_rendering.py**: Specialized visualization for customer category performance.
- **data_loader.py**: Excel file upload, validation, and database import functionality.
- **overview_visualizations.py**: Chart components for the overview dashboard.
- **product_portfolio_analysis.py**: Product performance matrix and portfolio analysis.
- **sales_overview.py**: Sales trend and comparison visualizations.
- **stock_analytics_functions.py**: Stock movement and valuation analysis.
- **ui.py**: Shared UI components like filters, cards, and custom styling.

### Utilities

- **utils.py**: Helper functions for data processing, formatting, and database operations.

## Data Flow

1. **Data Import**: Excel files are uploaded through the upload page, processed by components/data_loader.py, and stored in the PostgreSQL database using the models defined in models.py.

2. **Data Query**: User interactions with filters trigger database queries through SQLAlchemy, fetching data based on the selected criteria.

3. **Data Processing**: Raw data from the database is processed using pandas in various component modules to calculate metrics, aggregations, and prepare for visualization.

4. **Visualization**: Processed data is rendered into interactive charts and tables using Plotly and Streamlit components.

## Application Design Principles

- **Modular Architecture**: Functionality is separated into logical modules for better maintenance.
- **Interactive Filtering**: All analytics pages include interactive filters that affect all visualizations on the page.
- **Consistent Styling**: Uniform styling and UI patterns throughout the application.
- **Performance Optimization**: Database queries are optimized with proper indexing and query construction.
- **Responsive Design**: The UI adapts to different screen sizes and devices.

## Deployment Options

- **Replit**: The application is configured to run seamlessly on Replit with automatic database provisioning.
- **AWS**: Deployment guide available for AWS with environment variable configuration.
- **Docker**: Container-based deployment for local or cloud-based hosting.