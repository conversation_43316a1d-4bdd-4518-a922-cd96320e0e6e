[project]
name = "repl-nix-workspace"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.11"
dependencies = [
    "openpyxl>=3.1.5",
    "plotly>=6.0.1",
    "psycopg2-binary>=2.9.10",
    "sqlalchemy>=2.0.40",
    "streamlit-aggrid>=1.1.5.post1",
    "trafilatura>=2.0.0",
    "beautifulsoup4>=4.13.4",
    "requests>=2.32.3",
    "streamlit-authenticator>=0.4.2",
    "bcrypt>=4.3.0",
    "anthropic>=0.54.0",
    "openai>=1.88.0",
    "langgraph>=0.4.8",
    "langchain>=0.3.25",
    "langchain-openai>=0.3.24",
    "langchain-core>=0.3.65",
    "langgraph-checkpoint>=2.1.0",
    "pypdf2>=3.0.1",
    "python-docx>=1.2.0",
    "pillow>=11.2.1",
    "pandas==2.0.3",
    "numpy==1.24.3",
    "streamlit>=1.45.0",
]
