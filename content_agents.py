"""
Marketing Content Creation Agents using LangGraph
Multi-agent system for creating product articles, social media posts, and case studies
"""

import os
import json
import requests
from typing import Dict, List, Any, TypedDict, Literal
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.graph import StateGraph, END, START
from langgraph.checkpoint.memory import MemorySaver

from database import get_session
from models import Product, Sale, Buyer, CompetitorScrape
from sqlalchemy.orm import Session
from sqlalchemy import func, desc


class ContentCreationState(TypedDict):
    """State for the content creation workflow"""
    user_request: str
    content_type: Literal["product_article", "facebook_post", "linkedin_post", "case_study"]
    target_language: Literal["en", "hu"]
    
    # Research data
    product_data: Dict[str, Any]
    market_research: Dict[str, Any]
    competitor_data: Dict[str, Any]
    
    # Content strategy
    content_strategy: Dict[str, Any]
    target_audience: str
    key_messages: List[str]
    
    # Generated content
    draft_content: str
    final_content: str
    metadata: Dict[str, Any]
    
    # Workflow control
    current_step: str
    errors: List[str]
    quality_score: float


class PerplexitySearchAgent:
    """Agent for web research using Perplexity API"""
    
    def __init__(self):
        self.api_key = os.environ.get('PERPLEXITY_API_KEY')
        self.base_url = "https://api.perplexity.ai/chat/completions"
    
    def search(self, query: str, domain_filter: List[str] = None) -> Dict[str, Any]:
        """Search for information using Perplexity API"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            "model": "llama-3.1-sonar-small-128k-online",
            "messages": [
                {
                    "role": "system",
                    "content": "You are a research assistant for a music instrument retail company. Provide detailed, accurate information with sources."
                },
                {
                    "role": "user",
                    "content": query
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.2,
            "top_p": 0.9,
            "search_domain_filter": domain_filter or [],
            "return_images": False,
            "return_related_questions": False,
            "search_recency_filter": "month",
            "stream": False
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}


class ProductIntelligenceAgent:
    """Agent for extracting product data from ERP database"""
    
    def get_product_performance(self, product_query: str) -> Dict[str, Any]:
        """Get product sales performance and inventory data"""
        session = get_session()
        try:
            # Search for products by name or code
            products = session.query(Product).filter(
                (Product.product_name.ilike(f'%{product_query}%')) |
                (Product.product_id.ilike(f'%{product_query}%')) |
                (Product.brand.ilike(f'%{product_query}%'))
            ).limit(10).all()
            
            if not products:
                return {"error": "No products found matching the query"}
            
            product_data = []
            for product in products:
                # Get sales data
                sales = session.query(
                    func.sum(Sale.quantity).label('total_quantity'),
                    func.sum(Sale.quantity * Sale.unit_price).label('total_revenue'),
                    func.count(Sale.id).label('total_transactions')
                ).filter(Sale.product_id == product.product_id).first()
                
                # Get competitor pricing
                competitor = session.query(CompetitorScrape).filter(
                    CompetitorScrape.product_code == product.product_id
                ).first()
                
                product_info = {
                    "product_id": product.product_id,
                    "name": product.product_name,
                    "brand": product.brand,
                    "product_group": product.product_group,
                    "vendor_group": product.vendor_product_group,
                    "sales_data": {
                        "quantity": int(sales.total_quantity or 0),
                        "revenue": float(sales.total_revenue or 0),
                        "transactions": int(sales.total_transactions or 0)
                    },
                    "competitor_pricing": {}
                }
                
                if competitor:
                    product_info["competitor_pricing"] = {
                        "thomann_price": competitor.thomann_price,
                        "muziker_price": competitor.muziker_price,
                        "thomann_stock": competitor.thomann_stock,
                        "muziker_stock": competitor.muziker_stock
                    }
                
                product_data.append(product_info)
            
            return {"products": product_data}
            
        except Exception as e:
            return {"error": str(e)}
        finally:
            session.close()
    
    def get_trending_products(self, category: str = None) -> Dict[str, Any]:
        """Get trending products by sales volume"""
        session = get_session()
        try:
            query = session.query(
                Product.product_id,
                Product.product_name,
                Product.brand,
                Product.product_group,
                func.sum(Sale.quantity).label('total_quantity'),
                func.sum(Sale.quantity * Sale.unit_price).label('total_revenue')
            ).join(Sale).group_by(
                Product.product_id,
                Product.product_name,
                Product.brand,
                Product.product_group
            )
            
            if category:
                query = query.filter(Product.product_group.ilike(f'%{category}%'))
            
            trending = query.order_by(desc('total_revenue')).limit(10).all()
            
            return {
                "trending_products": [
                    {
                        "product_id": p.product_id,
                        "name": p.product_name,
                        "brand": p.brand,
                        "category": p.product_group,
                        "quantity_sold": int(p.total_quantity),
                        "revenue": float(p.total_revenue)
                    }
                    for p in trending
                ]
            }
            
        except Exception as e:
            return {"error": str(e)}
        finally:
            session.close()


class ContentCreationAgents:
    """Main orchestrator for content creation agents"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model="gpt-4o",  # the newest OpenAI model is "gpt-4o" which was released May 13, 2024
            temperature=0.7,
            api_key=os.environ.get('OPENAI_API_KEY')
        )
        self.perplexity = PerplexitySearchAgent()
        self.product_intel = ProductIntelligenceAgent()
        
        # Build the workflow graph
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow"""
        workflow = StateGraph(ContentCreationState)
        
        # Add nodes
        workflow.add_node("content_planner", self.content_planner)
        workflow.add_node("research_agent", self.research_agent)
        workflow.add_node("product_intelligence", self.product_intelligence)
        workflow.add_node("content_strategist", self.content_strategist)
        workflow.add_node("writer_agent", self.writer_agent)
        workflow.add_node("localization_agent", self.localization_agent)
        workflow.add_node("quality_agent", self.quality_agent)
        
        # Define the flow
        workflow.add_edge(START, "content_planner")
        workflow.add_edge("content_planner", "research_agent")
        workflow.add_edge("research_agent", "product_intelligence")
        workflow.add_edge("product_intelligence", "content_strategist")
        workflow.add_edge("content_strategist", "writer_agent")
        workflow.add_edge("writer_agent", "localization_agent")
        workflow.add_edge("localization_agent", "quality_agent")
        workflow.add_edge("quality_agent", END)
        
        # Set entry point
        workflow.set_entry_point("content_planner")
        
        return workflow.compile(checkpointer=MemorySaver())
    
    def content_planner(self, state: ContentCreationState) -> ContentCreationState:
        """Analyze user request and plan content creation"""
        user_request = state["user_request"]
        
        planning_prompt = f"""
        Analyze this content creation request and determine the content type and approach:
        Request: {user_request}
        
        Determine:
        1. Content type (product_article, facebook_post, linkedin_post, case_study)
        2. Target language (en or hu)
        3. Product/topic focus
        4. Target audience
        
        Respond in JSON format with your analysis.
        """
        
        response = self.llm.invoke([
            SystemMessage(content="You are a content planning specialist for a music instrument retailer."),
            HumanMessage(content=planning_prompt)
        ])
        
        try:
            plan = json.loads(response.content)
            state["content_type"] = plan.get("content_type", "product_article")
            state["target_language"] = plan.get("target_language", "en")
            state["current_step"] = "content_planner"
        except:
            state["content_type"] = "product_article"
            state["target_language"] = "en"
            state["errors"] = state.get("errors", []) + ["Failed to parse planning response"]
        
        return state
    
    def research_agent(self, state: ContentCreationState) -> ContentCreationState:
        """Conduct web research using Perplexity"""
        user_request = state["user_request"]
        
        # Create research query based on content type
        if state["content_type"] == "product_article":
            query = f"Latest trends and reviews for {user_request} music instruments 2024 2025"
        elif state["content_type"] in ["facebook_post", "linkedin_post"]:
            query = f"Social media marketing trends for {user_request} music industry"
        else:  # case_study
            query = f"Professional audio installation case studies {user_request}"
        
        research_result = self.perplexity.search(query)
        state["market_research"] = research_result
        state["current_step"] = "research_agent"
        
        return state
    
    def product_intelligence(self, state: ContentCreationState) -> ContentCreationState:
        """Extract relevant product data from ERP"""
        user_request = state["user_request"]
        
        # Extract product information
        product_data = self.product_intel.get_product_performance(user_request)
        trending_data = self.product_intel.get_trending_products()
        
        state["product_data"] = product_data
        state["competitor_data"] = trending_data
        state["current_step"] = "product_intelligence"
        
        return state
    
    def content_strategist(self, state: ContentCreationState) -> ContentCreationState:
        """Develop content strategy based on research and data"""
        strategy_prompt = f"""
        Based on the research and product data, create a content strategy for:
        Content Type: {state['content_type']}
        Language: {state['target_language']}
        
        Research Data: {json.dumps(state.get('market_research', {}), indent=2)}
        Product Data: {json.dumps(state.get('product_data', {}), indent=2)}
        
        Create a content strategy including:
        1. Target audience
        2. Key messages (3-5 points)
        3. Tone and style
        4. Content structure
        5. Call-to-action
        
        Respond in JSON format.
        """
        
        response = self.llm.invoke([
            SystemMessage(content="You are a content strategy expert for music instrument marketing."),
            HumanMessage(content=strategy_prompt)
        ])
        
        try:
            strategy = json.loads(response.content)
            state["content_strategy"] = strategy
            state["target_audience"] = strategy.get("target_audience", "music enthusiasts")
            state["key_messages"] = strategy.get("key_messages", [])
        except:
            state["errors"] = state.get("errors", []) + ["Failed to parse strategy response"]
        
        state["current_step"] = "content_strategist"
        return state
    
    def writer_agent(self, state: ContentCreationState) -> ContentCreationState:
        """Generate the actual content"""
        content_type = state["content_type"]
        language = state["target_language"]
        strategy = state.get("content_strategy", {})
        product_data = state.get("product_data", {})
        
        if content_type == "product_article":
            writing_prompt = self._get_article_prompt(strategy, product_data, language)
        elif content_type == "facebook_post":
            writing_prompt = self._get_facebook_prompt(strategy, product_data, language)
        elif content_type == "linkedin_post":
            writing_prompt = self._get_linkedin_prompt(strategy, product_data, language)
        else:  # case_study
            writing_prompt = self._get_case_study_prompt(strategy, product_data, language)
        
        response = self.llm.invoke([
            SystemMessage(content="You are a professional content writer specializing in music industry marketing."),
            HumanMessage(content=writing_prompt)
        ])
        
        state["draft_content"] = response.content
        state["current_step"] = "writer_agent"
        return state
    
    def localization_agent(self, state: ContentCreationState) -> ContentCreationState:
        """Ensure proper language and cultural adaptation"""
        if state["target_language"] == "hu":
            localization_prompt = f"""
            Review and improve this Hungarian content for cultural appropriateness and language quality:
            
            {state['draft_content']}
            
            Ensure:
            - Natural Hungarian expressions
            - Proper music industry terminology
            - Cultural context for Hungarian market
            - Correct currency formatting (HUF)
            """
            
            response = self.llm.invoke([
                SystemMessage(content="You are a Hungarian localization expert for music industry content."),
                HumanMessage(content=localization_prompt)
            ])
            
            state["draft_content"] = response.content
        
        state["current_step"] = "localization_agent"
        return state
    
    def quality_agent(self, state: ContentCreationState) -> ContentCreationState:
        """Final quality check and formatting"""
        quality_prompt = f"""
        Perform final quality check on this content:
        
        {state['draft_content']}
        
        Check for:
        - Grammar and spelling
        - Brand consistency
        - Call-to-action clarity
        - Professional formatting
        - Factual accuracy
        
        Provide the final polished version.
        """
        
        response = self.llm.invoke([
            SystemMessage(content="You are a quality assurance specialist for marketing content."),
            HumanMessage(content=quality_prompt)
        ])
        
        state["final_content"] = response.content
        state["quality_score"] = 0.9  # Placeholder scoring
        state["current_step"] = "quality_agent"
        
        return state
    
    def _get_article_prompt(self, strategy: Dict, product_data: Dict, language: str) -> str:
        """Generate prompt for product article"""
        lang_instruction = "Write in Hungarian" if language == "hu" else "Write in English"
        
        return f"""
        {lang_instruction}. Create a professional product article based on:
        
        Strategy: {json.dumps(strategy, indent=2)}
        Product Data: {json.dumps(product_data, indent=2)}
        
        Create a 800-1200 word article that:
        - Has an engaging headline
        - Includes product specifications and benefits
        - Uses sales data to show popularity
        - Compares with competitors when relevant
        - Includes a strong call-to-action
        - Maintains professional tone
        """
    
    def _get_facebook_prompt(self, strategy: Dict, product_data: Dict, language: str) -> str:
        """Generate prompt for Facebook post"""
        lang_instruction = "Write in Hungarian" if language == "hu" else "Write in English"
        
        return f"""
        {lang_instruction}. Create an engaging Facebook post based on:
        
        Strategy: {json.dumps(strategy, indent=2)}
        Product Data: {json.dumps(product_data, indent=2)}
        
        Create a Facebook post that:
        - Is 100-200 words maximum
        - Uses engaging, conversational tone
        - Includes relevant hashtags
        - Has a clear call-to-action
        - Mentions key product benefits
        - Encourages engagement (likes, comments, shares)
        """
    
    def _get_linkedin_prompt(self, strategy: Dict, product_data: Dict, language: str) -> str:
        """Generate prompt for LinkedIn post"""
        lang_instruction = "Write in Hungarian" if language == "hu" else "Write in English"
        
        return f"""
        {lang_instruction}. Create a professional LinkedIn post based on:
        
        Strategy: {json.dumps(strategy, indent=2)}
        Product Data: {json.dumps(product_data, indent=2)}
        
        Create a LinkedIn post that:
        - Is 150-300 words
        - Uses professional, industry-focused tone
        - Includes business benefits and ROI
        - Targets B2B audience (studios, venues, professionals)
        - Has professional hashtags
        - Includes call-to-action for business inquiries
        """
    
    def _get_case_study_prompt(self, strategy: Dict, product_data: Dict, language: str) -> str:
        """Generate prompt for case study"""
        lang_instruction = "Write in Hungarian" if language == "hu" else "Write in English"
        
        return f"""
        {lang_instruction}. Create a professional case study based on:
        
        Strategy: {json.dumps(strategy, indent=2)}
        Product Data: {json.dumps(product_data, indent=2)}
        
        Create a case study that:
        - Has clear problem/solution structure
        - Includes technical specifications
        - Shows measurable results
        - Features customer testimonials (create realistic ones)
        - Demonstrates professional installation process
        - Includes before/after scenarios
        - Maintains technical credibility
        """
    
    def create_content(self, user_request: str) -> Dict[str, Any]:
        """Main entry point for content creation"""
        initial_state = ContentCreationState(
            user_request=user_request,
            content_type="product_article",
            target_language="en",
            product_data={},
            market_research={},
            competitor_data={},
            content_strategy={},
            target_audience="",
            key_messages=[],
            draft_content="",
            final_content="",
            metadata={},
            current_step="",
            errors=[],
            quality_score=0.0
        )
        
        try:
            # Run the workflow
            result = self.workflow.invoke(initial_state)
            
            return {
                "success": True,
                "content": result["final_content"],
                "content_type": result["content_type"],
                "language": result["target_language"],
                "strategy": result.get("content_strategy", {}),
                "quality_score": result.get("quality_score", 0.0),
                "metadata": {
                    "created_at": datetime.now().isoformat(),
                    "workflow_steps": result["current_step"],
                    "errors": result.get("errors", [])
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "metadata": {"created_at": datetime.now().isoformat()}
            }


# Example usage
if __name__ == "__main__":
    creator = ContentCreationAgents()
    
    # Test with a product article request
    result = creator.create_content("Create a product spotlight for Shure SM58 microphones")
    print(json.dumps(result, indent=2))