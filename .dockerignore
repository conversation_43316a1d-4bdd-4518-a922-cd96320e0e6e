# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environment
venv/
ENV/

# IDE-specific files
.idea/
.vscode/
*.swp
*.swo

# Streamlit cache
.streamlit/secrets.toml

# Local development
.env
.env.local
*.log

# Data and assets that can be generated
assets/placeholder_image.svg

# Documentation
docs/
README.md

# Large data files
*.csv
*.xlsx
*.zip
attached_assets/