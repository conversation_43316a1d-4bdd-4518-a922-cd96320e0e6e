services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      # Default environment is set to Replit for local testing
      - REPL_ID=local-docker-development
      # Add other environment variables as needed
    # volumes:
      # Uncomment for development to enable hot reloading
      # - .:/app
    restart: unless-stopped
    networks:
      - app-network
    depends_on:
      - db

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=erp_analytics
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data: