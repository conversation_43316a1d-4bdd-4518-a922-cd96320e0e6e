import streamlit as st
import pandas as pd
import datetime
from database import get_session, close_session
from models import Buyer, Product, ProductCategory, Price, Stock, Sale, Purchase, CompetitorScrape
from components.data_loader import upload_file
from utils import parse_excel_data, save_buyers_data, save_products_data, save_product_categories_data
from utils import save_prices_data, save_stock_data, save_sales_data, save_purchases_data, save_competitor_scrape_data
from sqlalchemy import text

def show_browse_data():
    """Display the browse data functionality."""
    st.subheader("Browse Uploaded Data")
    st.info("View and search through your uploaded data. Select a table to explore its contents.")
    
    # Table selection
    table_options = {
        "buyers": "Buyers",
        "products": "Products", 
        "product_categories": "Product Categories",
        "prices": "Prices",
        "stock": "Stock",
        "sales": "Sales",
        "purchases": "Purchases",
        "competitor_scrape": "Competitor Scrape"
    }
    
    selected_table = st.selectbox(
        "Select Table to Browse",
        options=list(table_options.keys()),
        format_func=lambda x: table_options[x]
    )
    
    if selected_table:
        display_table_data(selected_table)

def display_table_data(table_name):
    """Display data from the selected table with pagination and search."""
    session = get_session()
    if not session:
        st.error("Failed to get database session")
        return
    
    try:
        # Get the model class for the selected table
        model_map = {
            "buyers": Buyer,
            "products": Product,
            "product_categories": ProductCategory,
            "prices": Price,
            "stock": Stock,
            "sales": Sale,
            "purchases": Purchase,
            "competitor_scrape": CompetitorScrape
        }
        
        model = model_map.get(table_name)
        if not model:
            st.error(f"Unknown table: {table_name}")
            return
        
        # Get total record count
        total_records = session.query(model).count()
        
        if total_records == 0:
            st.warning(f"No data found in {table_name} table.")
            return
        
        st.write(f"**Total Records:** {total_records:,}")
        
        # Search and filter section
        with st.expander("Search and Filter", expanded=False):
            search_term = st.text_input("Search all columns", key=f"search_{table_name}")
            
            # Get column names for filtering
            columns = [col.name for col in model.__table__.columns]
            
            col1, col2 = st.columns(2)
            with col1:
                filter_column = st.selectbox("Filter by column", ["None"] + columns, key=f"filter_col_{table_name}")
            with col2:
                filter_value = st.text_input("Filter value", key=f"filter_val_{table_name}") if filter_column != "None" else ""
        
        # Apply search and filters to query
        query = session.query(model)
        
        # Apply search filter
        if search_term:
            search_conditions = []
            for column in model.__table__.columns:
                if column.type.python_type in [str]:  # Only search text columns
                    search_conditions.append(getattr(model, column.name).ilike(f"%{search_term}%"))
            if search_conditions:
                from sqlalchemy import or_
                query = query.filter(or_(*search_conditions))
        
        # Apply column filter
        if filter_column != "None" and filter_value:
            column_attr = getattr(model, filter_column)
            query = query.filter(column_attr.ilike(f"%{filter_value}%"))
        
        # Get filtered record count
        filtered_records = query.count()
        if search_term or (filter_column != "None" and filter_value):
            st.write(f"**Filtered Records:** {filtered_records:,}")
        
        # Update pagination for filtered results
        total_records = filtered_records
        
        # Pagination settings
        page_size_options = [25, 50, 100, 200]
        page_size = st.selectbox("Records per page", page_size_options, index=1)
        
        total_pages = (total_records + page_size - 1) // page_size
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            page_number = st.number_input(
                f"Page (1 to {total_pages})",
                min_value=1,
                max_value=total_pages,
                value=1,
                step=1
            )
        
        # Calculate offset
        offset = (page_number - 1) * page_size
        
        # Apply pagination to the filtered query
        paginated_query = query.offset(offset).limit(page_size)
        results = paginated_query.all()
        
        if results:
            # Convert to DataFrame for display
            data_list = []
            for result in results:
                # Convert SQLAlchemy object to dictionary
                row_dict = {}
                for column in model.__table__.columns:
                    value = getattr(result, column.name)
                    # Handle datetime and date objects
                    if hasattr(value, 'isoformat'):
                        value = value.isoformat()
                    row_dict[column.name] = value
                data_list.append(row_dict)
            
            df = pd.DataFrame(data_list)
            
            # Edit mode toggle
            edit_mode = st.checkbox("Enable Editing Mode", key=f"edit_mode_{table_name}")
            
            if edit_mode:
                st.warning("⚠️ You are in editing mode. Changes will be saved to the database when you click 'Save Changes'.")
                
                # Use data_editor for editing
                edited_df = st.data_editor(
                    df,
                    use_container_width=True,
                    height=min(len(df) * 35 + 100, 600),
                    key=f"editor_{table_name}_{page_number}"
                )
                
                # Check for changes
                if not df.equals(edited_df):
                    st.info("Data has been modified. Click 'Save Changes' to update the database.")
                    
                    if st.button("Save Changes", type="primary", key=f"save_{table_name}_{page_number}"):
                        save_changes_to_database(model, df, edited_df, session, table_name)
                        st.rerun()
                
                if st.button("Reset Changes", key=f"reset_{table_name}_{page_number}"):
                    st.rerun()
            else:
                # Display read-only data
                st.dataframe(
                    df,
                    use_container_width=True,
                    height=min(len(df) * 35 + 100, 600)
                )
            
            # Pagination info
            start_record = offset + 1
            end_record = min(offset + page_size, total_records)
            st.caption(f"Showing records {start_record:,} to {end_record:,} of {total_records:,}")
            
            # Export option
            col_export1, col_export2 = st.columns(2)
            with col_export1:
                if st.button("Export as CSV"):
                    csv = df.to_csv(index=False)
                    st.download_button(
                        label="Download CSV",
                        data=csv,
                        file_name=f"{table_name}_page_{page_number}.csv",
                        mime="text/csv"
                    )
            with col_export2:
                if st.button("Export as Excel"):
                    try:
                        from io import BytesIO
                        output = BytesIO()
                        # Use with statement for proper handling
                        with pd.ExcelWriter(output, engine='openpyxl') as writer:
                            df.to_excel(writer, index=False, sheet_name=table_name.title())
                        
                        st.download_button(
                            label="Download Excel",
                            data=output.getvalue(),
                            file_name=f"{table_name}_page_{page_number}.xlsx",
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        )
                    except Exception as e:
                        st.error(f"Excel export failed: {str(e)}. Please use CSV export instead.")
        else:
            st.warning("No data found for the current page.")
            
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
    finally:
        close_session(session)

def save_changes_to_database(model, original_df, edited_df, session, table_name):
    """Save changes from the edited dataframe back to the database."""
    try:
        # Get the primary key column name
        primary_key_col = None
        for column in model.__table__.columns:
            if column.primary_key:
                primary_key_col = column.name
                break
        
        if not primary_key_col:
            st.error("No primary key found for this table. Cannot save changes.")
            return
        
        changes_made = 0
        errors = []
        
        # Compare dataframes to find changes
        for idx in edited_df.index:
            original_row = original_df.iloc[idx]
            edited_row = edited_df.iloc[idx]
            
            # Check if row has changed
            if not original_row.equals(edited_row):
                primary_key_value = edited_row[primary_key_col]
                
                # Find the database record to update
                record = session.query(model).filter(
                    getattr(model, primary_key_col) == primary_key_value
                ).first()
                
                if record:
                    # Update each changed column
                    for column_name in edited_df.columns:
                        if original_row[column_name] != edited_row[column_name]:
                            # Handle data type conversion
                            new_value = edited_row[column_name]
                            
                            # Get the column type from the model
                            column_obj = getattr(model.__table__.columns, column_name, None)
                            if column_obj is not None:
                                # Convert string dates back to date objects if needed
                                if hasattr(column_obj.type, 'python_type'):
                                    if column_obj.type.python_type == datetime.date:
                                        if isinstance(new_value, str):
                                            try:
                                                new_value = datetime.datetime.fromisoformat(new_value).date()
                                            except:
                                                errors.append(f"Invalid date format for {column_name}: {new_value}")
                                                continue
                                    elif column_obj.type.python_type == datetime.datetime:
                                        if isinstance(new_value, str):
                                            try:
                                                new_value = datetime.datetime.fromisoformat(new_value)
                                            except:
                                                errors.append(f"Invalid datetime format for {column_name}: {new_value}")
                                                continue
                            
                            setattr(record, column_name, new_value)
                    
                    changes_made += 1
                else:
                    errors.append(f"Record with {primary_key_col}={primary_key_value} not found")
        
        if changes_made > 0:
            session.commit()
            st.success(f"Successfully saved {changes_made} changes to {table_name} table.")
        
        if errors:
            st.error("Some changes could not be saved:")
            for error in errors:
                st.write(f"- {error}")
                
    except Exception as e:
        session.rollback()
        st.error(f"Error saving changes: {str(e)}")
        
    finally:
        pass  # Session will be closed by the calling function

def show():
    """Display the upload page for importing Excel data."""
    st.header("Data Upload")
    
    # Create tabs for different upload options
    tab1, tab2, tab3 = st.tabs(["Upload Excel Files", "Data Status", "Browse Data"])
    
    with tab1:
        st.subheader("Upload Excel Files")
        st.info("Upload your Excel files to import data into the system. Select the file type and use the flexible column mapping to control how data is imported.")
        
        # File type selection
        file_type_options = {
            "buyers": "Buyers (Vevők.xlsx)",
            "products": "Products (Termékek.xlsx)",
            "product_categories": "Product Categories (Webes termékcsoportok.xlsx)",
            "prices": "Prices (Termék korábbi árak és készlet.xlsx)",
            "stock": "Stock (Termék készletérték (FIFO szerint).xlsx)",
            "sales": "Sales (ELÁBÉ+.xlsx)",
            "purchases": "Purchases (Bejövő számlák tételes listája.xlsx)",
            "competitor_scrape": "Competitor Scrape (Competitor pricing data)"
        }
        
        selected_option = st.selectbox(
            "Select Data Type",
            options=list(file_type_options.values())
        )
        
        # Find the key for the selected option
        file_type_key = next(key for key, value in file_type_options.items() if value == selected_option)
        
        # Use our enhanced upload_file component with flexible column selection
        upload_file(file_type_key)
                
    with tab2:
        st.subheader("Current Data Status")
        
        # Get database session
        session = get_session()
        if session:
            try:
                # Check record counts in each table
                buyer_count = session.query(Buyer).count()
                product_count = session.query(Product).count()
                category_count = session.query(ProductCategory).count()
                price_count = session.query(Price).count()
                stock_count = session.query(Stock).count()
                sale_count = session.query(Sale).count()
                purchase_count = session.query(Purchase).count()
                competitor_count = session.query(CompetitorScrape).count()
                
                # Display counts in a table
                data = {
                    "Table": ["Buyers", "Products", "Product Categories", "Prices", "Stock", "Sales", "Purchases", "Competitor Scrape"],
                    "Record Count": [buyer_count, product_count, category_count, price_count, stock_count, sale_count, purchase_count, competitor_count]
                }
                df = pd.DataFrame(data)
                
                st.table(df)
                
                # Display data import status
                st.subheader("Data Import Status")
                
                if all([buyer_count, product_count, sale_count, purchase_count]) > 0:
                    st.success("Your data has been successfully imported. You can view analytics in the respective sections.")
                elif all([buyer_count, product_count, sale_count, purchase_count]) == 0:
                    st.warning("No data has been imported yet. Please upload Excel files to begin.")
                else:
                    missing_data = []
                    if buyer_count == 0:
                        missing_data.append("Buyers")
                    if product_count == 0:
                        missing_data.append("Products")
                    if sale_count == 0:
                        missing_data.append("Sales")
                    if purchase_count == 0:
                        missing_data.append("Purchases")
                    
                    st.warning(f"Partial data imported. Missing data for: {', '.join(missing_data)}.")
                
            except Exception as e:
                st.error(f"Error checking data status: {str(e)}")
            finally:
                close_session(session)
        else:
            st.error("Failed to get database session")
    
    with tab3:
        show_browse_data()